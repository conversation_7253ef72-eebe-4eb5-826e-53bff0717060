{"cells": [{"cell_type": "code", "execution_count": 5, "id": "f6a1df01-481c-4397-9c97-a6b4bf2daef6", "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "could not broadcast input array from shape (8,) into shape (6,)", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[5], line 23\u001b[0m\n\u001b[0;32m     20\u001b[0m df \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mDataFrame(index\u001b[38;5;241m=\u001b[39mrow_index, columns\u001b[38;5;241m=\u001b[39mcolumn_index, dtype\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mobject\u001b[39m)\n\u001b[0;32m     22\u001b[0m \u001b[38;5;66;03m# 填充示例数据\u001b[39;00m\n\u001b[1;32m---> 23\u001b[0m \u001b[43mdf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mloc\u001b[49m\u001b[43m[\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m<18\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m男\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m]\u001b[49m \u001b[38;5;241m=\u001b[39m [\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m21\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m-\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m3\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m32\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m-\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m66\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m33\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m43\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[0;32m     24\u001b[0m df\u001b[38;5;241m.\u001b[39mloc[(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m<18\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m女\u001b[39m\u001b[38;5;124m'\u001b[39m)] \u001b[38;5;241m=\u001b[39m [\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m-\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m-\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m-\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m-\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m-\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m-\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m-\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m-\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[0;32m     25\u001b[0m df\u001b[38;5;241m.\u001b[39mloc[(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m>=18 & <60\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m男\u001b[39m\u001b[38;5;124m'\u001b[39m)] \u001b[38;5;241m=\u001b[39m [\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m34\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124ma\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m3\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mN\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mM\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m77\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m3\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m43\u001b[39m\u001b[38;5;124m'\u001b[39m]\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pandas\\core\\indexing.py:849\u001b[0m, in \u001b[0;36m_LocationIndexer.__setitem__\u001b[1;34m(self, key, value)\u001b[0m\n\u001b[0;32m    846\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_has_valid_setitem_indexer(key)\n\u001b[0;32m    848\u001b[0m iloc \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mname \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124miloc\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj\u001b[38;5;241m.\u001b[39miloc\n\u001b[1;32m--> 849\u001b[0m \u001b[43miloc\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_setitem_with_indexer\u001b[49m\u001b[43m(\u001b[49m\u001b[43mindexer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvalue\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pandas\\core\\indexing.py:1837\u001b[0m, in \u001b[0;36m_iLocIndexer._setitem_with_indexer\u001b[1;34m(self, indexer, value, name)\u001b[0m\n\u001b[0;32m   1835\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_setitem_with_indexer_split_path(indexer, value, name)\n\u001b[0;32m   1836\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m-> 1837\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_setitem_single_block\u001b[49m\u001b[43m(\u001b[49m\u001b[43mindexer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvalue\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pandas\\core\\indexing.py:2077\u001b[0m, in \u001b[0;36m_iLocIndexer._setitem_single_block\u001b[1;34m(self, indexer, value, name)\u001b[0m\n\u001b[0;32m   2074\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj\u001b[38;5;241m.\u001b[39m_check_is_chained_assignment_possible()\n\u001b[0;32m   2076\u001b[0m \u001b[38;5;66;03m# actually do the set\u001b[39;00m\n\u001b[1;32m-> 2077\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj\u001b[38;5;241m.\u001b[39m_mgr \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mobj\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_mgr\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msetitem\u001b[49m\u001b[43m(\u001b[49m\u001b[43mindexer\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mindexer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvalue\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mvalue\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   2078\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj\u001b[38;5;241m.\u001b[39m_maybe_update_cacher(clear\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m, inplace\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pandas\\core\\internals\\managers.py:394\u001b[0m, in \u001b[0;36mBaseBlockManager.setitem\u001b[1;34m(self, indexer, value)\u001b[0m\n\u001b[0;32m    389\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m using_copy_on_write() \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_has_no_reference(\u001b[38;5;241m0\u001b[39m):\n\u001b[0;32m    390\u001b[0m     \u001b[38;5;66;03m# if being referenced -> perform Copy-on-Write and clear the reference\u001b[39;00m\n\u001b[0;32m    391\u001b[0m     \u001b[38;5;66;03m# this method is only called if there is a single block -> hardcoded 0\u001b[39;00m\n\u001b[0;32m    392\u001b[0m     \u001b[38;5;28mself\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcopy()\n\u001b[1;32m--> 394\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mapply\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43msetitem\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mindexer\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mindexer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvalue\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mvalue\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pandas\\core\\internals\\managers.py:352\u001b[0m, in \u001b[0;36mBaseBlockManager.apply\u001b[1;34m(self, f, align_keys, **kwargs)\u001b[0m\n\u001b[0;32m    350\u001b[0m         applied \u001b[38;5;241m=\u001b[39m b\u001b[38;5;241m.\u001b[39mapply(f, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m    351\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m--> 352\u001b[0m         applied \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mgetattr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mb\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mf\u001b[49m\u001b[43m)\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    353\u001b[0m     result_blocks \u001b[38;5;241m=\u001b[39m extend_blocks(applied, result_blocks)\n\u001b[0;32m    355\u001b[0m out \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mtype\u001b[39m(\u001b[38;5;28mself\u001b[39m)\u001b[38;5;241m.\u001b[39mfrom_blocks(result_blocks, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maxes)\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py:1067\u001b[0m, in \u001b[0;36mBlock.setitem\u001b[1;34m(self, indexer, value, using_cow)\u001b[0m\n\u001b[0;32m   1064\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(casted, np\u001b[38;5;241m.\u001b[39mndarray) \u001b[38;5;129;01mand\u001b[39;00m casted\u001b[38;5;241m.\u001b[39mndim \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m1\u001b[39m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(casted) \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m   1065\u001b[0m         \u001b[38;5;66;03m# NumPy 1.25 deprecation: https://github.com/numpy/numpy/pull/10615\u001b[39;00m\n\u001b[0;32m   1066\u001b[0m         casted \u001b[38;5;241m=\u001b[39m casted[\u001b[38;5;241m0\u001b[39m, \u001b[38;5;241m.\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;241m.\u001b[39m]\n\u001b[1;32m-> 1067\u001b[0m     \u001b[43mvalues\u001b[49m\u001b[43m[\u001b[49m\u001b[43mindexer\u001b[49m\u001b[43m]\u001b[49m \u001b[38;5;241m=\u001b[39m casted\n\u001b[0;32m   1068\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\n", "\u001b[1;31mValueError\u001b[0m: could not broadcast input array from shape (8,) into shape (6,)"]}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import matplotlib\n", "from IPython.display import Image as IPImage, display\n", "\n", "# 设置中文字体（可根据系统实际字体调整）\n", "matplotlib.rcParams['font.sans-serif'] = ['SimHei']\n", "matplotlib.rcParams['axes.unicode_minus'] = False\n", "# 定义行索引：年龄组和性别\n", "age_groups = ['<18', '>=18 & <60', '>=60 & <80', '>=80']\n", "genders = ['男', '女']\n", "row_index = pd.MultiIndex.from_product([age_groups, genders], names=['age_group', 'gender'])\n", "\n", "# 定义列索引：主类别和子类别\n", "main_categories = ['已婚', '无房', '有房']\n", "subcategories = ['已婚', '未婚']\n", "column_index = pd.MultiIndex.from_product([main_categories, subcategories], names=['category', 'subcategory'])\n", "\n", "# 创建DataFrame，使用object类型以支持混合数据\n", "df = pd.DataFrame(index=row_index, columns=column_index, dtype=object)\n", "\n", "# 填充示例数据\n", "df.loc[('<18', '男')] = ['21', '-', '3', '32', '-', '66', '33', '43']\n", "df.loc[('<18', '女')] = ['-', '-', '-', '-', '-', '-', '-', '-']\n", "df.loc[('>=18 & <60', '男')] = ['34', 'a', '3', 'N', 'M', '77', '3', '43']\n", "df.loc[('>=18 & <60', '女')] = ['53', 'c', '5', 'L', 'i', '55', '-', '76']\n", "df.loc[('>=60 & <80', '男')] = ['99', 'h', '22', 'K', '-', '34', '54', '23']\n", "df.loc[('>=60 & <80', '女')] = ['-', '-', '-', '-', '-', '2', '-', '-']\n", "df.loc[('>=80', '男')] = ['23', '23', '-', '-', '-', '-', '-', '-']\n", "df.loc[('>=80', '女')] = ['2', '11', '-', '-', '-', '-', '-', '-']\n", "\n", "# 创建图表\n", "fig, ax = plt.subplots(figsize=(12, 6))\n", "ax.axis('off')\n", "\n", "# 创建表格\n", "table = ax.table(cellText=df.values, rowLabels=[f'{age} {gen}' for age, gen in df.index], colLabels=[f'{cat} {sub}' for cat, sub in df.columns], loc='center', cellLoc='center')\n", "\n", "# 模拟合并单元格（行标签中的年龄组）\n", "merged_row_labels = []\n", "current_age = None\n", "for age, gen in df.index:\n", "    if age != current_age:\n", "        merged_row_labels.append(age)\n", "        current_age = age\n", "    else:\n", "        merged_row_labels.append('')\n", "\n", "# 设置合并后的行标签\n", "for i, label in enumerate(merged_row_labels):\n", "    if label:\n", "        table[(i + 1, 0)].get_text().set_text(label)\n", "        table[(i + 1, 0)].set_height(2)\n", "    else:\n", "        table[(i + 1, 0)].set_visible(False)\n", "\n", "# 调整表格样式\n", "table.auto_set_font_size(False)\n", "table.set_fontsize(10)\n", "table.scale(1, 1.5)\n", "\n", "# 保存为JPG\n", "plt.savefig('cross_table.jpg', bbox_inches='tight', dpi=300)\n", "plt.close()\n", "\n", "print(\"交叉表已保存为 'cross_table.jpg'\")"]}, {"cell_type": "code", "execution_count": null, "id": "94a0eec4-3d0b-4d91-a8e0-7e87ecf056d7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}