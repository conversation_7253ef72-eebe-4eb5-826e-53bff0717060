hospital_registration = {
    "display_name": "医院挂号",
    "table_title_template": "医院挂号信息表",
    "columns": {
        "挂号单号": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "REG########",
                    "GH########",
                    "HR########",
                    "H########"
                ]
            },
            "data_category": "text"
        },
        "患者ID": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "PID####",
                    "P######",
                    "PT########",
                    "PAT######"
                ]
            },
            "data_category": "text"
        },
        "患者姓名": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "性别": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "男",
                    "女"
                ]
            },
            "data_category": "text"
        },
        "年龄": {
            "generator_type": "integer_range",
            "params": {
                "min": 0,
                "max": 100
            },
            "data_category": "numeric"
        },
        "联系电话": {
            "generator_type": "faker_phone_number",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "证件号码": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "####################"
                ]
            },
            "data_category": "text"
        },
        "医院名称": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "北京协和医院",
                    "北京大学第一医院",
                    "北京大学人民医院",
                    "复旦大学附属华东医院",
                    "上海交通大学医学院附属瑞金医院",
                    "上海华东医院",
                    "中南大学湘雅医院",
                    "南京军区总医院",
                    "中山大学附属第一医院",
                    "广州军区总医院",
                    "武汉协和医院",
                    "重庆医科大学附属医院",
                    "西京医院",
                    "四川大学华西医院",
                    "天津医科大学总医院",
                    "中国医学科学院肿瘤医院",
                    "浙江大学医学院附属第一医院",
                    "山东大学附属医院",
                    "上海市第一人民医院",
                    "上海市第六人民医院",
                    "华中科技大学同济医院",
                    "广州医科大学附属医院",
                    "陕西省人民医院",
                    "四川省人民医院",
                    "山东省立医院",
                    "重庆三峡医院",
                    "南京市鼓楼医院",
                    "长春市第一医院",
                    "沈阳军区总医院",
                    "哈尔滨医科大学附属第一医院",
                    "郑州大学第一附属医院",
                    "安徽省立医院",
                    "北京友谊医院",
                    "北京同仁医院",
                    "上海华东医院",
                    "江西省人民医院",
                    "广西医科大学附属医院",
                    "湖北省人民医院",
                    "福建省立医院",
                    "辽宁省人民医院",
                    "江苏省人民医院",
                    "陕西省人民医院",
                    "山西省人民医院",
                    "河北医科大学附属医院",
                    "内蒙古医科大学附属医院",
                    "云南省人民医院",
                    "甘肃省人民医院",
                    "吉林大学第一医院",
                    "湖南省人民医院",
                    "陕西省肿瘤医院",
                    "黑龙江省医院",
                    "南京中医药大学附属医院",
                    "北京中医医院",
                    "上海中医药大学附属医院",
                    "广东省中医院",
                    "浙江中医药大学附属医院",
                    "福建中医药大学附属医院",
                    "重庆中医药大学附属医院",
                    "北京安贞医院",
                    "上海市第九人民医院",
                    "武汉同济医院",
                    "广东省人民医院",
                    "吉林省人民医院",
                    "内蒙古自治区医院",
                    "四川省人民医院",
                    "青岛市市立医院",
                    "浙江省人民医院",
                    "北京儿童医院",
                    "上海儿童医学中心",
                    "南京儿童医院",
                    "广州儿童医院",
                    "成都中医药大学附属医院",
                    "天津中医药大学附属医院",
                    "广西医科大学附属医院",
                    "宁波市第一医院",
                    "杭州市第一人民医院",
                    "南京市鼓楼医院",
                    "温州市人民医院",
                    "常州市第一人民医院",
                    "唐山市中心医院",
                    "邯郸市中心医院",
                    "长春市中心医院",
                    "沈阳市中医院"
                ]
            },
            "data_category": "text"
        },
        "科室": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "内科",
                    "外科",
                    "儿科",
                    "妇产科",
                    "眼科",
                    "耳鼻喉科",
                    "口腔科",
                    "皮肤科",
                    "神经科",
                    "精神科",
                    "肿瘤科",
                    "中医科",
                    "急诊科",
                    "康复科",
                    "检验科",
                    "放射科",
                    "超声科",
                    "心血管内科",
                    "消化内科",
                    "呼吸内科",
                    "神经内科",
                    "内分泌科",
                    "血液科",
                    "风湿免疫科",
                    "普外科",
                    "骨科",
                    "泌尿外科",
                    "心胸外科",
                    "神经外科",
                    "整形外科"
                ]
            },
            "data_category": "text"
        },
        "医生": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "挂号类型": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "普通门诊",
                    "专家门诊",
                    "特需门诊",
                    "急诊",
                    "专科门诊",
                    "预约门诊",
                    "复诊",
                    "会诊",
                    "远程会诊",
                    "义诊",
                    "专病门诊",
                    "夜间门诊"
                ]
            },
            "data_category": "text"
        },
        "挂号费用": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 5,
                "max_value": 500,
                "decimals": 2,
                "format_string": "{:.2f}"
            },
            "data_category": "numeric"
        },
        "挂号日期": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2023,
                "end_year": 2023,
                "format": "%Y-%m-%d"
            },
            "data_category": "date"
        },
        "就诊时间段": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "上午8:00-9:00",
                    "上午9:00-10:00",
                    "上午10:00-11:00",
                    "上午11:00-12:00",
                    "下午1:00-2:00",
                    "下午2:00-3:00",
                    "下午3:00-4:00",
                    "下午4:00-5:00",
                    "晚上6:00-7:00",
                    "晚上7:00-8:00",
                    "晚上8:00-9:00"
                ]
            },
            "data_category": "text"
        },
        "挂号状态": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "已预约",
                    "已挂号",
                    "待就诊",
                    "就诊中",
                    "已就诊",
                    "已取消",
                    "爽约",
                    "退号",
                    "转诊",
                    "改期"
                ]
            },
            "data_category": "text"
        },
        "挂号渠道": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "现场挂号",
                    "电话预约",
                    "网上预约",
                    "APP预约",
                    "自助机",
                    "社区转诊",
                    "家庭医生",
                    "第三方平台",
                    "医保平台",
                    "医院微信公众号"
                ]
            },
            "data_category": "text"
        },
        "支付方式": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "现金",
                    "医保卡",
                    "银行卡",
                    "微信支付",
                    "支付宝",
                    "医院储值卡",
                    "第三方支付"
                ]
            },
            "data_category": "text"
        },
        "是否初诊": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "是",
                    "否"
                ]
            },
            "data_category": "text"
        },
        "病历号": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "MR########",
                    "BL########",
                    "CHART######"
                ]
            },
            "data_category": "text"
        }
    },
    "text_columns_count": 10,
    "text_columns_names": [
        "患者姓名",
        "性别",
        "医院名称",
        "科室",
        "医生",
        "挂号类型",
        "挂号状态",
        "挂号渠道",
        "支付方式",
        "是否初诊"
    ],
    "numeric_columns_count": 6,
    "numeric_columns_names": [
        "挂号单号",
        "患者ID",
        "联系电话",
        "证件号码",
        "挂号费用",
        "病历号"
    ],
    "date_columns_count": 3,
    "date_columns_names": [
        "年龄",
        "挂号日期",
        "就诊时间段"
    ],
    "other_columns_count": 0,
    "other_columns_names": [],
    "all_columns": [
        "患者姓名",
        "性别",
        "医院名称",
        "科室",
        "医生",
        "挂号类型",
        "挂号状态",
        "挂号渠道",
        "支付方式",
        "是否初诊",
        "挂号单号",
        "患者ID",
        "联系电话",
        "证件号码",
        "挂号费用",
        "病历号",
        "年龄",
        "挂号日期",
        "就诊时间段"
    ]
}

