food = {
    "display_name": "餐饮美食",
    "table_title_template": "餐饮数据统计表",
    "columns": {
        "餐厅名称": {
            "generator_type": "categorical_with_pattern",
            "data_category": "text",
            "params": {
                "prefixes": [
                    "金牌",
                    "老字号",
                    "顶级",
                    "传统",
                    "经典",
                    "米其林",
                    "皇家",
                    "御膳",
                    "家常",
                    "特色",
                    "正宗",
                    "地道"
                ],
                "suffixes": [
                    "小馆",
                    "酒楼",
                    "餐厅",
                    "饭店",
                    "大厨",
                    "食府",
                    "烧烤",
                    "火锅",
                    "川菜",
                    "粤菜",
                    "湘菜",
                    "鲁菜",
                    "徽菜",
                    "闽菜",
                    "浙菜",
                    "苏菜"
                ]
            }
        },
        "菜系": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "川菜",
                    "粤菜",
                    "湘菜",
                    "鲁菜",
                    "徽菜",
                    "闽菜",
                    "浙菜",
                    "苏菜",
                    "京菜",
                    "沪菜",
                    "东北菜",
                    "西北菜",
                    "云南菜",
                    "贵州菜",
                    "客家菜",
                    "潮汕菜",
                    "江西菜",
                    "上海菜",
                    "台湾菜",
                    "西餐"
                ]
            }
        },
        "特色菜品": {
            "generator_type": "categorical_with_pattern",
            "data_category": "text",
            "params": {
                "prefixes": [
                    "红烧",
                    "清蒸",
                    "酱爆",
                    "香煎",
                    "糖醋",
                    "麻辣",
                    "干煸",
                    "水煮",
                    "爆炒",
                    "红焖"
                ],
                "suffixes": [
                    "鱼",
                    "肉",
                    "牛排",
                    "排骨",
                    "虾",
                    "鸡",
                    "鸭",
                    "豆腐",
                    "茄子",
                    "青菜"
                ]
            }
        },
        "平均价格": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 30,
                "max_value": 500,
                "format_string": "{:d}"
            }
        },
        "口味评分": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 7.0,
                "max_value": 9.9,
                "decimals": 1,
                "format_string": "{:.1f}"
            }
        },
        "环境评分": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 7.0,
                "max_value": 9.9,
                "decimals": 1,
                "format_string": "{:.1f}"
            }
        },
        "服务评分": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 7.0,
                "max_value": 9.9,
                "decimals": 1,
                "format_string": "{:.1f}"
            }
        },
        "位置": {
            "generator_type": "faker_address",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "营业时间": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "10:00-22:00",
                    "11:00-21:00",
                    "11:30-22:30",
                    "10:30-22:30",
                    "11:00-14:00, 17:00-22:00",
                    "11:30-14:30, 17:30-22:30",
                    "全天营业",
                    "24小时营业",
                    "09:00-21:00",
                    "08:00-20:00"
                ]
            }
        },
        "菜品数量": {
            "generator_type": "integer_range",
            "data_category": "numeric",
            "params": {
                "min": 10,
                "max": 200
            }
        },
        "推荐指数": {
            "generator_type": "percentage",
            "data_category": "numeric",
            "params": {
                "min_value": 60,
                "max_value": 99,
                "decimals": 0
            }
        },
        "排队时间": {
            "generator_type": "integer_range_with_unit",
            "data_category": "numeric",
            "params": {
                "min": 0,
                "max": 120,
                "unit": "分钟"
            }
        },
        "人均消费": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 50,
                "max_value": 1000,
                "format_string": "{:d}"
            }
        },
        "上菜时间": {
            "generator_type": "integer_range_with_unit",
            "data_category": "numeric",
            "params": {
                "min": 5,
                "max": 30,
                "unit": "分钟"
            }
        },
        "备注": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "无",
                    "重要",
                    "紧急",
                    "待跟进",
                    "已完成",
                    "已取消"
                ]
            },
            "data_category": "text"
        }
    },
    "text_columns_count": 5,
    "text_columns_names": [
        "菜系",
        "位置",
        "备注",
        "餐厅名称",
        "特色菜品",
    ],
    "numeric_columns_count": 9,
    "numeric_columns_names": [
        "餐厅名称",
        "特色菜品",
        "平均价格",
        "口味评分",
        "环境评分",
        "服务评分",
        "菜品数量",
        "推荐指数",
        "人均消费"
    ],
    "date_columns_count": 3,
    "date_columns_names": [
        "营业时间",
        "排队时间",
        "上菜时间"
    ],
    "other_columns_count": 0,
    "other_columns_names": [],
    "all_columns": [
        "菜系",
        "位置",
        "备注",
        "餐厅名称",
        "特色菜品",
        "平均价格",
        "口味评分",
        "环境评分",
        "服务评分",
        "菜品数量",
        "推荐指数",
        "人均消费",
        "营业时间",
        "排队时间",
        "上菜时间"
    ]
}

