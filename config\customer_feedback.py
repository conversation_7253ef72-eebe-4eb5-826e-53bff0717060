customer_feedback = {
    "display_name": "客户反馈",
    "table_title_template": "客户反馈数据表",
    "columns": {
        "反馈编号": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "CF########",
                    "FEEDBACK-####"
                ]
            },
            "data_category": "numeric"
        },
        "用户ID": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "UID####",
                    "U######",
                    "USER_###"
                ]
            },
            "data_category": "other"
        },
        "用户名": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "反馈时间": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2023,
                "end_year": 2023,
                "format": "%Y-%m-%d %H:%M:%S"
            },
            "data_category": "date"
        },
        "反馈内容": {
            "generator_type": "categorical_with_pattern",
            "params": {
                "prefixes": [
                    "满意",
                    "不满意",
                    "一般",
                    "建议"
                ],
                "suffixes": [
                    "",
                    "",
                    "",
                    ""
                ]
            },
            "data_category": "text"
        },
        "回复内容": {
            "generator_type": "categorical_with_pattern",
            "params": {
                "prefixes": [
                    "已回复",
                    "未回复"
                ],
                "suffixes": [
                    "",
                    ""
                ]
            },
            "data_category": "text"
        },
        "满意度评分": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 1,
                "max_value": 5,
                "decimals": 1,
                "format_string": "{:.1f}"
            },
            "data_category": "numeric"
        },
        "备注": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "无",
                    "重要",
                    "紧急",
                    "待跟进",
                    "已完成",
                    "已取消"
                ]
            },
            "data_category": "text"
        },
        "状态": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "活跃",
                    "挂起",
                    "完成",
                    "取消",
                    "延期",
                    "进行中"
                ]
            },
            "data_category": "text"
        }
    },
    "text_columns_count": 5,
    "text_columns_names": [
        "用户名",
        "备注",
        "状态",
        "反馈内容",
        "回复内容",
    ],
    "numeric_columns_count": 5,
    "numeric_columns_names": [
        "反馈编号",
        "满意度评分",
        
    ],
    "date_columns_count": 1,
    "date_columns_names": [
        "反馈时间",
    ],
    "other_columns_count": 1,
    "other_columns_names": [
        "用户ID"
    ],
    "all_columns": [
        "用户名",
        "备注",
        "状态",
        "反馈编号",
        "用户ID",
        "反馈内容",
        "回复内容",
        "满意度评分",
        "反馈时间"
    ]
}

