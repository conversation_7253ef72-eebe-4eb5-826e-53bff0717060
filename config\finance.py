finance = {
    "display_name": "财务信息",
    "table_title_template": "财务交易信息表",
    "columns": {
        "交易编号": {
            "generator_type": "alphanumeric_pattern",
            "data_category": "text",
            "params": {
                "patterns": [
                    "TRX######",
                    "FIN-####-##",
                    "PAY######"
                ]
            }
        },
        "交易日期": {
            "generator_type": "date_range",
            "data_category": "date",
            "params": {
                "start_year": 2023,
                "end_year": 2024,
                "format": "%Y-%m-%d"
            }
        },
        "交易时间": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "09:15:30",
                    "10:45:22",
                    "12:30:15",
                    "14:20:45",
                    "16:05:12",
                    "17:30:00",
                    "19:45:36",
                    "21:10:25"
                ]
            }
        },
        "交易类型": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "收入",
                    "支出",
                    "转账",
                    "退款",
                    "预付款",
                    "投资",
                    "贷款",
                    "分期付款",
                    "报销"
                ]
            }
        },
        "交易金额": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 10,
                "max_value": 100000,
                "decimals": 2,
                "format_string": "{:,.2f}"
            }
        },
        "账户名称": {
            "generator_type": "faker_name",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "支付方式": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "支付宝",
                    "微信支付",
                    "银行卡",
                    "现金",
                    "信用卡",
                    "公司账户",
                    "PayPal",
                    "Apple Pay",
                    "虚拟货币"
                ]
            }
        },
        "交易状态": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "已完成",
                    "处理中",
                    "已取消",
                    "待处理",
                    "失败",
                    "部分完成",
                    "待确认",
                    "已退款"
                ]
            }
        },
        "交易描述": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "购买商品",
                    "服务费",
                    "薪资支付",
                    "租金",
                    "水电费",
                    "办公用品",
                    "市场推广",
                    "技术服务",
                    "餐饮费",
                    "差旅费",
                    "保险费",
                    "税费",
                    "培训费"
                ]
            }
        },
        "交易渠道": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "线上",
                    "线下",
                    "移动端",
                    "PC端",
                    "银行柜台",
                    "ATM",
                    "自助终端",
                    "电话银行",
                    "第三方平台"
                ]
            }
        },
        "收款方": {
            "generator_type": "faker_company",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "付款方": {
            "generator_type": "faker_company",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "交易区域": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "国内",
                    "国际",
                    "港澳台",
                    "欧美",
                    "亚太",
                    "非洲",
                    "北美",
                    "南美",
                    "线上"
                ]
            }
        },
        "税率": {
            "generator_type": "percentage",
            "data_category": "numeric",
            "params": {
                "min_value": 0,
                "max_value": 30,
                "decimals": 1
            }
        },
        "税额": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 0,
                "max_value": 10000,
                "decimals": 2,
                "format_string": "{:,.2f}"
            }
        },
        "币种": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "人民币",
                    "美元",
                    "欧元",
                    "英镑",
                    "日元",
                    "港币",
                    "澳元",
                    "加元",
                    "韩元"
                ]
            }
        },
        "汇率": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 0.001,
                "max_value": 10,
                "decimals": 4,
                "format_string": "{:.4f}"
            }
        },
        "业务类型": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "销售",
                    "采购",
                    "工资",
                    "费用报销",
                    "资本支出",
                    "贷款",
                    "投资",
                    "股东分红",
                    "税费"
                ]
            }
        },
        "经办人": {
            "generator_type": "faker_name",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "审批人": {
            "generator_type": "faker_name",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "财务机构": {
            "generator_type": "categorical_with_pattern",
            "data_category": "text",
            "params": {
                "prefixes": [
                    "工商银行",
                    "建设银行",
                    "农业银行",
                    "中国银行",
                    "招商银行",
                    "交通银行",
                    "浦发银行",
                    "民生银行",
                    "平安银行",
                    "兴业银行"
                ],
                "suffixes": [
                    "总行",
                    "分行",
                    "支行",
                    "营业部",
                    "网点",
                    "金融中心"
                ]
            }
        },
        "账单周期": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "日结",
                    "周结",
                    "月结",
                    "季度",
                    "半年",
                    "年度",
                    "不定期",
                    "T+1",
                    "T+3",
                    "实时"
                ]
            }
        },
        "结算方式": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "全额结算",
                    "分期结算",
                    "定金+尾款",
                    "货到付款",
                    "服务完成付款",
                    "预付全款",
                    "月底结算",
                    "账期结算",
                    "对公转账",
                    "第三方托管"
                ]
            }
        },
        "关联合同编号": {
            "generator_type": "alphanumeric_pattern",
            "data_category": "text",
            "params": {
                "patterns": [
                    "CTR-####-####",
                    "CON######",
                    "AG-###-####-##"
                ]
            }
        },
        "风险等级": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "低风险",
                    "中低风险",
                    "中等风险",
                    "中高风险",
                    "高风险",
                    "无风险",
                    "待评估",
                    "R1",
                    "R2",
                    "R3"
                ]
            }
        },
        "发票状态": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "已开票",
                    "未开票",
                    "部分开票",
                    "待开票",
                    "已作废",
                    "退票中",
                    "已退票",
                    "开票中",
                    "发票丢失",
                    "不需要开票"
                ]
            }
        },
        "资金来源": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "自有资金",
                    "银行贷款",
                    "投资人投入",
                    "融资租赁",
                    "众筹",
                    "政府补助",
                    "合作伙伴出资",
                    "内部调拨",
                    "集团拨款",
                    "其他"
                ]
            }
        },
        "财务审核结果": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "通过",
                    "驳回",
                    "修改后通过",
                    "保留意见",
                    "暂缓处理",
                    "需补充材料",
                    "上报审核",
                    "条件通过",
                    "特批通过",
                    "待审核"
                ]
            }
        },
        "资金用途": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "运营支出",
                    "固定资产",
                    "项目投资",
                    "人力成本",
                    "市场推广",
                    "研发投入",
                    "债务偿还",
                    "资金周转",
                    "储备金",
                    "股东分红"
                ]
            }
        },
        "预算归属": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "销售部门",
                    "市场部门",
                    "研发部门",
                    "行政部门",
                    "人力资源",
                    "财务部门",
                    "生产部门",
                    "采购部门",
                    "总经办",
                    "非预算项目"
                ]
            }
        },
        "年度交易总额": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 100000,
                "max_value": 10000000,
                "format_string": "{:,}"
            }
        },
        "清算日期": {
            "generator_type": "date_range",
            "data_category": "date",
            "params": {
                "start_year": 2023,
                "end_year": 2024,
                "format": "%Y-%m-%d"
            }
        }
    },
    "text_columns_count": 18,
    "text_columns_names": [
        "交易类型",
        "账户名称",
        "支付方式",
        "交易状态",
        "交易描述",
        "交易渠道",
        "收款方",
        "付款方",
        "交易区域",
        "业务类型",
        "经办人",
        "审批人",
        "结算方式",
        "发票状态",
        "资金来源",
        "财务审核结果",
        "资金用途",
        "预算归属"
    ],
    "numeric_columns_count": 9,
    "numeric_columns_names": [
        "交易编号",
        "交易金额",
        "税率",
        "税额",
        "汇率",
        "财务机构",
        "账单周期",
        "关联合同编号",
        "风险等级"
    ],
    "date_columns_count": 4,
    "date_columns_names": [
        "交易日期",
        "交易时间",
        "年度交易总额",
        "清算日期"
    ],
    "other_columns_count": 1,
    "other_columns_names": [
        "币种"
    ],
    "all_columns": [
        "交易类型",
        "账户名称",
        "支付方式",
        "交易状态",
        "交易描述",
        "交易渠道",
        "收款方",
        "付款方",
        "交易区域",
        "业务类型",
        "经办人",
        "审批人",
        "结算方式",
        "发票状态",
        "资金来源",
        "财务审核结果",
        "资金用途",
        "预算归属",
        "交易编号",
        "交易金额",
        "税率",
        "税额",
        "汇率",
        "财务机构",
        "账单周期",
        "关联合同编号",
        "风险等级",
        "交易日期",
        "交易时间",
        "年度交易总额",
        "清算日期",
        "币种"
    ]
}

