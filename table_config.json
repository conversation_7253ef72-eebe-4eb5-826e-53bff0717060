{"common_data": {"cities": ["北京", "上海", "广州", "深圳", "成都", "武汉", "杭州", "重庆", "西安", "南京", "天津", "苏州", "青岛", "郑州", "长沙", "合肥", "无锡", "宁波", "福州", "厦门", "南昌", "济南", "大连", "哈尔滨", "长春", "沈阳", "石家庄", "太原", "呼和浩特", "乌鲁木齐", "兰州", "西宁", "银川", "贵阳", "昆明", "南宁", "海口", "拉萨", "香港", "澳门", "东莞", "佛山", "惠州", "珠海", "中山", "汕头", "江门", "湛江", "茂名", "肇庆", "阳江", "清远", "韶关", "梅州", "揭阳", "汕尾", "潮州", "榆林", "宝鸡", "咸阳", "安阳", "洛阳", "焦作", "商丘", "驻马店", "南阳", "信阳", "新乡", "常州", "徐州", "连云港", "南通", "扬州", "镇江", "盐城", "泰州", "宿迁", "嘉兴", "绍兴", "金华", "台州", "丽水", "温州", "湖州", "衢州", "舟山", "上饶", "赣州", "九江", "宜春", "景德镇", "萍乡", "新余", "鹰潭", "株洲", "湘潭", "岳阳", "常德", "衡阳", "邵阳", "益阳", "娄底", "永州", "怀化", "郴州", "张家界", "湘西", "北京", "上海", "广州", "深圳", "成都", "武汉", "杭州", "重庆", "西安", "南京", "天津", "苏州", "青岛", "郑州", "长沙", "合肥", "无锡", "宁波", "福州", "厦门"], "major_cities": ["北京", "上海", "广州", "深圳", "成都", "重庆", "武汉", "杭州", "南京", "西安"]}, "themes": {"transportation": {"display_name": "交通工具", "table_title_template": "交通工具信息表", "columns": {"类型": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["轿车", "SUV", "MPV", "跑车", "皮卡", "卡车", "公交车", "越野车", "敞篷车", "混合动力车"]}}, "品牌": {"generator_type": "faker_company", "data_category": "text", "params": {"locale": "zh_CN"}}, "型号": {"generator_type": "alphanumeric_pattern", "data_category": "text", "params": {"patterns": ["X###", "XY-###", "Model ##", "Series-#"]}}, "生产商": {"generator_type": "faker_company", "data_category": "text", "params": {"locale": "zh_CN"}}, "生产国": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["中国", "美国", "日本", "德国", "韩国", "法国", "英国", "意大利", "瑞典", "西班牙"]}}, "上市时间": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2000, "end_year": 2024, "format": "%Y-%m-%d"}}, "价格区间": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 50000, "max_value": 1000000, "format_string": "{:,}"}}, "座位数": {"generator_type": "integer_range", "data_category": "numeric", "params": {"min": 2, "max": 9}}, "载重量": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 0.5, "max_value": 50.0, "decimals": 1, "format_string": "{:.1f}吨"}}, "尺寸规格": {"generator_type": "dimension_format", "data_category": "text", "params": {"min_length": 3000, "max_length": 6000, "min_width": 1500, "max_width": 2200, "min_height": 1200, "max_height": 2000, "format_string": "{:d}×{:d}×{:d}mm"}}, "动力类型": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["汽油", "柴油", "电动", "混合动力", "氢能源", "天然气", "生物燃料"]}}, "最高速度": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 100, "max_value": 350, "format_string": "{:d}km/h"}}, "油耗": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 4.5, "max_value": 15.0, "decimals": 1, "format_string": "{:.1f}L/100km"}}, "颜色": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["红色", "蓝色", "黑色", "白色", "灰色", "银色", "绿色", "黄色", "棕色", "紫色"]}}, "发动机型号": {"generator_type": "alphanumeric_pattern", "data_category": "text", "params": {"patterns": ["V##-TDI", "L#-TSI", "T##", "EA###", "F##"]}}, "车牌号": {"generator_type": "categorical_with_pattern", "data_category": "text", "params": {"prefixes": ["京", "沪", "粤", "津", "冀", "晋", "蒙", "辽", "吉", "黑"], "suffixes": ["A", "B", "C", "D", "E", "F", "G", "H", "J", "K"]}}, "年检日期": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2023, "end_year": 2025, "format": "%Y-%m-%d"}}, "保险到期": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2023, "end_year": 2025, "format": "%Y-%m-%d"}}, "燃油效率": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 10, "max_value": 35, "decimals": 1, "format_string": "{:.1f}km/L"}}, "排放标准": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["国六", "国五", "国四", "欧六", "欧五", "T1", "T2", "ZEV"]}}, "安全配置": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["高级", "标准", "基础", "豪华", "安全包", "驾驶辅助", "全套", "增强型"]}}, "维护周期": {"generator_type": "integer_range_with_unit", "data_category": "numeric", "params": {"min": 5000, "max": 20000, "unit": "公里"}}, "保修期限": {"generator_type": "integer_range_with_unit", "data_category": "numeric", "params": {"min": 1, "max": 5, "unit": "年"}}, "驱动方式": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["前驱", "后驱", "四驱", "全时四驱", "适时四驱", "中央驱动"]}}, "变速箱": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["手动", "自动", "CVT", "双离合", "AMT", "AT", "DCT", "MT"]}}, "轮胎类型": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["夏季胎", "冬季胎", "全季节胎", "越野胎", "防爆胎", "高性能胎", "经济型"]}}, "车身结构": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["三厢", "两厢", "掀背", "旅行车", "硬顶敞篷", "软顶敞篷", "硬顶跑车", "轿跑", "猎装车", "皮卡底盘"]}}, "续航里程": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 300, "max_value": 1000, "format_string": "{:d}km"}}, "充电时间": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 0.5, "max_value": 10, "decimals": 1, "format_string": "{:.1f}小时"}}, "智能系统": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["CarPlay", "Android Auto", "智能车机", "语音助手", "智能导航", "OTA升级", "远程控制", "智能驾驶", "AI系统", "无"]}}, "安全气囊数": {"generator_type": "integer_range", "data_category": "numeric", "params": {"min": 2, "max": 12}}, "行驶证号": {"generator_type": "alphanumeric_pattern", "data_category": "text", "params": {"patterns": ["XS########", "DR########", "DC########"]}}, "生产批次": {"generator_type": "categorical_with_pattern", "data_category": "text", "params": {"prefixes": ["A", "B", "C", "D", "E", "F"], "suffixes": ["2020", "2021", "2022", "2023", "2024"]}}, "底盘高度": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 100, "max_value": 300, "format_string": "{:d}mm"}}, "进气方式": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["自然吸气", "涡轮增压", "机械增压", "双涡轮", "电子增压", "混合增压", "无增压"]}}, "转向系统": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["机械式", "电子助力", "液压助力", "电动助力", "四轮转向", "主动转向", "可变转向比", "线控转向"]}}, "上次保养日期": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2023, "end_year": 2024, "format": "%Y-%m-%d"}}, "下次保养日期": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2024, "end_year": 2025, "format": "%Y-%m-%d"}}, "行驶里程": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 500, "max_value": 100000, "format_string": "{:,}公里"}}, "发动机功率": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 60, "max_value": 500, "format_string": "{:d}kW"}}}}, "personnel": {"display_name": "人员信息", "table_title_template": "人员基本信息表", "columns": {"姓名": {"generator_type": "faker_name", "data_category": "text", "params": {"locale": "zh_CN"}}, "年龄": {"generator_type": "integer_range", "data_category": "numeric", "params": {"min": 18, "max": 65}}, "性别": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["男", "女", "未知", "保密"]}}, "部门": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["研发部", "市场部", "销售部", "行政部", "财务部", "人力资源部", "法务部"]}}, "职位": {"generator_type": "faker_job", "data_category": "text", "params": {"locale": "zh_CN"}}, "入职日期": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2010, "end_year": 2024, "format": "%Y-%m-%d"}}, "工号": {"generator_type": "alphanumeric_pattern", "data_category": "text", "params": {"patterns": ["EMP####", "WK-####", "P####"]}}, "联系电话": {"generator_type": "faker_phone_number", "data_category": "text", "params": {"locale": "zh_CN"}}, "电子邮箱": {"generator_type": "faker_email", "data_category": "text", "params": {"locale": "zh_CN"}}, "学历": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["高中", "中专", "大专", "本科", "硕士", "博士", "MBA"]}}, "薪资等级": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["P1", "P2", "P3", "P4", "P5", "P6", "M1", "M2", "D1", "D2"]}}, "月薪": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 5000, "max_value": 50000, "format_string": "{:,}"}}, "籍贯": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["北京", "上海", "广东", "江苏", "浙江", "四川", "湖北", "山东", "河南", "河北"]}}, "紧急联系人": {"generator_type": "faker_name", "data_category": "text", "params": {"locale": "zh_CN"}}, "绩效评级": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["A+", "A", "B+", "B", "C", "D", "未评级"]}}, "技能特长": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["编程", "设计", "营销", "管理", "财务分析", "项目管理", "外语", "沟通", "销售", "数据分析"]}}, "身份证号": {"generator_type": "alphanumeric_pattern", "data_category": "text", "params": {"patterns": ["##############X", "##############0", "##############1"]}}, "婚姻状况": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["未婚", "已婚", "离异", "丧偶", "保密"]}}, "户籍地址": {"generator_type": "faker_address", "data_category": "text", "params": {"locale": "zh_CN"}}, "合同类型": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["正式", "临时", "实习", "顾问", "外包", "兼职", "项目制"]}}, "试用期结束": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2023, "end_year": 2024, "format": "%Y-%m-%d"}}, "合同到期": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2024, "end_year": 2027, "format": "%Y-%m-%d"}}, "负责项目": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["项目A", "项目B", "项目C", "项目D", "项目E", "多项目", "未分配"]}}, "工作地点": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["总部", "分公司", "区域办事处", "远程", "驻场", "海外", "混合"]}}, "入职渠道": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["社会招聘", "校园招聘", "猎头推荐", "内部推荐", "人才市场", "招聘网站", "LinkedIn"]}}, "培训记录": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["已完成", "进行中", "未开始", "不需要", "待安排", "已认证", "待复训"]}}, "教育背景": {"generator_type": "categorical_with_pattern", "data_category": "text", "params": {"prefixes": ["北京大学", "清华大学", "复旦大学", "浙江大学", "上海交大", "武汉大学", "南京大学", "中山大学", "厦门大学", "普通高校"], "suffixes": ["计算机系", "经济系", "管理系", "法学院", "医学院", "工商管理", "机械工程", "电子信息", "人文学院", "理学院"]}}, "语言能力": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["英语流利", "英语良好", "日语N1", "日语N2", "韩语", "法语", "德语", "西班牙语", "多语种", "中文"]}}, "加班情况": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["无加班", "偶尔加班", "经常加班", "项目期间加班", "节假日加班", "高峰期加班", "弹性工作", "正常工作时间", "远程加班", "不定期"]}}, "职业规划": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["技术路线", "管理路线", "专家路线", "创业", "顾问", "国际发展", "学术研究", "自由职业", "待定", "多元发展"]}}, "社保缴纳": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["五险一金", "五险", "三险一金", "全额缴纳", "基数封顶", "最低基数", "未缴纳", "外籍社保", "异地社保", "补充商业保险"]}}, "年假天数": {"generator_type": "integer_range", "data_category": "numeric", "params": {"min": 5, "max": 20}}, "健康状况": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["健康", "良好", "一般", "有慢性病", "需定期体检", "孕期", "哺乳期", "术后恢复", "亚健康", "体弱"]}}, "办公地点": {"generator_type": "categorical_with_pattern", "data_category": "text", "params": {"prefixes": ["A座", "B座", "C座", "科技园", "研发中心", "总部", "分部", "园区"], "suffixes": ["101室", "202室", "303室", "开放区", "会议区", "创新区", "办公区", "协作区"]}}, "绩效奖金": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 1000, "max_value": 50000, "format_string": "{:,}"}}, "证书资质": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["PMP", "CPA", "律师资格证", "教师资格证", "医师资格证", "注册工程师", "软件工程师", "项目管理师", "人力资源管理师", "无"]}}, "上次晋升日期": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2020, "end_year": 2024, "format": "%Y-%m-%d"}}, "下次评估日期": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2024, "end_year": 2025, "format": "%Y-%m-%d"}}, "效率指数": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 75, "max_value": 98, "decimals": 1, "format_string": "{:.1f}"}}, "团队贡献度": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 70, "max_value": 100, "decimals": 0, "format_string": "{:d}分"}}}}, "education": {"display_name": "教育信息", "table_title_template": "教育信息表", "columns": {"学校名称": {"generator_type": "categorical", "params": {"values": ["北京大学", "清华大学", "复旦大学", "上海交通大学", "浙江大学", "南京大学", "武汉大学", "中国人民大学", "中山大学", "华中科技大学", "南开大学", "四川大学", "吉林大学", "山东大学", "厦门大学", "哈尔滨工业大学", "西安交通大学", "中南大学", "电子科技大学", "东南大学", "同济大学", "北京师范大学", "天津大学", "华南理工大学", "中国科学技术大学", "华东师范大学", "北京航空航天大学", "大连理工大学", "重庆大学", "西北工业大学", "中国农业大学", "湖南大学", "东北大学", "北京理工大学", "华中师范大学", "北京科技大学", "南京师范大学", "中国海洋大学", "西南大学", "兰州大学", "云南大学", "广西大学", "贵州大学", "东北师范大学", "西北大学", "安徽大学", "郑州大学", "首都师范大学", "暨南大学", "深圳大学", "苏州大学", "南昌大学", "海南大学", "福州大学", "宁波大学", "青岛大学", "合肥工业大学", "北京交通大学", "北京邮电大学", "华南师范大学", "西南交通大学", "北京工业大学", "南京航空航天大学", "华东理工大学", "河海大学", "中国矿业大学", "北京中医药大学", "上海财经大学", "中国政法大学", "中央财经大学", "对外经济贸易大学", "中国传媒大学", "上海外国语大学", "复旦大学附属中学", "北京四中", "上海中学", "南京外国语学校", "成都七中", "华南师范大学附属中学", "广州市第二中学", "深圳中学", "杭州学军中学", "西安交通大学附属中学", "武汉外国语学校", "东北师范大学附属中学", "清华大学附属中学", "复旦大学附属小学", "上海师范大学附属小学", "人大附中", "北大附中", "南师附中"]}, "data_category": "text"}, "专业": {"generator_type": "categorical", "params": {"values": ["计算机科学与技术", "软件工程", "人工智能", "数据科学", "信息安全", "电子信息工程", "通信工程", "自动化", "机械工程", "金融学", "经济学", "会计学", "工商管理", "市场营销", "国际贸易", "法学", "汉语言文学", "英语", "日语", "新闻学", "医学", "临床医学", "药学", "化学", "物理学", "数学", "统计学", "生物技术", "生物医学工程", "环境科学", "材料科学与工程", "土木工程", "建筑学", "城市规划", "风景园林", "航空航天工程", "船舶与海洋工程", "电气工程", "能源与动力工程", "核工程", "农学", "林学", "水产养殖学", "动物医学", "兽医学", "食品科学与工程", "生物工程", "化学工程与工艺", "制药工程", "高分子材料与工程", "冶金工程", "矿业工程", "石油工程", "测绘工程", "地质工程", "安全工程", "工业设计", "产品设计", "视觉传达设计", "环境设计", "音乐表演", "绘画", "雕塑", "戏剧影视文学", "广播电视编导", "播音与主持艺术", "动画", "舞蹈学", "运动训练", "体育教育", "社会学", "政治学与行政学", "德语", "法语", "西班牙语", "意大利语", "俄语", "阿拉伯语", "朝鲜语", "印地语", "历史学", "考古学", "哲学", "宗教学", "天文学", "地理科学", "大气科学", "海洋科学", "地质学", "心理学", "应用心理学", "教育学", "学前教育", "特殊教育", "小学教育", "公共事业管理", "行政管理", "资源环境与城乡规划管理", "电子商务", "物流管理", "旅游管理", "计算机科学与技术", "软件工程", "人工智能", "网络工程", "信息安全", "数据科学与大数据技术", "物联网工程", "数字媒体技术", "智能科学与技术", "空间信息与数字技术", "电子信息工程", "通信工程", "自动化", "电气工程及其自动化", "机械设计制造及其自动化", "车辆工程", "能源与动力工程", "土木工程", "工程管理", "工业设计", "建筑学", "城市规划", "环境设计", "测绘工程", "化学工程与工艺", "制药工程", "生物工程", "生物信息学", "临床医学", "口腔医学", "护理学", "医学影像学", "中医学", "公共卫生与预防医学", "药学", "经济学", "国际经济与贸易", "金融学", "会计学", "财务管理", "审计学", "工商管理", "人力资源管理", "市场营销", "物流管理", "电子商务", "旅游管理", "法学", "知识产权", "社会工作", "政治学与行政学", "国际政治", "汉语言文学", "新闻学", "广告学", "广播电视学", "网络与新媒体", "英语", "日语", "翻译", "教育学", "学前教育", "小学教育", "心理学", "历史学", "数学与应用数学", "信息与计算科学", "物理学", "应用物理学", "化学", "应用化学", "统计学", "地理科学", "地质学", "环境科学", "环境工程", "生态学", "海洋科学", "材料科学与工程", "高分子材料与工程", "冶金工程", "安全工程", "核工程与核技术", "船舶与海洋工程", "航空航天工程", "兵器科学与技术", "交通运输", "轨道交通信号与控制", "农业机械化及其自动化", "园艺", "动物医学", "食品科学与工程", "酿酒工程", "茶学", "美术学", "音乐学", "舞蹈学", "戏剧影视文学", "数字艺术", "动画", "游戏设计"]}, "data_category": "text"}, "学历": {"generator_type": "categorical", "params": {"values": ["高中", "中专", "大专", "本科", "硕士", "博士", "博士后", "初中", "小学", "职业高中", "技工学校", "自学考试", "成人高考", "电大", "远程教育", "研究生在读", "函授", "留学", "联合培养", "MBA", "EMBA", "MPA", "双学位", "辅修学位", "专升本", "高起专", "专转本", "硕博连读", "学士学位", "硕士学位", "博士学位"]}, "data_category": "text"}, "毕业时间": {"generator_type": "date_range", "params": {"start_year": 2010, "end_year": 2023, "format": "%Y-%m-%d"}, "data_category": "date"}, "入学时间": {"generator_type": "date_range", "params": {"start_year": 2005, "end_year": 2020, "format": "%Y-%m-%d"}, "data_category": "date"}, "学位": {"generator_type": "categorical", "params": {"values": ["学士学位", "硕士学位", "博士学位", "名誉博士学位", "副博士学位", "法学学士", "理学学士", "工学学士", "医学学士", "管理学学士", "经济学学士", "文学学士", "教育学学士", "农学学士", "军事学学士", "艺术学学士", "哲学学士", "历史学学士", "法学硕士", "理学硕士", "工学硕士", "医学硕士", "管理学硕士", "经济学硕士", "文学硕士", "教育学硕士", "农学硕士", "军事学硕士", "艺术学硕士", "哲学硕士", "历史学硕士", "法学博士", "理学博士", "工学博士", "医学博士", "管理学博士", "经济学博士", "文学博士", "教育学博士", "农学博士", "军事学博士", "艺术学博士", "哲学博士", "历史学博士", "双学位", "联合学位", "专业学位", "学位证书", "同等学力申硕"]}, "data_category": "text"}, "绩点": {"generator_type": "numerical_range_formatted", "params": {"min_value": 2.0, "max_value": 4.0, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}, "排名": {"generator_type": "integer_range_with_unit", "params": {"min": 1, "max": 30, "unit": "%"}, "data_category": "numeric"}, "奖学金": {"generator_type": "categorical", "params": {"values": ["国家奖学金", "国家励志奖学金", "校级一等奖学金", "校级二等奖学金", "校级三等奖学金", "专项奖学金", "企业奖学金", "无", "研究生国家奖学金", "优秀学生奖学金", "学习优秀奖学金", "科研优秀奖学金", "社会工作奖学金", "社会实践奖学金", "单项奖学金", "优秀新生奖学金", "优秀毕业生奖学金", "综合奖学金", "国际交流奖学金", "学科竞赛奖学金", "学术创新奖学金", "特等奖学金", "院长奖学金", "校长奖学金", "社会捐赠奖学金", "助学金", "勤工助学基金", "贫困生补助", "少数民族学生奖学金", "优秀学生干部奖学金", "创新创业奖学金", "体育特长奖学金", "艺术特长奖学金", "军训优秀奖学金", "优秀志愿者奖学金", "科技发明奖学金", "社会工作积极分子奖学金", "文艺活动积极分子奖学金", "优秀团员奖学金", "优秀党员奖学金", "竞赛优胜奖学金"]}, "data_category": "text"}, "导师": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}}}, "product": {"display_name": "产品信息", "table_title_template": "产品数据表", "columns": {"产品名称": {"generator_type": "categorical_with_pattern", "params": {"prefixes": ["智能", "多功能", "便携", "高性能", "经典", "超薄", "迷你", "专业", "入门级", "旗舰"], "suffixes": ["手机", "笔记本", "平板", "智能手表", "耳机", "音箱", "电视", "相机", "打印机", "路由器"]}, "data_category": "text"}, "品牌": {"generator_type": "categorical", "params": {"values": ["苹果", "华为", "小米", "三星", "索尼", "戴尔", "联想", "海尔", "美的", "LG", "松下", "佳能", "微软", "惠普", "OPPO", "VIVO", "飞利浦", "西门子", "大疆", "宜家", "迪卡侬", "耐克", "阿迪达斯", "安踏", "李宁", "特步", "匹克", "彪马", "新百伦", "斯凯奇", "优衣库", "H&M", "ZARA", "GAP", "无印良品", "MUJI", "雀巢", "可口可乐", "百事可乐", "农夫山泉", "康师傅", "统一", "娃哈哈", "伊利", "蒙牛", "光<PERSON>", "三只松鼠", "良品铺子", "百草味", "周黑鸭", "来伊份", "华硕", "宏碁", "HTC", "摩托罗拉", "诺基亚", "黑莓", "东芝", "富士", "TCL", "长虹", "创维", "格力", "奥克斯", "志高", "科龙", "海信", "康佳", "夏普", "明基", "雷蛇", "罗技", "赛睿", "雷柏", "双飞燕", "贵族", "富勒", "爱普生", "兄弟", "富士施乐", "柯达", "尼康", "宾得", "徕卡", "奥林巴斯", "富士胶片", "卡西欧", "精工", "欧米茄", "天梭", "浪琴", "美度", "依波路", "汉密尔顿", "宝珀", "万国", "江诗丹顿", "劳力士", "欧莱雅", "资生堂", "兰蔻", "雅诗兰黛", "迪奥", "香奈儿", "YSL", "MAC", "SK-II", "碧欧泉", "科颜氏", "雪花秀", "兰芝", "海蓝之谜", "娇韵诗", "希思黎", "茱莉蔻", "巴黎欧莱雅"]}, "data_category": "text"}, "型号": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["X###", "Pro-##", "MAX###", "Air-#", "Plus##", "Neo-##", "Y##X", "Z##"]}, "data_category": "text"}, "价格": {"generator_type": "numerical_range_formatted", "params": {"min_value": 999, "max_value": 15999, "format_string": "{:,}"}, "data_category": "numeric"}, "上市日期": {"generator_type": "date_range", "params": {"start_year": 2020, "end_year": 2023, "format": "%Y-%m-%d"}, "data_category": "date"}, "库存": {"generator_type": "integer_range", "params": {"min": 0, "max": 1000}, "data_category": "numeric"}, "销量": {"generator_type": "integer_range", "params": {"min": 100, "max": 50000}, "data_category": "numeric"}, "评分": {"generator_type": "numerical_range_formatted", "params": {"min_value": 3.5, "max_value": 5.0, "decimals": 1, "format_string": "{:.1f}"}, "data_category": "numeric"}, "颜色": {"generator_type": "categorical", "params": {"values": ["红色", "蓝色", "黄色", "绿色", "橙色", "紫色", "粉色", "白色", "黑色", "灰色", "棕色", "米色", "青色", "浅蓝色", "深蓝色", "天蓝色", "宝蓝色", "藏蓝色", "湖蓝色", "深绿色", "浅绿色", "草绿色", "薄荷绿", "橄榄绿", "军绿色", "青柠色", "浅黄色", "土黄色", "金黄色", "香槟色", "柠檬黄", "淡紫色", "深紫色", "薰衣草色", "茄子紫", "葡萄紫", "浅粉色", "玫瑰粉", "樱花粉", "桃红色", "莓果红", "砖红色", "酒红色", "胭脂红", "火焰红", "橙红色", "珊瑚色", "胡萝卜橙", "琥珀色", "象牙白", "奶油白", "珍珠白", "雪白", "烟白色", "雾白色", "炭黑色", "乌黑", "墨黑", "亮黑", "亚光黑", "银灰色", "铅灰色", "钛白色", "钢青色", "石板灰", "巧克力色", "咖啡色", "摩卡棕", "栗色", "古铜色", "焦糖色", "灰褐色", "驼色", "奶茶色", "香槟金", "金色", "银色", "铜色", "淡蓝灰", "淡紫灰", "石青色", "鹅卵石灰", "石榴红", "蔷薇红", "莓紫色", "梅子红", "葡萄酒红", "天青色", "青黛色", "水鸭色", "墨绿色", "翡翠绿", "松石绿", "孔雀蓝", "钴蓝色", "亮蓝色", "霞光红", "蜜桃粉", "南瓜橙", "柚子黄", "柿子橙", "琥珀黄", "琉璃蓝", "钴绿", "碧绿色", "青瓷色", "豆绿色", "海水绿", "海天蓝", "湖绿", "鲑鱼色", "番茄红", "樱桃红", "番石榴粉", "雾蓝", "云灰", "灰蓝", "灰绿", "鼠灰", "淡棕", "烟墨色", "墨蓝", "靛蓝", "深靛青", "暮紫", "夜蓝"]}, "data_category": "text"}, "产品描述": {"generator_type": "categorical_with_pattern", "params": {"prefixes": ["高性能", "轻薄便携", "长续航", "高清显示", "快速充电", "大容量", "智能操控", "防水防尘", "高精度", "快速响应", "时尚外观", "强劲动力", "超低功耗", "精准控制", "创新设计", "持久耐用", "超高清", "多功能", "智能适配"], "suffixes": ["，提升效率", "，改善体验", "，舒适便捷", "，科技尖端", "，品质保障", "，性价比高", "，美观大方", "，节省时间", "，用户至上", "，轻松操作", "，极致性能", "，强力支持", "，功能强大", "，操作简便", "，性能卓越", "，满足需求", "，完美体验", "，卓越品质", "，随心所欲", "，持久稳定"]}, "data_category": "text"}}}, "finance": {"display_name": "财务信息", "table_title_template": "财务交易信息表", "columns": {"交易编号": {"generator_type": "alphanumeric_pattern", "data_category": "text", "params": {"patterns": ["TRX######", "FIN-####-##", "PAY######"]}}, "交易日期": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2023, "end_year": 2024, "format": "%Y-%m-%d"}}, "交易时间": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["09:15:30", "10:45:22", "12:30:15", "14:20:45", "16:05:12", "17:30:00", "19:45:36", "21:10:25"]}}, "交易类型": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["收入", "支出", "转账", "退款", "预付款", "投资", "贷款", "分期付款", "报销"]}}, "交易金额": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 10, "max_value": 100000, "decimals": 2, "format_string": "{:,.2f}"}}, "账户名称": {"generator_type": "faker_name", "data_category": "text", "params": {"locale": "zh_CN"}}, "支付方式": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["支付宝", "微信支付", "银行卡", "现金", "信用卡", "公司账户", "PayPal", "Apple Pay", "虚拟货币"]}}, "交易状态": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["已完成", "处理中", "已取消", "待处理", "失败", "部分完成", "待确认", "已退款"]}}, "交易描述": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["购买商品", "服务费", "薪资支付", "租金", "水电费", "办公用品", "市场推广", "技术服务", "餐饮费", "差旅费", "保险费", "税费", "培训费"]}}, "交易渠道": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["线上", "线下", "移动端", "PC端", "银行柜台", "ATM", "自助终端", "电话银行", "第三方平台"]}}, "收款方": {"generator_type": "faker_company", "data_category": "text", "params": {"locale": "zh_CN"}}, "付款方": {"generator_type": "faker_company", "data_category": "text", "params": {"locale": "zh_CN"}}, "交易区域": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["国内", "国际", "港澳台", "欧美", "亚太", "非洲", "北美", "南美", "线上"]}}, "税率": {"generator_type": "percentage", "data_category": "numeric", "params": {"min_value": 0, "max_value": 30, "decimals": 1}}, "税额": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 0, "max_value": 10000, "decimals": 2, "format_string": "{:,.2f}"}}, "币种": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["人民币", "美元", "欧元", "英镑", "日元", "港币", "澳元", "加元", "韩元"]}}, "汇率": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 0.001, "max_value": 10, "decimals": 4, "format_string": "{:.4f}"}}, "业务类型": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["销售", "采购", "工资", "费用报销", "资本支出", "贷款", "投资", "股东分红", "税费"]}}, "经办人": {"generator_type": "faker_name", "data_category": "text", "params": {"locale": "zh_CN"}}, "审批人": {"generator_type": "faker_name", "data_category": "text", "params": {"locale": "zh_CN"}}, "财务机构": {"generator_type": "categorical_with_pattern", "data_category": "text", "params": {"prefixes": ["工商银行", "建设银行", "农业银行", "中国银行", "招商银行", "交通银行", "浦发银行", "民生银行", "平安银行", "兴业银行"], "suffixes": ["总行", "分行", "支行", "营业部", "网点", "金融中心"]}}, "账单周期": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["日结", "周结", "月结", "季度", "半年", "年度", "不定期", "T+1", "T+3", "实时"]}}, "结算方式": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["全额结算", "分期结算", "定金+尾款", "货到付款", "服务完成付款", "预付全款", "月底结算", "账期结算", "对公转账", "第三方托管"]}}, "关联合同编号": {"generator_type": "alphanumeric_pattern", "data_category": "text", "params": {"patterns": ["CTR-####-####", "CON######", "AG-###-####-##"]}}, "风险等级": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["低风险", "中低风险", "中等风险", "中高风险", "高风险", "无风险", "待评估", "R1", "R2", "R3"]}}, "发票状态": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["已开票", "未开票", "部分开票", "待开票", "已作废", "退票中", "已退票", "开票中", "发票丢失", "不需要开票"]}}, "资金来源": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["自有资金", "银行贷款", "投资人投入", "融资租赁", "众筹", "政府补助", "合作伙伴出资", "内部调拨", "集团拨款", "其他"]}}, "财务审核结果": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["通过", "驳回", "修改后通过", "保留意见", "暂缓处理", "需补充材料", "上报审核", "条件通过", "特批通过", "待审核"]}}, "资金用途": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["运营支出", "固定资产", "项目投资", "人力成本", "市场推广", "研发投入", "债务偿还", "资金周转", "储备金", "股东分红"]}}, "预算归属": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["销售部门", "市场部门", "研发部门", "行政部门", "人力资源", "财务部门", "生产部门", "采购部门", "总经办", "非预算项目"]}}, "年度交易总额": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 100000, "max_value": 10000000, "format_string": "{:,}"}}, "清算日期": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2023, "end_year": 2024, "format": "%Y-%m-%d"}}}}, "health": {"display_name": "健康档案", "table_title_template": "医疗健康记录表", "columns": {"患者姓名": {"generator_type": "faker_name", "data_category": "text", "params": {"locale": "zh_CN"}}, "患者ID": {"generator_type": "alphanumeric_pattern", "data_category": "text", "params": {"patterns": ["P######", "MR-####-##", "HID######"]}}, "性别": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["男", "女"]}}, "年龄": {"generator_type": "integer_range", "data_category": "numeric", "params": {"min": 1, "max": 100}}, "出生日期": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 1950, "end_year": 2023, "format": "%Y-%m-%d"}}, "血型": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["A型", "B型", "AB型", "O型", "A型Rh阴性", "B型Rh阴性", "AB型Rh阴性", "O型Rh阴性"]}}, "身高": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 50, "max_value": 200, "decimals": 1, "format_string": "{:.1f}cm"}}, "体重": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 5, "max_value": 150, "decimals": 1, "format_string": "{:.1f}kg"}}, "BMI指数": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 15, "max_value": 40, "decimals": 1, "format_string": "{:.1f}"}}, "血压": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["120/80", "110/70", "130/85", "140/90", "150/95", "160/100", "115/75", "125/82"]}}, "脉搏": {"generator_type": "integer_range_with_unit", "data_category": "numeric", "params": {"min": 50, "max": 120, "unit": "次/分"}}, "体温": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 35.5, "max_value": 39.5, "decimals": 1, "format_string": "{:.1f}°C"}}, "就诊日期": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2023, "end_year": 2024, "format": "%Y-%m-%d"}}, "科室": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["内科", "外科", "妇产科", "儿科", "骨科", "眼科", "耳鼻喉科", "皮肤科", "神经科", "心脏科", "呼吸科", "泌尿科"]}}, "主诊医师": {"generator_type": "faker_name", "data_category": "text", "params": {"locale": "zh_CN"}}, "主诉": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["发热", "头痛", "咳嗽", "腹痛", "胸闷", "乏力", "恶心", "呕吐", "关节疼痛", "腰痛", "皮疹", "眩晕", "视力模糊"]}}, "诊断结果": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["普通感冒", "流感", "胃炎", "结膜炎", "高血压", "糖尿病", "贫血", "支气管炎", "肠胃炎", "腰肌劳损", "荨麻疹", "过敏性鼻炎"]}}, "用药建议": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["抗生素", "消炎药", "退烧药", "止痛药", "抗过敏药", "胃药", "降压药", "降糖药", "维生素", "营养补充剂", "口服液", "外用药膏"]}}, "治疗方案": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["口服药物治疗", "物理治疗", "心理治疗", "手术治疗", "针灸治疗", "推拿按摩", "饮食调理", "日常保健", "中医治疗", "观察随访"]}}, "过敏史": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["无", "青霉素过敏", "海鲜过敏", "花粉过敏", "尘螨过敏", "对某些食物过敏", "药物过敏", "多种过敏"]}}, "家族病史": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["无", "高血压", "糖尿病", "心脏病", "癌症", "肝病", "肾病", "哮喘", "精神疾病", "自身免疫疾病"]}}, "复诊时间": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2023, "end_year": 2024, "format": "%Y-%m-%d"}}, "医疗保险类型": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["社会医疗保险", "商业医疗保险", "城镇职工医保", "城乡居民医保", "公费医疗", "新农合", "无保险", "国际医疗保险", "企业补充医疗保险", "医疗救助"]}}, "就诊医院": {"generator_type": "categorical_with_pattern", "data_category": "text", "params": {"prefixes": ["北京", "上海", "广州", "深圳", "武汉", "成都", "南京", "西安", "杭州", "重庆"], "suffixes": ["协和医院", "人民医院", "第一医院", "中医院", "妇幼保健院", "儿童医院", "肿瘤医院", "口腔医院", "中西医结合医院", "三甲医院"]}}, "住院状态": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["未住院", "待住院", "住院中", "已出院", "转院", "日间住院", "ICU", "康复中心", "留观", "家庭病床"]}}, "住院天数": {"generator_type": "integer_range_with_unit", "data_category": "numeric", "params": {"min": 1, "max": 30, "unit": "天"}}, "医疗费用": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 100, "max_value": 50000, "decimals": 2, "format_string": "{:.2f}"}}, "检查项目": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["血常规", "尿常规", "CT扫描", "X光", "B超", "心电图", "磁共振", "骨密度检查", "肝功能", "肾功能", "血脂检查", "血糖检查", "内窥镜检查"]}}, "检查结果": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["正常", "轻度异常", "中度异常", "重度异常", "需进一步检查", "疑似阳性", "阳性", "阴性", "边缘性异常", "待复查"]}}, "营养状况": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["良好", "一般", "较差", "营养不良", "营养过剩", "蛋白质缺乏", "维生素缺乏", "贫血", "肥胖", "消瘦"]}}, "心理状态": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["正常", "焦虑", "抑郁", "紧张", "恐惧", "情绪波动", "适应良好", "应激状态", "心理压力大", "情绪稳定"]}}, "康复计划": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["物理治疗", "职业治疗", "言语治疗", "心理康复", "营养干预", "运动康复", "家庭康复", "社区康复", "综合康复", "长期随访"]}}, "随访日期": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2024, "end_year": 2025, "format": "%Y-%m-%d"}}, "检查周期": {"generator_type": "integer_range_with_unit", "data_category": "numeric", "params": {"min": 1, "max": 12, "unit": "月"}}}}, "project": {"display_name": "项目管理", "table_title_template": "项目任务管理表", "columns": {"项目名称": {"generator_type": "categorical_with_pattern", "data_category": "text", "params": {"prefixes": ["智能", "数字化", "云平台", "大数据", "AI", "移动", "在线", "物联网", "区块链", ""], "suffixes": ["系统开发", "平台升级", "数据分析", "用户研究", "应用开发", "解决方案", "营销推广", "基础设施建设"]}}, "项目编号": {"generator_type": "alphanumeric_pattern", "data_category": "text", "params": {"patterns": ["PRJ-####-##", "P####", "PROJ######"]}}, "任务ID": {"generator_type": "alphanumeric_pattern", "data_category": "text", "params": {"patterns": ["TASK-###", "T####", "TID-######"]}}, "任务名称": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["需求分析", "系统设计", "UI设计", "前端开发", "后端开发", "数据库设计", "测试", "部署", "文档编写", "培训", "项目管理", "质量控制"]}}, "任务描述": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["完成用户需求收集", "设计系统架构", "开发核心功能模块", "修复已知bug", "优化性能", "撰写技术文档", "进行用户测试", "部署测试环境", "与客户沟通确认需求", "评审代码质量"]}}, "优先级": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["高", "中", "低", "紧急", "非常高", "次要", "可选"]}}, "状态": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["未开始", "进行中", "已完成", "已延期", "已取消", "待评审", "待测试", "待部署", "已关闭"]}}, "计划开始日期": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2023, "end_year": 2024, "format": "%Y-%m-%d"}}, "计划结束日期": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2023, "end_year": 2025, "format": "%Y-%m-%d"}}, "实际开始日期": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2023, "end_year": 2024, "format": "%Y-%m-%d"}}, "实际结束日期": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2023, "end_year": 2025, "format": "%Y-%m-%d"}}, "负责人": {"generator_type": "faker_name", "data_category": "text", "params": {"locale": "zh_CN"}}, "团队成员": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["张工,李工", "王工,赵工,钱工", "孙工,周工", "吴工,郑工,冯工", "陈工,褚工", "魏工,蒋工,沈工,韩工", "杨工,朱工", "秦工,许工"]}}, "工时估计": {"generator_type": "integer_range_with_unit", "data_category": "numeric", "params": {"min": 1, "max": 100, "unit": "小时"}}, "实际工时": {"generator_type": "integer_range_with_unit", "data_category": "numeric", "params": {"min": 1, "max": 120, "unit": "小时"}}, "完成百分比": {"generator_type": "percentage", "data_category": "numeric", "params": {"min_value": 0, "max_value": 100, "decimals": 0}}, "关联任务": {"generator_type": "alphanumeric_pattern", "data_category": "text", "params": {"patterns": ["TASK-###", "T####", "无"]}}, "风险级别": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["高", "中", "低", "无风险"]}}, "备注": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["需要加急处理", "与客户进一步确认需求", "等待其他团队协助", "技术难点需要攻克", "资源配置不足", "进展顺利", "已提前完成", "质量有待提高", "需要更多测试"]}}, "所属迭代": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["Sprint 1", "Sprint 2", "Sprint 3", "迭代1", "迭代2", "迭代3", "版本1.0", "版本2.0", "MVP阶段", "产品验证阶段"]}}, "项目预算": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 10000, "max_value": 1000000, "format_string": "{:,}"}}, "项目类型": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["内部项目", "外包项目", "研发项目", "维护项目", "升级项目", "整合项目", "试点项目", "战略项目", "创新项目", "商业化项目"]}}, "客户名称": {"generator_type": "faker_company", "data_category": "text", "params": {"locale": "zh_CN"}}, "项目进度": {"generator_type": "percentage", "data_category": "numeric", "params": {"min_value": 0, "max_value": 100, "decimals": 0}}, "技术栈": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["Java/Spring", "Python/Django", "JavaScript/React", "PHP/Laravel", "C#/.NET", "Ruby on Rails", "Node.js/Express", "Flutter/Dart", "Go/Gin", "Vue.js/Nuxt"]}}, "预期成果": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["系统上线", "功能完善", "性能提升", "用户满意度提高", "成本降低", "流程优化", "数据整合", "平台迁移", "代码重构", "安全性提升"]}}, "质量评级": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["A+", "A", "B+", "B", "C", "需改进", "合格", "优秀", "不合格", "超出预期"]}}, "会议频率": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["每日站会", "每周例会", "双周会议", "月度汇报", "需求驱动", "临时会议", "里程碑会议", "远程协作", "不定期沟通", "按需会议"]}}, "部署环境": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["开发环境", "测试环境", "预发布环境", "生产环境", "本地环境", "云服务", "混合云", "私有云", "容器化环境", "微服务架构"]}}, "资源需求": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["前端开发2人", "后端开发3人", "UI设计1人", "测试2人", "产品经理1人", "DevOps工程师", "数据分析师", "全栈开发3人", "项目经理1人", "技术顾问"]}}, "开发成本": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 5000, "max_value": 100000, "format_string": "{:,}/月"}}, "上线日期": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2023, "end_year": 2025, "format": "%Y-%m-%d"}}}}, "real_estate": {"display_name": "房产信息", "table_title_template": "房产信息登记表", "columns": {"房产编号": {"generator_type": "alphanumeric_pattern", "data_category": "text", "params": {"patterns": ["RE-####-##", "H######", "PROP####"]}}, "房产类型": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["住宅", "商铺", "写字楼", "厂房", "公寓", "别墅", "复式", "平房", "商住两用", "酒店式公寓"]}}, "地址": {"generator_type": "faker_address", "data_category": "text", "params": {"locale": "zh_CN"}}, "所在城市": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["北京", "上海", "广州", "深圳", "成都", "武汉", "杭州", "南京", "西安", "重庆"]}}, "面积": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 20, "max_value": 500, "decimals": 2, "format_string": "{:.2f}㎡"}}, "价格": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 500000, "max_value": 20000000, "format_string": "{:,}"}}, "单价": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 5000, "max_value": 100000, "format_string": "{:,}/㎡"}}, "卧室数量": {"generator_type": "integer_range", "data_category": "numeric", "params": {"min": 1, "max": 6}}, "卫生间数量": {"generator_type": "integer_range", "data_category": "numeric", "params": {"min": 1, "max": 4}}, "楼层": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["1层", "2层", "3层", "4层", "5层", "6层", "7层", "8层", "9层", "10层", "11-20层", "20层以上"]}}, "总楼层": {"generator_type": "integer_range", "data_category": "numeric", "params": {"min": 1, "max": 60}}, "建成年份": {"generator_type": "integer_range", "data_category": "numeric", "params": {"min": 1990, "max": 2024}}, "装修情况": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["精装修", "简装", "毛坯", "豪华装修", "中等装修", "清水房", "老旧房", "翻新装修"]}}, "朝向": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["南北通透", "东西朝向", "南向", "北向", "东向", "西向", "东南", "西南", "东北", "西北"]}}, "小区名称": {"generator_type": "categorical_with_pattern", "data_category": "text", "params": {"prefixes": ["阳光", "金色", "绿地", "万科", "保利", "恒大", "碧桂园", "龙湖", "华润", "富力"], "suffixes": ["花园", "小区", "公馆", "华府", "苑", "城", "景苑", "名苑", "佳园", "豪庭"]}}, "交通情况": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["近地铁", "公交便利", "地铁口500米", "临主干道", "公交站步行5分钟", "交通便利", "有轻轨", "临高速路口", "有机场大巴", "靠近火车站"]}}, "配套设施": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["学校,医院,商场", "公园,超市,健身房", "商业街,幼儿园,游泳池", "图书馆,影院,餐厅", "会所,老年活动中心,运动场", "社区医疗,停车场,儿童乐园"]}}, "房本情况": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["满二", "满五", "未满二", "未满五", "一手房", "二手房", "公房", "商品房", "军产房"]}}, "土地性质": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["住宅用地", "商业用地", "工业用地", "综合用地", "教育用地", "医疗用地", "农用地"]}}, "产权年限": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["70年", "50年", "40年", "永久", "40年以下", "50-70年"]}}, "挂牌时间": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2023, "end_year": 2024, "format": "%Y-%m-%d"}}, "联系人": {"generator_type": "faker_name", "data_category": "text", "params": {"locale": "zh_CN"}}, "联系电话": {"generator_type": "faker_phone_number", "data_category": "text", "params": {"locale": "zh_CN"}}, "物业费": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 1, "max_value": 10, "decimals": 2, "format_string": "{:.2f}/㎡/月"}}, "供暖方式": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["集中供暖", "自采暖", "煤气暖气", "电暖气", "空调制热", "地暖", "暖气片", "太阳能", "无供暖", "燃气壁挂炉"]}}, "绿化率": {"generator_type": "percentage", "data_category": "numeric", "params": {"min_value": 10, "max_value": 70, "decimals": 0}}, "车位情况": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["地下车位充足", "地面车位", "无固定车位", "一户一车位", "车位紧张", "收费车位", "专属车库", "充电桩车位", "机械车位", "租赁车位"]}}, "开发商": {"generator_type": "faker_company", "data_category": "text", "params": {"locale": "zh_CN"}}, "看房时间": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["随时可看", "预约看房", "周末可看", "工作日可看", "电话预约", "每日9点-18点", "每日全天", "联系中介", "业主陪同看房", "暂不可看"]}}, "税费情况": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["买卖双方各付", "买方承担", "卖方承担", "免税房", "契税已缴", "增值税需缴", "个税需缴", "税费待定", "首套优惠", "满五唯一"]}}, "户型结构": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["平层", "错层", "跃层", "复式", "开间", "普通住宅", "LOFT", "花园洋房", "联排别墅", "独栋别墅"]}}, "电梯情况": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["有电梯", "无电梯", "双梯", "一梯两户", "一梯三户", "一梯四户", "电梯房", "步梯房", "货梯", "观光电梯"]}}, "楼龄": {"generator_type": "integer_range_with_unit", "data_category": "numeric", "params": {"min": 1, "max": 50, "unit": "年"}}, "装修费用": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 50000, "max_value": 500000, "format_string": "{:,}"}}, "满意度评分": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 3.0, "max_value": 5.0, "decimals": 1, "format_string": "{:.1f}分"}}}}, "sports": {"display_name": "体育赛事", "table_title_template": "体育赛事统计表", "columns": {"比赛项目": {"generator_type": "categorical", "params": {"values": ["足球", "篮球", "排球", "网球", "乒乓球", "羽毛球", "游泳", "田径", "体操", "举重", "射击", "击剑", "拳击", "柔道", "跆拳道", "马拉松", "棒球", "高尔夫", "冰球", "滑雪", "花样滑冰", "短道速滑", "橄榄球", "美式足球", "手球", "曲棍球", "水球", "沙滩排球", "保龄球", "壁球", "台球", "斯诺克", "藤球", "垒球", "板球", "壁球", "飞盘", "极限运动", "攀岩", "滑板", "冲浪", "帆船", "赛艇", "皮划艇", "独木舟", "潜水", "跳水", "蹦床", "艺术体操", "健美操", "舞蹈", "武术", "空手道", "摔跤", "散打", "太极拳", "自行车", "公路自行车", "山地自行车", "BMX", "铁人三项", "现代五项", "马术", "赛马", "滑翔", "热气球", "跳伞", "蹦极", "高山滑雪", "越野滑雪", "单板滑雪", "速度滑冰", "冰壶", "钓鱼", "飞镖", "台球", "象棋", "国际象棋", "围棋", "桥牌", "电子竞技", "赛车", "方程式赛车", "拉力赛车", "摩托车赛", "卡丁车", "水上摩托", "滑水", "水上飞机", "动力伞", "热气球", "三项全能", "铁人五项", "室内划船", "健美健身", "野外定向", "定向越野", "户外探险", "攀冰", "山地车", "障碍赛", "越野跑", "超级马拉松", "障碍跑"]}, "data_category": "text"}, "比赛日期": {"generator_type": "date_range", "params": {"start_year": 2022, "end_year": 2024, "format": "%Y-%m-%d"}, "data_category": "date"}, "比赛地点": {"generator_type": "categorical_with_pattern", "params": {"prefixes": {"type": "common_data", "key": "major_cities"}, "suffixes": ["体育场", "体育馆", "奥体中心", "奥林匹克体育中心", "综合体育馆", "体育中心"]}, "data_category": "text"}, "参赛队伍": {"generator_type": "categorical_with_pattern", "params": {"prefixes": {"type": "common_data", "key": "major_cities"}, "suffixes": ["队", "俱乐部", "体育队", "联队", "勇士", "猛虎", "雄鹰", "飞龙", "火箭", "骑士"]}, "data_category": "text"}, "比分": {"generator_type": "categorical", "params": {"values": ["1:0", "2:1", "3:2", "2:0", "3:0", "1:1", "2:2", "3:3", "4:3", "5:4", "0:0", "1:2", "0:3", "2:3", "3:4", "4:5"]}, "data_category": "text"}, "冠军": {"generator_type": "categorical_with_pattern", "params": {"prefixes": {"type": "common_data", "key": "major_cities"}, "suffixes": ["队", "俱乐部", "体育队", "联队", "勇士"]}, "data_category": "text"}, "观众人数": {"generator_type": "numerical_range_formatted", "params": {"min_value": 1, "max_value": 90, "format_string": "{:d}万"}, "data_category": "numeric"}, "赛事级别": {"generator_type": "categorical", "params": {"values": ["世界杯", "奥运会", "世锦赛", "全国锦标赛", "联赛", "邀请赛", "友谊赛", "洲际杯", "大满贯", "大师赛", "公开赛"]}, "data_category": "text"}, "学历": {"generator_type": "categorical", "data_category": "text", "values": ["高中", "中专", "大专", "本科", "硕士", "博士", "MBA"]}}, "转播平台": {"generator_type": "categorical", "params": {"values": ["CCTV-5", "CCTV-5+", "北京卫视", "上海卫视", "广东体育", "五星体育", "腾讯体育", "爱奇艺体育", "咪咕视频", "优酷体育", "哔哩哔哩"]}, "data_category": "text"}, "奖金": {"generator_type": "numerical_range_formatted", "params": {"min_value": 50, "max_value": 5000, "format_string": "{:,}万"}, "data_category": "numeric"}}, "company": {"display_name": "公司信息", "table_title_template": "公司基本信息表", "columns": {"公司名称": {"generator_type": "faker_company", "data_category": "text", "params": {"locale": "zh_CN"}}, "行业": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["互联网/IT", "金融/银行", "医疗/健康", "教育/培训", "制造业", "零售/电商", "农业/食品", "能源/矿产", "建筑/房地产", "传媒/广告", "文化/艺术", "物流/运输", "旅游/餐饮", "咨询/法律", "汽车/交通", "通信/电子", "服装/奢侈品", "软件开发", "云计算/大数据", "人工智能"]}}, "成立时间": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 1990, "end_year": 2022, "format": "%Y-%m-%d"}}, "员工数量": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["10-50人", "50-100人", "100-500人", "500-1000人", "1000-5000人", "5000-10000人", "10000人以上"]}}, "注册资本": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 100, "max_value": 10000, "format_string": "{:,}万"}}, "公司地址": {"generator_type": "faker_address", "data_category": "text", "params": {"locale": "zh_CN"}}, "经营范围": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["软件开发、技术咨询、系统集成", "互联网信息服务、数据处理", "金融投资、资产管理、风险评估", "医疗器械、生物技术、药品研发", "教育培训、文化传播、学术交流", "生产制造、工业设计、质量检测", "商品零售、网上贸易、进出口业务", "农产品加工、食品生产、饮料制造", "能源开发、电力设备、环保技术", "房地产开发、建筑设计、装饰工程", "广告设计、媒体运营、出版发行", "文艺创作、艺术展览、文化活动"]}}, "联系电话": {"generator_type": "faker_phone_number", "data_category": "text", "params": {"locale": "zh_CN"}}, "电子邮箱": {"generator_type": "faker_email", "data_category": "text", "params": {"locale": "zh_CN"}}, "法定代表人": {"generator_type": "faker_name", "data_category": "text", "params": {"locale": "zh_CN"}}, "企业类型": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["有限责任公司", "股份有限公司", "合伙企业", "独资企业", "外资企业", "国有企业", "集体企业", "私营企业"]}}, "上市状态": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["未上市", "已上市", "新三板", "科创板", "主板", "创业板", "退市", "筹备上市", "香港上市", "美国上市"]}}, "年营业额": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 100, "max_value": 100000, "format_string": "{:,}万"}}, "公司网站": {"generator_type": "categorical_with_pattern", "data_category": "text", "params": {"prefixes": ["www.", ""], "suffixes": [".com", ".cn", ".net", ".com.cn", ".org", ".tech", ".co"]}}}}, "environment": {"display_name": "环境数据", "table_title_template": "环境监测数据表", "columns": {"监测项目": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["空气质量", "水质", "噪声", "辐射", "土壤", "固体废物", "森林覆盖率", "碳排放", "温室气体", "绿化率", "生物多样性"]}}, "监测地点": {"generator_type": "categorical_with_pattern", "data_category": "text", "params": {"prefixes": {"type": "common_data", "key": "major_cities"}, "suffixes": ["中心城区", "工业园区", "生态保护区", "水源地", "居民区", "郊区", "农业区"]}}, "监测时间": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2020, "end_year": 2023, "format": "%Y-%m-%d"}}, "污染程度": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["轻度污染", "中度污染", "重度污染", "严重污染", "无污染", "临界值", "达标", "超标"]}}, "指标值": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 0, "max_value": 500, "decimals": 2, "format_string": "{:.2f}"}}, "执行标准": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["国家标准", "行业标准", "地方标准", "企业标准", "国际标准", "欧盟标准", "美国标准", "日本标准"]}}, "治理措施": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["源头控制", "过程管理", "末端治理", "综合治理", "生态修复", "清洁生产", "循环利用", "监测预警"]}}, "治理效果": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["显著改善", "逐步改善", "局部改善", "无明显改善", "持续恶化", "反复波动", "长期稳定", "季节性变化"]}}, "负责单位": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["环保局", "生态环境局", "水务局", "林业局", "农业农村局", "住建局", "规划局", "自然资源局", "气象局"]}}, "投入资金": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 100, "max_value": 10000, "format_string": "{:,}万"}}, "环境容量": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 1000, "max_value": 100000, "format_string": "{:,}吨"}}, "监测频率": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["实时监测", "每日监测", "每周监测", "每月监测", "季度监测", "半年监测", "年度监测", "不定期监测"]}}}}, "financial": {"display_name": "金融数据", "table_title_template": "金融市场数据表", "columns": {"证券名称": {"generator_type": "categorical_with_pattern", "data_category": "text", "params": {"prefixes": ["中国", "建设", "工商", "农业", "交通", "招商", "民生", "兴业", "浦发", "广发"], "suffixes": ["银行", "证券", "保险", "信托", "投资", "地产", "科技", "电力", "石油", "钢铁"]}}, "证券代码": {"generator_type": "alphanumeric_pattern", "data_category": "text", "params": {"patterns": ["60####", "00####", "30####", "688###", "83####"]}}, "交易日期": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2022, "end_year": 2023, "format": "%Y-%m-%d"}}, "开盘价": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 5.0, "max_value": 100.0, "decimals": 2, "format_string": "{:.2f}"}}, "收盘价": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 5.0, "max_value": 100.0, "decimals": 2, "format_string": "{:.2f}"}}, "最高价": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 5.0, "max_value": 100.0, "decimals": 2, "format_string": "{:.2f}"}}, "最低价": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 5.0, "max_value": 100.0, "decimals": 2, "format_string": "{:.2f}"}}, "成交量": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 1000000, "max_value": 100000000, "format_string": "{:,}"}}, "成交额": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 10000000, "max_value": 1000000000, "format_string": "{:,}"}}, "涨跌幅": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": -10.0, "max_value": 10.0, "decimals": 2, "format_string": "{:.2f}%"}}, "市盈率": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 5.0, "max_value": 100.0, "decimals": 2, "format_string": "{:.2f}"}}, "交易所": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["上海证券交易所", "深圳证券交易所", "北京证券交易所", "香港交易所", "纽约证券交易所", "纳斯达克", "伦敦证券交易所", "东京证券交易所", "新加坡证券交易所", "法兰克福证券交易所"]}}, "行业分类": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["金融业", "房地产业", "信息技术", "医药健康", "能源", "原材料", "必需消费品", "可选消费品", "工业", "公用事业"]}}, "股息率": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 0.0, "max_value": 10.0, "decimals": 2, "format_string": "{:.2f}%"}}, "总市值": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 1000000000, "max_value": 10000000000000, "format_string": "{:,}"}}}}, "travel": {"display_name": "旅游数据", "table_title_template": "旅游景点数据表", "columns": {"景点名称": {"generator_type": "categorical_with_pattern", "data_category": "text", "params": {"prefixes": {"type": "common_data", "key": "major_cities"}, "suffixes": ["故宫", "长城", "博物馆", "动物园", "植物园", "公园", "湖", "山", "寺", "塔", "广场", "海滩", "古镇"]}}, "所在地": {"generator_type": "categorical", "data_category": "text", "params": {"values": {"type": "common_data", "key": "cities"}}}, "景区等级": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["5A级", "4A级", "3A级", "2A级", "1A级", "非A级", "世界遗产", "国家公园", "国家风景名胜区", "国家森林公园", "国家地质公园"]}}, "门票价格": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 0, "max_value": 300, "format_string": "{:d}"}}, "游客数量": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 1, "max_value": 100, "format_string": "{:d}万人次/年"}}, "开放时间": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["08:00-17:00", "08:30-17:30", "09:00-18:00", "09:00-17:00", "全天开放", "08:00-18:00", "08:00-20:00", "09:00-16:00"]}}, "建议游玩时间": {"generator_type": "integer_range_with_unit", "data_category": "numeric", "params": {"min": 2, "max": 8, "unit": "小时"}}, "景点类型": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["自然风光", "历史遗迹", "博物馆", "主题公园", "休闲娱乐", "宗教场所", "民俗文化", "现代建筑", "古建筑群", "海滩海岛"]}}, "最佳季节": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["春季", "夏季", "秋季", "冬季", "全年", "春夏", "秋冬", "春秋", "3-5月", "6-8月", "9-11月", "12-2月"]}}, "交通方式": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["公交", "地铁", "出租车", "自驾", "景区巴士", "步行", "高铁", "飞机", "多种方式", "轮船"]}}, "住宿选择": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["五星级酒店", "四星级酒店", "三星级酒店", "经济型酒店", "精品酒店", "主题酒店", "度假村", "民宿", "客栈", "青年旅舍"]}}, "特色体验": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["温泉泡浴", "滑雪", "冲浪", "潜水", "攀岩", "徒步", "骑马", "骑骆驼", "热气球", "直升机观光", "游船", "漂流", "滑翔伞", "蹦极", "跳伞"]}}, "旅游评分": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 3.5, "max_value": 5.0, "decimals": 1, "format_string": "{:.1f}分"}}, "参观日期": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2022, "end_year": 2024, "format": "%Y-%m-%d"}}}}, "food": {"display_name": "餐饮美食", "table_title_template": "餐饮数据统计表", "columns": {"餐厅名称": {"generator_type": "categorical_with_pattern", "data_category": "text", "params": {"prefixes": ["金牌", "老字号", "顶级", "传统", "经典", "米其林", "皇家", "御膳", "家常", "特色", "正宗", "地道"], "suffixes": ["小馆", "酒楼", "餐厅", "饭店", "大厨", "食府", "烧烤", "火锅", "川菜", "粤菜", "湘菜", "鲁菜", "徽菜", "闽菜", "浙菜", "苏菜"]}}, "菜系": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["川菜", "粤菜", "湘菜", "鲁菜", "徽菜", "闽菜", "浙菜", "苏菜", "京菜", "沪菜", "东北菜", "西北菜", "云南菜", "贵州菜", "客家菜", "潮汕菜", "江西菜", "上海菜", "台湾菜", "西餐"]}}, "特色菜品": {"generator_type": "categorical_with_pattern", "data_category": "text", "params": {"prefixes": ["红烧", "清蒸", "酱爆", "香煎", "糖醋", "麻辣", "干煸", "水煮", "爆炒", "红焖"], "suffixes": ["鱼", "肉", "牛排", "排骨", "虾", "鸡", "鸭", "豆腐", "茄子", "青菜"]}}, "平均价格": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 30, "max_value": 500, "format_string": "{:d}"}}, "口味评分": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 7.0, "max_value": 9.9, "decimals": 1, "format_string": "{:.1f}"}}, "环境评分": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 7.0, "max_value": 9.9, "decimals": 1, "format_string": "{:.1f}"}}, "服务评分": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 7.0, "max_value": 9.9, "decimals": 1, "format_string": "{:.1f}"}}, "位置": {"generator_type": "faker_address", "data_category": "text", "params": {"locale": "zh_CN"}}, "营业时间": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["10:00-22:00", "11:00-21:00", "11:30-22:30", "10:30-22:30", "11:00-14:00, 17:00-22:00", "11:30-14:30, 17:30-22:30", "全天营业", "24小时营业", "09:00-21:00", "08:00-20:00"]}}, "菜品数量": {"generator_type": "integer_range", "data_category": "numeric", "params": {"min": 10, "max": 200}}, "推荐指数": {"generator_type": "percentage", "data_category": "numeric", "params": {"min_value": 60, "max_value": 99, "decimals": 0}}, "排队时间": {"generator_type": "integer_range_with_unit", "data_category": "numeric", "params": {"min": 0, "max": 120, "unit": "分钟"}}, "人均消费": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 50, "max_value": 1000, "format_string": "{:d}"}}, "上菜时间": {"generator_type": "integer_range_with_unit", "data_category": "numeric", "params": {"min": 5, "max": 30, "unit": "分钟"}}}}, "healthcare": {"display_name": "医疗健康", "table_title_template": "医疗健康数据表", "columns": {"医院名称": {"generator_type": "categorical_with_pattern", "params": {"prefixes": ["北京", "上海", "广州", "深圳", "成都", "武汉", "杭州", "重庆", "西安", "南京", "天津", "苏州", "青岛", "郑州", "长沙", "合肥", "无锡", "宁波", "福州", "厦门", "南昌", "济南", "大连", "哈尔滨", "长春", "沈阳", "石家庄", "太原", "呼和浩特", "乌鲁木齐", "兰州", "西宁", "银川", "贵阳", "昆明", "南宁", "海口", "拉萨", "香港", "澳门", "东莞", "佛山", "惠州", "珠海", "中山", "汕头", "江门", "湛江", "茂名", "肇庆", "阳江", "清远", "韶关", "梅州", "揭阳", "汕尾", "潮州", "榆林", "宝鸡", "咸阳", "安阳", "洛阳", "焦作", "商丘", "驻马店", "南阳", "信阳", "新乡", "常州", "徐州", "连云港", "南通", "扬州", "镇江", "盐城", "泰州", "宿迁", "嘉兴", "绍兴", "金华", "台州", "丽水", "温州", "湖州", "衢州", "舟山", "上饶", "赣州", "九江", "宜春", "景德镇", "萍乡", "新余", "鹰潭", "株洲", "湘潭", "岳阳", "常德", "衡阳", "邵阳", "益阳"], "suffixes": ["人民医院", "中医院", "第一医院", "第二医院", "医科大学附属医院", "协和医院", "同济医院", "中西医结合医院"]}, "data_category": "text"}, "科室": {"generator_type": "categorical", "params": {"values": ["内科", "外科", "妇产科", "儿科", "眼科", "耳鼻喉科", "口腔科", "皮肤科", "神经科", "心脏科", "呼吸科", "消化科", "泌尿科", "骨科", "肿瘤科", "精神科", "心血管内科", "血液科", "内分泌科", "风湿免疫科", "传染科", "肾内科", "急诊科", "重症医学科", "麻醉科", "放射科", "超声科", "检验科", "病理科", "康复科", "整形外科", "介入科", "中医科", "针灸科", "推拿科", "营养科", "疼痛科", "高压氧科", "核医学科", "生殖医学科", "老年医学科", "儿童心理科", "运动医学科", "变态反应科", "临床心理科", "健康管理科", "肝胆外科", "胃肠外科", "血管外科", "甲状腺外科", "乳腺外科", "胸外科", "神经外科", "小儿外科", "烧伤科", "美容科", "口腔正畸科", "口腔修复科", "口腔种植科", "前列腺科", "男科", "脊柱外科", "手外科", "足踝外科", "关节外科", "创伤骨科", "医学影像科", "分子影像科", "PET-CT科", "磁共振科", "心电图科", "脑电图科", "神经电生理科", "物理治疗科", "作业治疗科", "言语治疗科", "中西医结合科", "中医骨伤科", "中医妇科", "中医儿科", "中医肿瘤科", "全科医学科"]}, "data_category": "text"}, "医生": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "职称": {"generator_type": "categorical", "params": {"values": ["主任医师", "副主任医师", "主治医师", "住院医师", "实习医师", "专家", "特聘专家", "院士", "教授", "副教授", "讲师", "进修医师", "研究员", "高级职称", "中级职称", "初级职称", "特需专家", "知名专家", "首席专家", "客座专家", "名誉主任", "客座教授", "特聘教授", "博士生导师", "硕士生导师"]}, "data_category": "text"}, "患者人数": {"generator_type": "integer_range", "params": {"min": 10, "max": 500}, "data_category": "numeric"}, "平均诊疗时间": {"generator_type": "integer_range_with_unit", "params": {"min": 5, "max": 50, "unit": "分钟"}, "data_category": "numeric"}, "治愈率": {"generator_type": "percentage", "params": {"min_value": 70, "max_value": 99, "decimals": 1}, "data_category": "numeric"}, "常见疾病": {"generator_type": "categorical", "params": {"values": ["高血压", "糖尿病", "冠心病", "肺炎", "胃炎", "哮喘", "肾炎", "肝炎", "脑梗", "骨折", "关节炎", "皮炎", "抑郁症", "焦虑症", "失眠症", "痛风", "甲亢", "甲减", "结石", "脂肪肝", "脊椎病", "颈椎病", "腰椎间盘突出", "白内障", "青光眼", "中风", "心肌梗死", "胆囊炎", "阑尾炎", "胃溃疡", "十二指肠溃疡", "过敏性鼻炎", "慢性支气管炎", "肺结核", "慢性阻塞性肺疾病", "肝硬化", "肾衰竭", "贫血", "白血病", "淋巴瘤", "乳腺癌", "肺癌", "肝癌", "胃癌", "结肠癌", "前列腺癌", "子宫内膜癌", "卵巢癌", "骨质疏松症", "风湿病", "帕金森病", "阿尔茨海默病", "多发性硬化症", "脑膜炎", "脑炎", "癫痫", "偏头痛", "三叉神经痛", "面肌痉挛", "眩晕症", "耳鸣", "听力下降", "鼻窦炎", "扁桃体炎", "咽喉炎", "口腔溃疡", "牙周炎", "龋齿", "视网膜病变", "黄斑变性", "飞蚊症", "近视", "远视", "散光", "弱视", "斜视", "心律失常", "心肌炎", "心包炎", "主动脉瘤", "血栓", "静脉曲张", "动脉硬化", "支气管扩张", "肺气肿", "肺纤维化", "肺栓塞", "胸腔积液", "消化性溃疡", "胃食管反流", "胃下垂", "胰腺炎", "克罗恩病", "溃疡性结肠炎", "肠易激综合征", "便秘", "痔疮", "肛裂", "尿路感染", "泌尿系结石", "前列腺炎", "前列腺增生", "精索静脉曲张", "阳痿", "早泄", "子宫肌瘤", "子宫内膜异位", "卵巢囊肿", "阴道炎", "盆腔炎", "乳腺增生", "膀胱炎", "尿失禁", "系统性红斑狼疮", "类风湿关节炎", "强直性脊柱炎", "红斑狼疮", "银屑病", "湿疹", "荨麻疹", "痤疮", "鱼鳞病", "白癜风", "黑色素瘤", "带状疱疹", "疥疮", "灰指甲"]}, "data_category": "text"}}}, "technology": {"display_name": "科技创新", "table_title_template": "科技项目数据表", "columns": {"项目名称": {"generator_type": "categorical_with_pattern", "params": {"prefixes": ["智能", "云计算", "区块链", "人工智能", "物联网", "大数据", "5G", "虚拟现实", "量子", "机器学习", "智能", "云计算", "区块链", "人工智能", "物联网", "大数据", "5G", "虚拟现实", "量子", "机器学习", "边缘计算", "深度学习", "自动驾驶", "脑机接口", "数字孪生", "绿色能源", "低碳技术", "智慧城市", "无人系统", "生物识别"], "suffixes": ["平台", "系统", "解决方案", "框架", "技术", "应用", "网络", "产品", "生态", "平台", "系统", "管理平台", "解决方案", "中台", "门户", "服务框架", "引擎", "工具集", "网络", "集成平台", "运营平台", "管控系统", "开发框架", "云服务", "生态系统", "智能体", "模型库", "算法平台", "监测系统"]}, "data_category": "text"}, "研发单位": {"generator_type": "faker_company", "params": {"locale": "zh_CN"}, "data_category": "text"}, "技术领域": {"generator_type": "categorical", "params": {"values": ["信息技术", "生物技术", "新材料", "新能源", "智能制造", "航空航天", "医疗健康", "环境保护", "农业科技", "海洋技术", "人工智能", "大数据分析", "云计算", "物联网", "区块链", "量子计算", "5G通信", "6G研究", "虚拟现实", "增强现实", "混合现实", "脑机接口", "基因编辑", "干细胞研究", "合成生物学", "微纳技术", "光电技术", "超导材料", "石墨烯应用", "高分子材料", "生物材料", "智能机器人", "无人驾驶", "智慧城市", "智能电网", "网络安全", "太空探索", "卫星通信", "绿色能源", "能源存储", "智慧农业", "精准医疗", "远程医疗", "环境监测", "污染治理", "循环经济", "智能交通", "数字孪生", "边缘计算", "可穿戴设备", "深度学习", "机器学习", "强化学习", "自然语言处理", "计算机视觉", "语音识别", "生物信息学", "蛋白质组学", "药物发现", "纳米技术", "金属有机框架", "复合材料", "智能材料", "仿生材料", "氢能源", "太阳能发电", "风能发电", "生物质能", "潮汐能", "地热能", "核聚变", "工业4.0", "智能工厂", "3D打印", "增材制造", "航天飞机", "探月工程", "火星探测", "深空探测", "卫星导航", "再生医学", "生物打印", "免疫疗法", "生物传感器", "碳捕获", "水处理技术", "土壤修复", "精准农业", "垂直农业", "植物工厂", "海水淡化", "深海采矿", "海洋能源", "量子通信", "量子密码", "量子传感"]}, "data_category": "text"}, "研发状态": {"generator_type": "categorical", "params": {"values": ["概念阶段", "立项阶段", "研发中", "测试中", "试运行", "已完成", "已商用", "迭代升级", "终止", "可行性研究", "需求分析", "方案设计", "原型开发", "技术验证", "小规模试验", "大规模测试", "beta测试", "内部测试", "用户测试", "生产准备", "初步上线", "全面上线", "市场推广", "持续优化", "版本迭代", "项目暂停", "重新规划", "技术瓶颈", "寻求合作", "专利申请中", "资金筹集中", "团队组建中", "项目重组", "成果转化", "商业化运营"]}, "data_category": "text"}, "开始时间": {"generator_type": "date_range", "params": {"start_year": 2019, "end_year": 2023, "format": "%Y-%m-%d"}, "data_category": "date"}, "团队人数": {"generator_type": "integer_range", "params": {"min": 5, "max": 100}, "data_category": "numeric"}, "预期成果": {"generator_type": "categorical", "params": {"values": ["专利技术", "软件著作权", "技术标准", "学术论文", "产品原型", "商业产品", "技术报告", "解决方案", "开源框架", "实用新型专利", "外观设计专利", "国际专利", "核心算法", "技术白皮书", "研究报告", "数据模型", "行业标准", "国家标准", "国际标准", "示范工程", "产业应用", "系统平台", "应用程序", "企业服务", "消费级产品", "工业级产品", "技术咨询", "技术服务", "科研基地", "人才培养", "产学研合作", "技术转让", "衍生企业", "产业孵化", "创新生态"]}, "data_category": "text"}}}}, "company": {"display_name": "公司信息", "table_title_template": "公司基本信息表", "columns": {"公司名称": {"generator_type": "faker_company", "params": {"locale": "zh_CN"}, "data_category": "text"}, "行业": {"generator_type": "categorical", "params": {"values": ["互联网/IT", "金融/银行", "医疗/健康", "教育/培训", "制造业", "零售/电商", "农业/食品", "能源/矿产", "建筑/房地产", "传媒/广告", "文化/艺术", "物流/运输", "旅游/餐饮", "咨询/法律", "汽车/交通", "通信/电子", "服装/奢侈品", "软件开发", "云计算/大数据", "人工智能", "区块链", "游戏", "电子商务", "社交媒体", "数字营销", "移动互联网", "智能硬件", "生物医药", "医疗器械", "健康管理", "养老服务", "幼儿教育", "K12教育", "高等教育", "职业培训", "在线教育", "机械制造", "电子制造", "化工原料", "纺织服装", "食品加工", "家具制造", "新能源汽车", "光伏产业", "风能产业", "石油天然气", "煤炭开采", "金属矿产", "商业地产", "住宅地产", "工业地产", "建筑设计", "装饰装修", "工程咨询", "电视传媒", "广播传媒", "新媒体", "出版业", "广告创意", "公关服务", "演出娱乐", "博物馆", "艺术品", "快递物流", "供应链", "仓储服务", "航空运输", "铁路运输", "水路运输", "酒店", "景区", "旅行社", "餐饮服务", "咖啡厅", "酒吧", "管理咨询", "法律服务", "会计审计", "税务服务", "知识产权", "汽车制造", "汽车零部件", "汽车销售", "航天航空", "军工", "环保产业", "水务", "垃圾处理", "可再生能源", "电信运营", "通信设备", "半导体", "集成电路", "电子元器件", "奢侈品", "时尚零售", "化妆品", "珠宝首饰"]}, "data_category": "text"}, "成立时间": {"generator_type": "date_range", "params": {"start_year": 1990, "end_year": 2022, "format": "%Y-%m-%d"}, "data_category": "date"}, "员工数量": {"generator_type": "categorical", "params": {"values": ["10-50人", "50-100人", "100-500人", "500-1000人", "1000-5000人", "5000-10000人", "10000人以上"]}, "data_category": "text"}, "注册资本": {"generator_type": "numerical_range_formatted", "params": {"min_value": 100, "max_value": 10000, "format_string": "{:,}万"}, "data_category": "numeric"}, "公司地址": {"generator_type": "faker_address", "params": {"locale": "zh_CN"}, "data_category": "text"}, "经营范围": {"generator_type": "categorical", "params": {"values": ["软件开发、技术咨询、系统集成", "互联网信息服务、数据处理", "金融投资、资产管理、风险评估", "医疗器械、生物技术、药品研发", "教育培训、文化传播、学术交流", "生产制造、工业设计、质量检测", "商品零售、网上贸易、进出口业务", "农产品加工、食品生产、饮料制造", "能源开发、电力设备、环保技术", "房地产开发、建筑设计、装饰工程", "广告设计、媒体运营、出版发行", "文艺创作、艺术展览、文化活动"]}, "data_category": "text"}, "联系电话": {"generator_type": "faker_phone_number", "params": {"locale": "zh_CN"}, "data_category": "text"}, "电子邮箱": {"generator_type": "faker_email", "params": {"locale": "zh_CN"}, "data_category": "text"}, "法定代表人": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "企业类型": {"generator_type": "categorical", "params": {"values": ["有限责任公司", "股份有限公司", "合伙企业", "独资企业", "外资企业", "国有企业", "集体企业", "私营企业"]}, "data_category": "text"}}}, "environment": {"display_name": "环境数据", "table_title_template": "环境监测数据表", "columns": {"监测项目": {"generator_type": "categorical", "params": {"values": ["空气质量", "水质", "噪声", "辐射", "土壤", "固体废物", "森林覆盖率", "碳排放", "温室气体", "绿化率", "生物多样性"]}, "data_category": "text"}, "监测地点": {"generator_type": "categorical_with_pattern", "params": {"prefixes": {"type": "common_data", "key": "major_cities"}, "suffixes": ["中心城区", "工业园区", "生态保护区", "水源地", "居民区", "郊区", "农业区"]}, "data_category": "text"}, "监测时间": {"generator_type": "date_range", "params": {"start_year": 2020, "end_year": 2023, "format": "%Y-%m-%d"}, "data_category": "date"}, "污染程度": {"generator_type": "categorical", "params": {"values": ["轻度污染", "中度污染", "重度污染", "严重污染", "无污染", "临界值", "达标", "超标"]}, "data_category": "text"}, "指标值": {"generator_type": "numerical_range_formatted", "params": {"min_value": 0, "max_value": 500, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}, "执行标准": {"generator_type": "categorical", "params": {"values": ["国家标准", "行业标准", "地方标准", "企业标准", "国际标准", "欧盟标准", "美国标准", "日本标准"]}, "data_category": "text"}, "治理措施": {"generator_type": "categorical", "params": {"values": ["源头控制", "过程管理", "末端治理", "综合治理", "生态修复", "清洁生产", "循环利用", "监测预警"]}, "data_category": "text"}, "治理效果": {"generator_type": "categorical", "params": {"values": ["显著改善", "逐步改善", "局部改善", "无明显改善", "持续恶化", "反复波动", "长期稳定", "季节性变化"]}, "data_category": "text"}, "负责单位": {"generator_type": "categorical", "params": {"values": ["环保局", "生态环境局", "水务局", "林业局", "农业农村局", "住建局", "规划局", "自然资源局", "气象局"]}, "data_category": "text"}, "投入资金": {"generator_type": "numerical_range_formatted", "params": {"min_value": 100, "max_value": 10000, "format_string": "{:,}万"}, "data_category": "numeric"}}}, "financial": {"display_name": "金融数据", "table_title_template": "金融市场数据表", "columns": {"证券名称": {"generator_type": "categorical_with_pattern", "params": {"prefixes": ["中国", "建设", "工商", "农业", "交通", "招商", "民生", "兴业", "浦发", "广发", "平安", "中信"], "suffixes": ["银行", "证券", "保险", "信托", "投资", "地产", "科技", "电力", "石油", "钢铁", "煤炭", "黄金", "期货", "控股", "资源", "集团", "基金", "财险", "人寿", "资产", "金控", "租赁", "债券", "指数", "医药", "白酒", "汽车", "互联网", "软件", "通信", "机械", "化工", "建材", "有色", "食品", "券商", "新能源", "物业", "物流", "航空", "航运", "港口", "高铁", "水务", "燃气", "环保", "医疗", "教育"]}, "data_category": "text"}, "证券代码": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["60####", "00####", "30####", "688###", "83####", "43####", "15####", "16####"]}, "data_category": "text"}, "交易日期": {"generator_type": "date_range", "params": {"start_year": 2022, "end_year": 2023, "format": "%Y-%m-%d"}, "data_category": "date"}, "开盘价": {"generator_type": "numerical_range_formatted", "params": {"min_value": 5.0, "max_value": 100.0, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}, "收盘价": {"generator_type": "numerical_range_formatted", "params": {"min_value": 5.0, "max_value": 100.0, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}, "最高价": {"generator_type": "numerical_range_formatted", "params": {"min_value": 5.0, "max_value": 100.0, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}, "最低价": {"generator_type": "numerical_range_formatted", "params": {"min_value": 5.0, "max_value": 100.0, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}, "成交量": {"generator_type": "numerical_range_formatted", "params": {"min_value": 1000000, "max_value": 100000000, "format_string": "{:,}"}, "data_category": "numeric"}, "成交额": {"generator_type": "numerical_range_formatted", "params": {"min_value": 10000000, "max_value": 1000000000, "format_string": "{:,}"}, "data_category": "numeric"}, "涨跌幅": {"generator_type": "numerical_range_formatted", "params": {"min_value": -10.0, "max_value": 10.0, "decimals": 2, "format_string": "{:.2f}%"}, "data_category": "numeric"}, "市盈率": {"generator_type": "numerical_range_formatted", "params": {"min_value": 5.0, "max_value": 100.0, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}, "交易所": {"generator_type": "categorical", "params": {"values": ["上海证券交易所", "深圳证券交易所", "北京证券交易所", "香港交易所", "纽约证券交易所", "纳斯达克", "伦敦证券交易所", "东京证券交易所", "新加坡证券交易所", "法兰克福证券交易所", "多伦多证券交易所", "悉尼证券交易所", "印度国家证券交易所", "韩国证券交易所", "台湾证券交易所", "泰国证券交易所", "巴西证券交易所", "莫斯科证券交易所", "约翰内斯堡证券交易所", "迪拜金融市场", "沪港通", "深港通", "港股通", "科创板", "创业板", "新三板", "主板", "中小板"]}, "data_category": "text"}, "行业分类": {"generator_type": "categorical", "params": {"values": ["金融业", "房地产业", "信息技术", "医药健康", "能源", "原材料", "必需消费品", "可选消费品", "工业", "公用事业", "通信服务", "交通运输", "建筑业", "批发零售", "传媒", "电力设备", "汽车", "机械设备", "家用电器", "食品饮料", "农林牧渔", "纺织服装", "商业贸易", "银行", "非银金融", "保险", "证券", "多元金融", "石油石化", "煤炭", "有色金属", "钢铁", "建材", "化工", "电力", "燃气", "水务", "环保", "酒店旅游", "餐饮", "教育", "电子", "半导体", "计算机软件", "通信设备", "互联网", "医疗器械", "医药生物", "生物科技", "医疗服务", "中药", "新能源汽车", "光伏", "风电", "储能", "军工", "航空航天", "航运", "港口", "高速公路", "铁路运输", "物流", "房地产开发", "物业管理"]}, "data_category": "text"}, "板块": {"generator_type": "categorical", "params": {"values": ["主板", "创业板", "科创板", "中小板", "新三板", "港股", "美股", "A股", "B股", "H股", "蓝筹股", "白马股", "绩优股", "高送转", "高股息", "壳资源", "ST股", "*ST股", "次新股", "龙头股", "权重股", "周期股", "成长股", "价值股", "防御型股票", "大盘股", "中盘股", "小盘股", "ETF", "LOF", "QDII", "REITs", "分级基金", "货币基金", "债券基金", "股票基金", "混合基金", "指数基金", "国企改革", "一带一路", "新基建", "智能制造", "数字经济", "军民融合", "碳中和", "元宇宙", "芯片", "人工智能", "大数据", "云计算", "物联网", "区块链", "生物医药", "新材料", "高端装备", "智能汽车", "消费升级", "农村振兴", "养老产业", "环保产业", "节能减排"]}, "data_category": "text"}, "交易量级别": {"generator_type": "categorical", "params": {"values": ["极低", "低", "中低", "中等", "中高", "高", "极高", "活跃", "不活跃", "冷清", "热门", "大宗交易", "大额交易", "小额交易", "零星交易", "集中交易", "分散交易", "溢价交易", "折价交易", "平价交易", "机构交易", "散户交易", "北向资金", "南向资金", "融资买入", "融券卖出", "减持", "增持", "回购", "定增", "配股", "分红送转", "大幅波动", "低迷", "高开低走", "低开高走", "震荡上行", "震荡下行", "横盘整理", "突破新高"]}, "data_category": "text"}}}, "travel": {"display_name": "旅游数据", "table_title_template": "旅游景点数据表", "columns": {"景点名称": {"generator_type": "categorical_with_pattern", "params": {"prefixes": {"type": "common_data", "key": "major_cities"}, "suffixes": ["故宫", "长城", "博物馆", "公园", "塔", "湖", "山", "岛", "海滨", "古镇", "古村", "古街", "古巷", "古桥", "古楼", "古庙", "古寺", "古道", "古路", "古城", "古镇", "古村", "古街", "古巷", "古桥", "古楼", "古庙", "古寺", "古道", "古路", "古城"]}, "data_category": "text"}, "艺术家": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "艺术流派": {"generator_type": "categorical", "params": {"values": ["印象派", "表现主义", "抽象主义", "现实主义", "超现实主义", "立体主义", "波普艺术", "极简主义", "后现代主义", "古典主义", "浪漫主义", "野兽派", "达达主义", "未来主义", "观念艺术", "行为艺术", "装置艺术", "数字艺术"]}, "data_category": "text"}, "创作年代": {"generator_type": "date_range", "params": {"start_year": 1900, "end_year": 2023, "format": "%Y年"}, "data_category": "date"}, "作品类型": {"generator_type": "categorical", "params": {"values": ["油画", "水彩画", "素描", "版画", "国画", "书法", "雕塑", "摄影", "装置", "多媒体", "数字艺术", "行为艺术", "综合材料", "陶艺", "壁画"]}, "data_category": "text"}, "尺寸规格": {"generator_type": "dimension_format", "params": {"min_length": 20, "max_length": 300, "min_width": 20, "max_width": 300, "min_height": 0, "max_height": 200, "format_string": "{}×{}cm"}, "data_category": "text"}, "展览地点": {"generator_type": "categorical_with_pattern", "params": {"prefixes": {"type": "common_data", "key": "major_cities"}, "suffixes": ["美术馆", "博物馆", "艺术中心", "画廊", "文化中心", "艺术区", "当代艺术馆"]}, "data_category": "text"}, "估价": {"generator_type": "numerical_range_formatted", "params": {"min_value": 5000, "max_value": 10000000, "format_string": "{:,}"}, "data_category": "numeric"}, "材质": {"generator_type": "categorical", "params": {"values": ["布面油彩", "纸本水彩", "木板油彩", "纸本墨笔", "综合材料", "大理石", "青铜", "不锈钢", "玻璃钢", "陶瓷", "木材", "丙烯颜料", "数字媒体", "纸本版画", "铜版", "宣纸墨色", "丝网印刷"]}, "data_category": "text"}, "收藏情况": {"generator_type": "categorical", "params": {"values": ["私人收藏", "美术馆收藏", "博物馆收藏", "基金会收藏", "企业收藏", "艺术家自持", "待售", "巡展中", "公共艺术", "限时展出", "永久展出"]}, "data_category": "text"}, "创作背景": {"generator_type": "categorical", "params": {"values": ["个人经历", "社会事件", "历史纪念", "文学启发", "音乐灵感", "自然观察", "情感表达", "哲学思考", "政治评论", "文化传承", "实验创新", "委托创作"]}, "data_category": "text"}}}, "realestate": {"display_name": "房地产", "table_title_template": "房产信息数据表", "columns": {"小区名称": {"generator_type": "categorical_with_pattern", "params": {"prefixes": ["龙湖", "万科", "保利", "绿地", "恒大", "碧桂园", "华润", "中海", "融创", "金地", "首开"], "suffixes": ["花园", "公馆", "壹号", "名邸", "府", "园", "湾", "城", "庭", "山", "郡", "领袖"]}, "data_category": "text"}, "所在区域": {"generator_type": "categorical", "params": {"values": ["朝阳区", "海淀区", "东城区", "西城区", "丰台区", "石景山区", "房山区", "通州区", "顺义区", "昌平区", "大兴区", "怀柔区", "密云区", "延庆区"]}, "data_category": "text"}, "建筑面积": {"generator_type": "numerical_range_formatted", "params": {"min_value": 50, "max_value": 300, "format_string": "{:d}㎡"}, "data_category": "numeric"}, "户型": {"generator_type": "categorical", "params": {"values": ["一室一厅", "两室一厅", "两室两厅", "三室一厅", "三室两厅", "四室两厅", "复式", "loft", "跃层", "别墅", "公寓", "商住两用"]}, "data_category": "text"}, "单价": {"generator_type": "numerical_range_formatted", "params": {"min_value": 10000, "max_value": 100000, "format_string": "{:,}/㎡"}, "data_category": "numeric"}, "总价": {"generator_type": "numerical_range_formatted", "params": {"min_value": 100, "max_value": 2000, "format_string": "{:d}万"}, "data_category": "numeric"}, "物业费": {"generator_type": "numerical_range_formatted", "params": {"min_value": 1.5, "max_value": 10.0, "decimals": 1, "format_string": "{:.1f}/㎡/月"}, "data_category": "numeric"}, "建成年份": {"generator_type": "integer_range", "params": {"min": 2000, "max": 2023}, "data_category": "numeric"}, "装修情况": {"generator_type": "categorical", "params": {"values": ["精装修", "豪华装修", "简装", "毛坯", "中等装修", "装修中", "公共部分精装修", "全新装修", "精致装修", "奢华装修", "欧式装修", "现代简约", "北欧风格", "美式风格", "中式风格", "日式风格", "工业风格", "地中海风格", "复古风格", "极简风格", "轻奢风格", "宜家风格", "原木风格", "清水房", "待装修", "二次装修", "旧房翻新", "样板间装修", "品牌装修", "设计师装修", "软装配套", "精装带家具", "整体定制", "智能家居装修", "别墅装修", "局部装修", "装修老旧", "待翻新"]}, "data_category": "text"}, "楼层": {"generator_type": "categorical_with_pattern", "params": {"prefixes": ["", ""], "suffixes": ["低楼层", "中楼层", "高楼层", "顶层", "地下室", "一层", "底商"]}, "data_category": "text"}}}, "food": {"display_name": "餐饮美食", "table_title_template": "餐饮数据统计表", "columns": {"餐厅名称": {"generator_type": "categorical_with_pattern", "params": {"prefixes": ["金牌", "老字号", "顶级", "传统", "经典", "米其林", "皇家", "御膳", "家常", "特色", "正宗", "地道"], "suffixes": ["小馆", "酒楼", "餐厅", "饭店", "大厨", "食府", "烧烤", "火锅", "川菜", "粤菜", "湘菜", "鲁菜", "徽菜", "闽菜", "浙菜", "苏菜"]}, "data_category": "text"}, "菜系": {"generator_type": "categorical", "params": {"values": ["川菜", "粤菜", "湘菜", "鲁菜", "徽菜", "闽菜", "浙菜", "苏菜", "京菜", "沪菜", "东北菜", "西北菜", "云南菜", "贵州菜", "客家菜", "潮汕菜", "江西菜", "上海菜", "台湾菜", "西餐", "日料", "韩餐", "泰国菜", "印度菜", "意大利菜", "法国菜", "地中海料理", "墨西哥菜", "西班牙菜", "希腊菜", "土耳其菜", "黎巴嫩菜", "摩洛哥菜", "越南菜", "马来西亚菜", "新加坡菜", "印尼菜", "菲律宾菜", "缅甸菜", "中东料理", "巴西烤肉", "美式快餐", "英国菜", "俄罗斯菜", "德国菜", "匈牙利菜", "波兰菜", "葡萄牙菜", "荷兰菜", "比利时菜", "瑞典菜", "挪威菜", "丹麦菜", "芬兰菜", "瑞士菜", "奥地利菜", "澳大利亚菜", "新西兰菜", "南非菜", "埃及菜", "尼日利亚菜", "肯尼亚菜", "火锅", "烧烤", "小吃", "素食", "创意菜", "融合菜", "分子料理", "农家菜", "海鲜", "清真菜", "私房菜", "养生菜", "宫廷菜"]}, "data_category": "text"}, "特色菜品": {"generator_type": "categorical_with_pattern", "params": {"prefixes": ["红烧", "清蒸", "酱爆", "香煎", "糖醋", "麻辣", "干煸", "水煮", "爆炒", "红焖", "五香", "酱香", "干锅", "椒盐", "白灼", "清炖", "蜜汁"], "suffixes": ["鱼", "肉", "牛排", "排骨", "虾", "鸡", "鸭", "豆腐", "茄子", "青菜", "土豆", "藕片", "腐竹", "面筋", "年糕", "馒头", "包子", "饺子", "面条", "炒饭"]}, "data_category": "text"}, "平均价格": {"generator_type": "numerical_range_formatted", "params": {"min_value": 30, "max_value": 500, "format_string": "{:d}"}, "data_category": "numeric"}, "口味评分": {"generator_type": "numerical_range_formatted", "params": {"min_value": 7.0, "max_value": 9.9, "decimals": 1, "format_string": "{:.1f}"}, "data_category": "numeric"}, "环境评分": {"generator_type": "numerical_range_formatted", "params": {"min_value": 7.0, "max_value": 9.9, "decimals": 1, "format_string": "{:.1f}"}, "data_category": "numeric"}, "服务评分": {"generator_type": "numerical_range_formatted", "params": {"min_value": 7.0, "max_value": 9.9, "decimals": 1, "format_string": "{:.1f}"}, "data_category": "numeric"}, "位置": {"generator_type": "faker_address", "params": {"locale": "zh_CN"}, "data_category": "text"}, "营业时间": {"generator_type": "categorical", "params": {"values": ["10:00-22:00", "11:00-21:00", "11:30-22:30", "10:30-22:30", "11:00-14:00, 17:00-22:00", "11:30-14:30, 17:30-22:30", "全天营业", "24小时营业", "09:00-21:00", "08:00-20:00"]}, "data_category": "text"}, "菜品数量": {"generator_type": "integer_range", "params": {"min": 10, "max": 200}, "data_category": "numeric"}, "推荐指数": {"generator_type": "percentage", "params": {"min_value": 60, "max_value": 99, "decimals": 0}, "data_category": "numeric"}, "排队时间": {"generator_type": "integer_range_with_unit", "params": {"min": 0, "max": 120, "unit": "分钟"}, "data_category": "numeric"}}}, "weather": {"display_name": "气象数据", "table_title_template": "气象监测数据表", "columns": {"城市": {"generator_type": "categorical", "params": {"values": {"type": "common_data", "key": "cities"}}, "data_category": "text"}, "日期": {"generator_type": "date_range", "params": {"start_year": 2020, "end_year": 2023, "format": "%Y-%m-%d"}, "data_category": "date"}, "最高温度": {"generator_type": "numerical_range_formatted", "params": {"min_value": -10, "max_value": 42, "format_string": "{:d}°C"}, "data_category": "numeric"}, "最低温度": {"generator_type": "numerical_range_formatted", "params": {"min_value": -30, "max_value": 30, "format_string": "{:d}°C"}, "data_category": "numeric"}, "体感温度": {"generator_type": "numerical_range_formatted", "params": {"min_value": -15, "max_value": 45, "format_string": "{:d}°C"}, "data_category": "numeric"}, "天气状况": {"generator_type": "categorical", "params": {"values": ["晴", "多云", "阴", "小雨", "中雨", "大雨", "暴雨", "阵雨", "雷阵雨", "小雪", "中雪", "大雪", "暴雪", "雨夹雪", "冻雨", "雾", "霾", "沙尘暴", "浮尘", "扬沙", "强沙尘暴", "大风", "龙卷风", "冰雹", "热带风暴", "飓风", "台风", "晴转多云", "多云转晴", "阴转小雨", "小雨转中雨", "中雨转大雨", "阵雨转多云", "小雪转中雪", "雷阵雨伴有冰雹", "小到中雨", "中到大雨", "大到暴雨", "暴雨到大暴雨", "大暴雨到特大暴雨", "小到中雪", "中到大雪", "大到暴雪", "浓雾", "强浓雾", "轻雾", "薄雾", "大雾", "特强浓雾", "霜", "结冰", "冰粒", "微风", "疾风", "烈风", "狂风", "热带低压", "强热带风暴", "超强台风", "轻度霾", "中度霾", "重度霾", "严重霾", "晴间多云", "雨雪天气", "阴有小雨", "阵雪", "浮冰", "扬沙转多云"]}, "data_category": "text"}, "降水量": {"generator_type": "numerical_range_formatted", "params": {"min_value": 0, "max_value": 200, "decimals": 1, "format_string": "{:.1f}mm"}, "data_category": "numeric"}, "湿度": {"generator_type": "percentage", "params": {"min_value": 10, "max_value": 99, "decimals": 0}, "data_category": "numeric"}, "风力": {"generator_type": "integer_range_with_unit", "params": {"min": 0, "max": 12, "unit": "级"}, "data_category": "numeric"}, "风向": {"generator_type": "categorical", "params": {"values": ["东风", "南风", "西风", "北风", "东北风", "东南风", "西南风", "西北风", "微风", "无持续风向"]}, "data_category": "text"}, "气压": {"generator_type": "numerical_range_formatted", "params": {"min_value": 950, "max_value": 1050, "format_string": "{:d}hPa"}, "data_category": "numeric"}, "能见度": {"generator_type": "numerical_range_formatted", "params": {"min_value": 0.1, "max_value": 30, "decimals": 1, "format_string": "{:.1f}km"}, "data_category": "numeric"}}}, "music": {"display_name": "音乐数据", "table_title_template": "音乐作品数据表", "columns": {"歌曲名称": {"generator_type": "categorical_with_pattern", "params": {"prefixes": ["夜的", "爱的", "心的", "梦中", "青春", "永恒", "回忆", "思念", "孤独", "快乐", "忧伤", "希望"], "suffixes": ["旋律", "光芒", "眼泪", "微笑", "记忆", "舞曲", "幻想", "思绪", "旅程", "故事", "声音", "倒影", "节奏"]}, "data_category": "text"}, "歌手": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "专辑名称": {"generator_type": "categorical_with_pattern", "params": {"prefixes": ["新的", "最美的", "永恒的", "我的", "心的", "爱的", "第一", "梦想", "回归", "黑色", "白色", "红色"], "suffixes": ["世界", "旅程", "时光", "季节", "爱情", "记忆", "梦想", "告白", "选择", "勇气", "空间", "宣言"]}, "data_category": "text"}, "发行时间": {"generator_type": "date_range", "params": {"start_year": 2000, "end_year": 2023, "format": "%Y-%m-%d"}, "data_category": "date"}, "音乐风格": {"generator_type": "categorical", "params": {"values": ["流行", "摇滚", "民谣", "电子", "嘻哈", "R&B", "爵士", "古典", "蓝调", "重金属", "朋克", "乡村", "民族", "拉丁", "雷鬼", "世界音乐", "实验音乐", "后摇", "新世纪", "独立音乐"]}, "data_category": "text"}, "时长": {"generator_type": "numerical_range_formatted", "params": {"min_value": 2.0, "max_value": 8.0, "decimals": 2, "format_string": "{:.2f}分钟"}, "data_category": "numeric"}, "播放量": {"generator_type": "numerical_range_formatted", "params": {"min_value": 10000, "max_value": 100000000, "format_string": "{:,}"}, "data_category": "numeric"}, "评分": {"generator_type": "numerical_range_formatted", "params": {"min_value": 6.0, "max_value": 10.0, "decimals": 1, "format_string": "{:.1f}"}, "data_category": "numeric"}, "语言": {"generator_type": "categorical", "params": {"values": ["国语", "粤语", "英语", "日语", "韩语", "法语", "西班牙语", "意大利语", "德语", "拉丁语", "方言", "多语种"]}, "data_category": "text"}, "作曲": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "作词": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "唱片公司": {"generator_type": "categorical", "params": {"values": ["环球音乐", "索尼音乐", "华纳音乐", "摩登天空", "太合音乐", "英皇娱乐", "华研国际", "福茂唱片", "相信音乐", "海蝶音乐", "乐华娱乐", "百纳娱乐", "杰威尔音乐", "奔跑怪物", "听见音乐", "华纳飞碟", "金牌大风"]}, "data_category": "text"}}}, "education_institution": {"display_name": "教育机构", "table_title_template": "教育机构数据表", "columns": {"机构名称": {"generator_type": "categorical_with_pattern", "params": {"prefixes": ["北京", "上海", "广州", "深圳", "杭州", "成都", "武汉", "南京", "西安", "重庆", "新东方", "学而思", "环球", "启德", "博思"], "suffixes": ["教育", "培训中心", "学院", "进修学校", "教育集团", "辅导班", "教育研究院", "国际学校", "少儿英语", "学习中心"]}, "data_category": "text"}, "机构类型": {"generator_type": "categorical", "params": {"values": ["K12培训", "语言培训", "国际教育", "职业培训", "考研培训", "IT培训", "艺术培训", "兴趣培训", "体育培训", "早教中心", "留学机构", "高考补习", "幼儿园", "私立学校", "公立学校", "在线教育"]}, "data_category": "text"}, "成立时间": {"generator_type": "date_range", "params": {"start_year": 2000, "end_year": 2022, "format": "%Y-%m-%d"}, "data_category": "date"}, "校区数量": {"generator_type": "integer_range", "params": {"min": 1, "max": 500}, "data_category": "numeric"}, "师资规模": {"generator_type": "categorical", "params": {"values": ["10人以下", "10-50人", "50-100人", "100-300人", "300-500人", "500-1000人", "1000人以上"]}, "data_category": "text"}, "学员人数": {"generator_type": "numerical_range_formatted", "params": {"min_value": 100, "max_value": 1000000, "format_string": "{:,}人"}, "data_category": "numeric"}, "主要课程": {"generator_type": "categorical", "params": {"values": ["英语", "数学", "语文", "物理", "化学", "生物", "编程", "美术", "音乐", "舞蹈", "体育", "历史", "地理", "政治", "雅思", "托福", "SAT", "AP课程", "IB课程", "A-Level", "留学申请"]}, "data_category": "text"}, "平均班级人数": {"generator_type": "integer_range", "params": {"min": 5, "max": 50}, "data_category": "numeric"}, "教学模式": {"generator_type": "categorical", "params": {"values": ["小班教学", "一对一", "双师模式", "在线直播", "录播课程", "混合式教学", "沉浸式教学", "项目制学习", "实践教学", "游戏化教学", "翻转课堂"]}, "data_category": "text"}, "地址": {"generator_type": "faker_address", "params": {"locale": "zh_CN"}, "data_category": "text"}, "课程费用": {"generator_type": "numerical_range_formatted", "params": {"min_value": 1000, "max_value": 50000, "format_string": "{:,}"}, "data_category": "numeric"}, "学员满意度": {"generator_type": "percentage", "params": {"min_value": 70, "max_value": 99, "decimals": 1}, "data_category": "numeric"}}}, "sports_equipment": {"display_name": "体育器材", "table_title_template": "体育器材数据表", "columns": {"器材名称": {"generator_type": "categorical", "params": {"values": ["篮球", "足球", "排球", "网球拍", "羽毛球拍", "乒乓球拍", "哑铃", "杠铃", "跑步机", "动感单车", "椭圆机", "划船机", "健身球", "瑜伽垫", "拉力器", "仰卧板", "引体向上器", "健腹轮", "深蹲架", "卧推凳", "踏步机", "跳绳", "握力器", "拳击手套", "护具套装"]}, "data_category": "text"}, "品牌": {"generator_type": "categorical", "params": {"values": ["耐克", "阿迪达斯", "李宁", "安踏", "特步", "匹克", "361°", "迪卡侬", "斯伯丁", "威尔胜", "亚瑟士", "美津浓", "FILA", "彪马", "锐步", "双鱼", "红双喜", "尤尼克斯", "川崎", "VICTOR", "汤尤杯", "乔丹", "铁人三项"]}, "data_category": "text"}, "型号": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["X###", "Pro-##", "MAX###", "Air-#", "Plus##", "Neo-##", "Y##X", "Z##", "TY-###"]}, "data_category": "text"}, "价格": {"generator_type": "numerical_range_formatted", "params": {"min_value": 50, "max_value": 10000, "format_string": "{:,}"}, "data_category": "numeric"}, "材质": {"generator_type": "categorical", "params": {"values": ["PU", "PVC", "橡胶", "合成革", "真皮", "尼龙", "碳纤维", "铝合金", "钢铁", "木质", "复合材料", "ABS塑料", "EVA", "聚酯纤维", "棉质", "海绵", "硅胶"]}, "data_category": "text"}, "适用人群": {"generator_type": "categorical", "params": {"values": ["专业运动员", "业余爱好者", "健身爱好者", "初学者", "青少年", "成人", "儿童", "男性", "女性", "老年人", "全民健身", "家庭使用", "商业场所", "训练场地"]}, "data_category": "text"}, "用途": {"generator_type": "categorical", "params": {"values": ["比赛用", "训练用", "娱乐用", "健身用", "家庭健身", "户外运动", "室内运动", "力量训练", "有氧训练", "柔韧性训练", "平衡训练", "协调性训练", "速度训练", "爆发力训练"]}, "data_category": "text"}, "重量": {"generator_type": "numerical_range_formatted", "params": {"min_value": 0.1, "max_value": 200, "decimals": 1, "format_string": "{:.1f}kg"}, "data_category": "numeric"}, "尺寸": {"generator_type": "dimension_format", "params": {"min_length": 10, "max_length": 300, "min_width": 10, "max_width": 200, "min_height": 0, "max_height": 200, "format_string": "{}×{}×{}cm"}, "data_category": "text"}, "颜色": {"generator_type": "categorical", "params": {"values": ["黑色", "白色", "红色", "蓝色", "绿色", "黄色", "橙色", "紫色", "粉色", "灰色", "银色", "金色", "渐变色", "迷彩", "多彩", "荧光色", "透明"]}, "data_category": "text"}, "评分": {"generator_type": "numerical_range_formatted", "params": {"min_value": 3.0, "max_value": 5.0, "decimals": 1, "format_string": "{:.1f}"}, "data_category": "numeric"}}}, "user_info": {"display_name": "用户信息", "table_title_template": "用户基本信息表", "columns": {"用户ID": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["UID####", "U######", "USER_###", "ID_#####"]}, "data_category": "text"}, "用户名": {"generator_type": "categorical_with_pattern", "params": {"prefixes": ["happy", "cool", "super", "crazy", "lovely", "funny", "smart", "bright", "quick", "clever"], "suffixes": ["user", "person", "star", "gamer", "reader", "writer", "player", "fan", "friend", "smile"]}, "data_category": "text"}, "真实姓名": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "性别": {"generator_type": "categorical", "params": {"values": ["男", "女", "未知", "保密"]}, "data_category": "text"}, "年龄": {"generator_type": "integer_range", "params": {"min": 12, "max": 70}, "data_category": "numeric"}, "手机号": {"generator_type": "faker_phone_number", "params": {"locale": "zh_CN"}, "data_category": "text"}, "邮箱": {"generator_type": "faker_email", "params": {"locale": "zh_CN"}, "data_category": "text"}, "注册时间": {"generator_type": "date_range", "params": {"start_year": 2015, "end_year": 2023, "format": "%Y-%m-%d %H:%M:%S"}, "data_category": "date"}, "最后登录时间": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d %H:%M:%S"}, "data_category": "date"}, "会员等级": {"generator_type": "categorical", "params": {"values": ["普通会员", "青铜会员", "白银会员", "黄金会员", "铂金会员", "钻石会员", "VIP会员"]}, "data_category": "text"}, "账户状态": {"generator_type": "categorical", "params": {"values": ["正常", "已冻结", "已封禁", "待验证", "休眠", "注销中", "已注销"]}, "data_category": "text"}, "注册来源": {"generator_type": "categorical", "params": {"values": ["官网", "安卓APP", "iOS APP", "微信小程序", "微博", "QQ", "邮箱", "手机号", "Facebook", "Twitter", "邀请注册", "活动注册"]}, "data_category": "text"}}}, "order_records": {"display_name": "订单记录", "table_title_template": "订单记录数据表", "columns": {"订单编号": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["ORD######", "OD########", "TX########", "ORDER-####"]}, "data_category": "text"}, "用户ID": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["UID####", "U######", "USER_###"]}, "data_category": "text"}, "用户名": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "下单时间": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d %H:%M:%S"}, "data_category": "date"}, "支付时间": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d %H:%M:%S"}, "data_category": "date"}, "订单状态": {"generator_type": "categorical", "params": {"values": ["待支付", "已支付", "已发货", "已签收", "已完成", "已取消", "已退款", "部分退款", "退款中", "交易关闭", "已过期", "待评价"]}, "data_category": "text"}, "支付方式": {"generator_type": "categorical", "params": {"values": ["支付宝", "微信支付", "银联", "信用卡", "储蓄卡", "花呗", "白条", "货到付款", "余额支付", "积分兑换", "优惠券", "组合支付"]}, "data_category": "text"}, "订单金额": {"generator_type": "numerical_range_formatted", "params": {"min_value": 9.9, "max_value": 9999, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}, "优惠金额": {"generator_type": "numerical_range_formatted", "params": {"min_value": 0, "max_value": 100, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}, "实付金额": {"generator_type": "numerical_range_formatted", "params": {"min_value": 9.9, "max_value": 9999, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}, "商品数量": {"generator_type": "integer_range", "params": {"min": 1, "max": 20}, "data_category": "numeric"}, "配送方式": {"generator_type": "categorical", "params": {"values": ["快递配送", "到店自提", "同城配送", "跨境物流", "顺丰速运", "京东物流", "邮政EMS", "圆通速递", "中通快递", "申通快递"]}, "data_category": "text"}, "收货地址": {"generator_type": "faker_address", "params": {"locale": "zh_CN"}, "data_category": "text"}}}, "job_recruitment": {"display_name": "职位招聘", "table_title_template": "职位招聘信息表", "columns": {"职位ID": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["JOB####", "P######", "HR_JD_####"]}, "data_category": "text"}, "职位名称": {"generator_type": "faker_job", "params": {"locale": "zh_CN"}, "data_category": "text"}, "公司名称": {"generator_type": "faker_company", "params": {"locale": "zh_CN"}, "data_category": "text"}, "所属部门": {"generator_type": "categorical", "params": {"values": ["技术部", "产品部", "研发部", "设计部", "运营部", "市场部", "销售部", "客户服务部", "人力资源部", "行政部", "财务部", "法务部", "采购部", "物流部"]}, "data_category": "text"}, "工作地点": {"generator_type": "categorical", "params": {"values": {"type": "common_data", "key": "cities"}}, "data_category": "text"}, "工作性质": {"generator_type": "categorical", "params": {"values": ["全职", "兼职", "实习", "校招", "社招", "项目制", "外包", "劳务派遣", "返聘", "顾问", "志愿者"]}, "data_category": "text"}, "职位类别": {"generator_type": "categorical", "params": {"values": ["技术/开发", "产品经理", "设计师", "测试/运维", "项目经理", "运营专员", "市场营销", "销售代表", "客服专员", "行政/助理", "人事/招聘", "财务/审计", "法务/合规", "高级管理", "教育培训", "翻译", "编辑", "软件工程师", "前端开发", "后端开发", "全栈开发", "移动开发", "嵌入式开发", "算法工程师", "数据分析师", "数据科学家", "人工智能专家", "机器学习工程师", "系统架构师", "技术总监", "CTO", "运维工程师", "网络工程师", "安全工程师", "云计算架构师", "数据库管理员", "UI设计师", "UX设计师", "交互设计师", "视觉设计师", "平面设计师", "网页设计师", "游戏设计师", "产品运营", "内容运营", "社区运营", "新媒体运营", "电商运营", "活动运营", "商务拓展", "广告投放", "SEO专员", "SEM专员", "品牌推广", "公关专员", "媒介经理", "销售经理", "大客户经理", "销售总监", "销售支持", "销售顾问", "电话销售", "培训讲师", "课程设计", "培训管理", "HR经理", "猎头顾问", "人力资源专员", "薪酬福利专员", "绩效考核专员", "财务总监", "财务经理", "会计", "出纳", "税务专员", "审计师", "律师", "法务专员", "合规专员", "知识产权专员", "CEO", "COO", "CFO", "总经理", "副总经理", "总监", "部门经理"]}, "data_category": "text"}, "工作经验": {"generator_type": "categorical", "params": {"values": ["应届毕业生", "1年以下", "1-3年", "3-5年", "5-10年", "10年以上", "经验不限", "在校生", "有相关经验者优先"]}, "data_category": "text"}, "学历要求": {"generator_type": "categorical", "params": {"values": ["高中", "中专", "大专", "本科", "硕士", "博士", "MBA", "学历不限", "985/211优先"]}, "data_category": "text"}, "薪资范围": {"generator_type": "categorical", "params": {"values": ["面议", "3k-5k", "5k-8k", "8k-12k", "12k-15k", "15k-20k", "20k-30k", "30k-50k", "50k以上", "年薪制", "保密", "底薪+提成"]}, "data_category": "text"}, "招聘人数": {"generator_type": "integer_range", "params": {"min": 1, "max": 50}, "data_category": "numeric"}, "发布时间": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d"}, "data_category": "date"}, "截止日期": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2024, "format": "%Y-%m-%d"}, "data_category": "date"}}}, "medical_records": {"display_name": "医疗病历", "table_title_template": "患者病历记录表", "columns": {"病历编号": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["MR########", "EMR######", "CASE######", "HC########"]}, "data_category": "text"}, "患者姓名": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "性别": {"generator_type": "categorical", "params": {"values": ["男", "女"]}, "data_category": "text"}, "年龄": {"generator_type": "integer_range", "params": {"min": 0, "max": 100}, "data_category": "numeric"}, "就诊日期": {"generator_type": "date_range", "params": {"start_year": 2022, "end_year": 2023, "format": "%Y-%m-%d"}, "data_category": "date"}, "科室": {"generator_type": "categorical", "params": {"values": ["内科", "外科", "妇产科", "儿科", "眼科", "耳鼻喉科", "口腔科", "皮肤科", "神经内科", "神经外科", "心血管内科", "呼吸内科", "消化内科", "泌尿外科", "骨科", "肿瘤科", "精神科", "急诊科", "重症医学科", "康复科"]}, "data_category": "text"}, "主诊医师": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "主诉": {"generator_type": "categorical_with_pattern", "params": {"prefixes": ["反复", "持续", "突发", "间断", "偶发", "慢性", "急性", "逐渐加重的"], "suffixes": ["头痛", "胸痛", "腹痛", "咳嗽", "发热", "恶心呕吐", "乏力", "头晕", "腹泻", "便秘", "背痛", "关节痛", "视力模糊", "听力下降", "皮疹", "失眠"]}, "data_category": "text"}, "现病史": {"generator_type": "categorical_with_pattern", "params": {"prefixes": ["患者", "病人"], "suffixes": ["近3日出现症状，无明显诱因", "1周前开始出现不适，逐渐加重", "半月前出现症状，曾自行服药未见好转", "2天前突然发病，伴有明显症状", "反复出现类似症状已有3个月", "长期患有基础疾病，症状时轻时重", "近期工作压力大，症状明显加重", "无明显诱因突然发病，伴有全身不适"]}, "data_category": "text"}, "既往史": {"generator_type": "categorical", "params": {"values": ["否认特殊疾病史", "高血压病史5年", "糖尿病病史3年", "冠心病病史", "肝炎病史", "肺结核病史", "手术史：阑尾切除", "外伤史：车祸致骨折", "过敏史：青霉素", "精神疾病史", "家族性疾病史", "无特殊", "否认传染病史", "慢性胃炎病史"]}, "data_category": "text"}, "体格检查": {"generator_type": "categorical_with_pattern", "params": {"prefixes": ["BP: ", "T: ", "P: ", "R: ", "身高: ", "体重: "], "suffixes": ["120/80mmHg", "36.5℃", "75次/分", "18次/分", "170cm", "65kg", "神志清楚", "精神可", "面色苍白", "呼吸平稳", "心律齐", "腹软", "无殊"]}, "data_category": "text"}, "辅助检查": {"generator_type": "categorical", "params": {"values": ["血常规：正常", "血常规：WBC升高", "尿常规：正常", "肝功能：正常", "肾功能：正常", "心电图：窦性心律", "心电图：ST-T改变", "X线胸片：正常", "CT：未见明显异常", "MRI：正常", "超声：未见异常", "超声：肝脏轻度脂肪浸润", "血糖：正常", "血脂：TC升高", "电解质：正常", "凝血功能：正常"]}, "data_category": "text"}, "初步诊断": {"generator_type": "categorical", "params": {"values": ["上呼吸道感染", "肺炎", "急性胃肠炎", "冠心病", "高血压", "2型糖尿病", "慢性胃炎", "消化性溃疡", "胆囊炎", "尿路感染", "肾结石", "颈椎病", "腰椎间盘突出", "骨质疏松", "关节炎", "偏头痛", "焦虑障碍", "抑郁症", "失眠症", "过敏性皮炎", "荨麻疹", "结膜炎", "扁桃体炎"]}, "data_category": "text"}, "治疗方案": {"generator_type": "categorical_with_pattern", "params": {"prefixes": ["口服", "静脉滴注", "肌肉注射", "皮下注射", "局部用药", "物理治疗", "手术治疗", "综合治疗"], "suffixes": ["抗生素", "解热镇痛药", "抗病毒药", "胃肠道药物", "心血管药物", "降压药", "降糖药", "镇静催眠药", "止咳祛痰药", "抗过敏药", "激素类药物", "维生素补充", "中药治疗"]}, "data_category": "text"}, "医嘱": {"generator_type": "categorical", "params": {"values": ["规律服药，注意休息", "清淡饮食，多饮水", "定期复诊，观察病情变化", "遵医嘱用药，禁忌辛辣刺激食物", "加强锻炼，控制体重", "戒烟限酒，规律作息", "避免剧烈运动，保持心情舒畅", "遵医嘱用药，必要时复查", "注意保暖，避免着凉", "患病期间避免接触过敏原", "严格控制饮食，监测血糖", "控制血压，低盐饮食", "做好个人防护，避免交叉感染"]}, "data_category": "text"}, "下次复诊日期": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2024, "format": "%Y-%m-%d"}, "data_category": "date"}}}, "customer_feedback": {"display_name": "客户反馈", "table_title_template": "客户反馈数据表", "columns": {"反馈编号": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["CF########", "FEEDBACK-####"]}, "data_category": "text"}, "用户ID": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["UID####", "U######", "USER_###"]}, "data_category": "text"}, "用户名": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "反馈时间": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d %H:%M:%S"}, "data_category": "date"}, "反馈内容": {"generator_type": "categorical_with_pattern", "params": {"prefixes": ["满意", "不满意", "一般", "建议"], "suffixes": ["", "", "", ""]}, "data_category": "text"}, "回复内容": {"generator_type": "categorical_with_pattern", "params": {"prefixes": ["已回复", "未回复"], "suffixes": ["", ""]}, "data_category": "text"}, "满意度评分": {"generator_type": "numerical_range_formatted", "params": {"min_value": 1, "max_value": 5, "decimals": 1, "format_string": "{:.1f}"}, "data_category": "numeric"}}}, "bank_account": {"display_name": "银行账户", "table_title_template": "银行账户明细表", "columns": {"账户ID": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["ACC######", "BA########", "BANK######"]}, "data_category": "text"}, "账户名称": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "账户类型": {"generator_type": "categorical", "params": {"values": ["活期储蓄账户", "定期储蓄账户", "支票账户", "信用卡账户", "贷款账户", "基金账户", "股票账户", "理财账户", "企业账户", "联名账户", "外币账户"]}, "data_category": "text"}, "开户银行": {"generator_type": "categorical", "params": {"values": ["中国工商银行", "中国农业银行", "中国银行", "中国建设银行", "交通银行", "招商银行", "中信银行", "浦发银行", "民生银行", "兴业银行", "平安银行", "华夏银行", "光大银行", "广发银行", "邮储银行", "浙商银行", "渤海银行"]}, "data_category": "text"}, "开户支行": {"generator_type": "categorical_with_pattern", "params": {"prefixes": {"type": "common_data", "key": "major_cities"}, "suffixes": ["分行", "支行", "第一支行", "营业部", "中心支行", "科技园支行", "工业区支行", "城南支行", "城北支行", "城东支行", "城西支行"]}, "data_category": "text"}, "开户日期": {"generator_type": "date_range", "params": {"start_year": 2010, "end_year": 2023, "format": "%Y-%m-%d"}, "data_category": "date"}, "账户状态": {"generator_type": "categorical", "params": {"values": ["正常", "冻结", "挂失", "止付", "销户", "休眠", "锁定", "临时冻结"]}, "data_category": "text"}, "币种": {"generator_type": "categorical", "params": {"values": ["人民币", "美元", "欧元", "英镑", "日元", "港币", "澳元", "加元", "瑞士法郎"]}, "data_category": "text"}, "账户余额": {"generator_type": "numerical_range_formatted", "params": {"min_value": 0, "max_value": 1000000, "decimals": 2, "format_string": "{:,.2f}"}, "data_category": "numeric"}, "可用余额": {"generator_type": "numerical_range_formatted", "params": {"min_value": 0, "max_value": 1000000, "decimals": 2, "format_string": "{:,.2f}"}, "data_category": "numeric"}, "利率": {"generator_type": "numerical_range_formatted", "params": {"min_value": 0.1, "max_value": 5.0, "decimals": 2, "format_string": "{:.2f}%"}, "data_category": "numeric"}, "上次交易日期": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d"}, "data_category": "date"}, "账户级别": {"generator_type": "categorical", "params": {"values": ["普通账户", "金卡账户", "白金账户", "钻石账户", "黑金账户", "VIP账户", "私人银行", "企业账户", "公司账户", "个人账户"]}, "data_category": "text"}, "年费": {"generator_type": "numerical_range_formatted", "params": {"min_value": 0, "max_value": 10000, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}}}, "email_data": {"display_name": "电子邮件", "table_title_template": "电子邮件数据表", "columns": {"邮件ID": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["MAIL-####-####", "MSG######", "EM########", "EMAIL######"]}, "data_category": "text"}, "发件人": {"generator_type": "faker_email", "params": {"locale": "zh_CN"}, "data_category": "text"}, "发件人姓名": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "收件人": {"generator_type": "faker_email", "params": {"locale": "zh_CN"}, "data_category": "text"}, "收件人姓名": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "抄送人": {"generator_type": "faker_email", "params": {"locale": "zh_CN"}, "data_category": "text"}, "主题": {"generator_type": "categorical", "params": {"values": ["会议通知：周例会安排", "项目进度更新", "关于预算调整的讨论", "新产品发布计划", "请假申请", "客户反馈汇总", "系统维护通知", "年度报告提交", "培训课程安排", "节日祝福", "合同审批", "账单通知", "密码重置", "账户登录验证", "服务器异常警报", "招聘面试安排", "市场调研报告", "销售数据分析", "产品更新说明", "技术支持请求"]}, "data_category": "text"}, "发送时间": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d %H:%M:%S"}, "data_category": "date"}, "状态": {"generator_type": "categorical", "params": {"values": ["已发送", "已读", "未读", "草稿", "已回复", "已转发", "已删除", "垃圾邮件", "收件箱", "已存档", "已标记", "待处理", "重要", "发送失败"]}, "data_category": "text"}, "优先级": {"generator_type": "categorical", "params": {"values": ["高", "中", "低", "普通", "紧急"]}, "data_category": "text"}, "邮件大小": {"generator_type": "numerical_range_formatted", "params": {"min_value": 1, "max_value": 25000, "format_string": "{:,}KB"}, "data_category": "numeric"}, "是否有附件": {"generator_type": "categorical", "params": {"values": ["是", "否"]}, "data_category": "text"}, "附件数量": {"generator_type": "integer_range", "params": {"min": 0, "max": 10}, "data_category": "numeric"}, "邮件标签": {"generator_type": "categorical", "params": {"values": ["工作", "个人", "重要", "待办", "已完成", "跟进", "项目", "会议", "客户", "财务", "合同", "报告", "产品", "技术", "管理", "未分类"]}, "data_category": "text"}, "是否已回复": {"generator_type": "categorical", "params": {"values": ["是", "否"]}, "data_category": "text"}, "回复时间": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d %H:%M:%S"}, "data_category": "date"}}}, "network_logs": {"display_name": "网络访问日志", "table_title_template": "网络访问日志数据表", "columns": {"日志ID": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["LOG########", "NL-####-####", "NET######", "ACCESS######"]}, "data_category": "text"}, "时间戳": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d %H:%M:%S.%f"}, "data_category": "date"}, "请求方法": {"generator_type": "categorical", "params": {"values": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"]}, "data_category": "text"}, "URL路径": {"generator_type": "categorical", "params": {"values": ["/api/users", "/api/products", "/api/orders", "/api/login", "/api/logout", "/api/auth", "/api/profile", "/api/settings", "/api/dashboard", "/api/search", "/home", "/about", "/contact", "/products", "/services", "/blog", "/news", "/cart", "/checkout", "/payment", "/success", "/error", "/404", "/admin"]}, "data_category": "text"}}, "状态码": {"generator_type": "categorical", "params": {"values": ["200", "201", "204", "301", "302", "304", "400", "401", "403", "404", "405", "408", "422", "429", "500", "502", "503", "504"]}, "data_category": "text"}, "响应时间": {"generator_type": "numerical_range_formatted", "params": {"min_value": 5, "max_value": 10000, "format_string": "{:,}ms"}, "data_category": "numeric"}, "IP地址": {"generator_type": "categorical", "params": {"values": ["***********", "********", "**********", "127.0.0.1", "*******", "***************", "*********", "*********", "*******", "*******", "**************", "**************", "***************", "***************", "***********", "**************"]}, "data_category": "text"}, "用户代理": {"generator_type": "categorical", "params": {"values": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36"]}, "data_category": "text"}, "引用页": {"generator_type": "categorical", "params": {"values": ["https://www.google.com/", "https://www.baidu.com/", "https://www.bing.com/", "https://www.example.com/", "https://www.facebook.com/", "https://twitter.com/", "https://www.instagram.com/", "https://www.linkedin.com/", "https://www.youtube.com/", "https://www.zhihu.com/", "https://weibo.com/", "https://www.douyin.com/", "https://www.qq.com/", "https://www.163.com/", "direct", "none"]}, "data_category": "text"}, "用户ID": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["UID####", "USER####", "U######"]}, "data_category": "text"}, "会话ID": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["SID-####-####-####", "SESSION######", "S-####-####"]}, "data_category": "text"}, "数据传输量": {"generator_type": "numerical_range_formatted", "params": {"min_value": 1, "max_value": 10000, "format_string": "{:,}KB"}, "data_category": "numeric"}, "设备类型": {"generator_type": "categorical", "params": {"values": ["Desktop", "Mobile", "Tablet", "Smart TV", "Game Console", "Wearable", "Bot", "Unknown"]}, "data_category": "text"}, "操作系统": {"generator_type": "categorical", "params": {"values": ["Windows 10", "Windows 11", "macOS", "iOS", "Android", "Linux", "Chrome OS", "Ubuntu", "Debian", "CentOS", "Windows 7", "Windows 8.1", "Not Detected"]}, "data_category": "text"}, "浏览器": {"generator_type": "categorical", "params": {"values": ["Chrome", "Firefox", "Safari", "Edge", "Opera", "Internet Explorer", "Samsung Browser", "UC Browser", "<PERSON><PERSON> Browser", "<PERSON><PERSON>", "Yande<PERSON> Browser", "Unknown"]}, "data_category": "text"}}, "inventory": {"display_name": "库存信息", "table_title_template": "商品库存数据表", "columns": {"商品ID": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["SKU####", "PROD######", "IT########", "INV-####"]}, "data_category": "text"}, "商品名称": {"generator_type": "categorical_with_pattern", "params": {"prefixes": ["高级", "精选", "豪华", "优质", "经典", "时尚", "专业", "定制", "限量版"], "suffixes": ["手机", "电脑", "平板", "耳机", "电视", "洗衣机", "冰箱", "空调", "沙发", "餐桌"]}, "data_category": "text"}, "品牌": {"generator_type": "categorical", "params": {"values": ["苹果", "三星", "华为", "小米", "索尼", "联想", "戴尔", "华硕", "宏碁", "魅族", "OPPO", "vivo", "一加", "中兴", "金立", "酷派", "诺基亚", "LG", "摩托罗拉", "HTC", "富士通", "TCL", "爱立信", "锐捷", "华为荣耀", "亿通", "长城", "蓝星科技", "流行电子", "绿动科技", "雷霆电子", "云动科技", "超频科技", "光明智能", "京东", "阿里巴巴", "百度", "美团", "拼多多", "网易", "腾讯", "小米米家", "富士", "卡西欧", "佳能", "尼康", "松下", "雷克萨斯", "丰田", "宝马", "奔驰", "奥迪", "特斯拉", "雪佛兰", "福特", "本田", "宝马Mini", "哈雷戴维森", "劳斯莱斯", "兰博基尼", "迪奥", "香奈儿", "古驰", "爱马仕", "路易威登", "迪士尼", "华特迪士尼", "海尔", "美的", "西门子", "格力", "志高", "澳柯玛", "奥克斯", "长虹", "创维", "苏宁", "国美", "小熊电器", "奥迪双钻", "小米生态链", "乐视", "三星半导体", "海信", "东芝", "赛米控", "纽曼", "爱普生", "联想ThinkPad", "华为Mat<PERSON>Book", "神州", "小米Redmi", "TCL电视", "瓦克", "兰芝", "高露洁", "贝达药业", "药明康德", "联邦制药", "联邦快递"]}, "data_category": "text"}, "分类": {"generator_type": "categorical", "params": {"values": ["电子产品", "家具", "家电", "服装", "鞋帽", "箱包", "食品", "饮料", "日用品", "母婴用品", "运动健身", "户外装备", "数码配件", "手机", "电脑", "平板", "智能手表", "耳机", "音响", "相机", "摄影器材", "游戏机", "电玩", "办公设备", "办公用品", "文具", "书籍", "电子书", "影音", "居家装饰", "厨房用品", "卫浴用品", "清洁用品", "洗护用品", "化妆品", "护肤品", "香水", "珠宝首饰", "手表", "眼镜", "健康保健", "医疗器械", "药品", "保健品", "茶叶", "咖啡", "酒类", "零食", "生鲜", "水果", "蔬菜", "肉类", "海鲜", "奶制品", "粮油", "调味品", "宠物用品", "宠物食品", "乐器", "玩具", "模型", "手办", "艺术品", "收藏品", "汽车用品", "汽车配件", "摩托车装备", "自行车装备", "家居软装", "窗帘", "地毯", "沙发", "床上用品", "灯具", "花卉园艺", "工具", "五金", "电工", "智能家居", "建材", "男装", "女装", "童装", "内衣", "袜子", "运动服", "休闲服", "礼品", "旅行用品", "行李箱", "背包", "手袋", "手机壳", "数据线", "充电器", "移动电源", "显示器", "键盘", "鼠标", "打印机", "学习用品", "教辅材料", "教材", "纸尿裤", "奶瓶", "婴儿床", "婴儿车", "月子用品", "孕妇装", "儿童玩具", "童车", "围巾", "帽子", "手套", "太阳镜", "雨伞"]}, "data_category": "text"}, "库存数量": {"generator_type": "integer_range", "params": {"min": 0, "max": 10000}, "data_category": "numeric"}, "库存状态": {"generator_type": "categorical", "params": {"values": ["充足", "正常", "偏低", "紧缺", "缺货", "待补货", "在途", "预售", "停售", "已下架"]}, "data_category": "text"}, "供应商": {"generator_type": "faker_company", "params": {"locale": "zh_CN"}, "data_category": "text"}, "仓库位置": {"generator_type": "categorical", "params": {"values": ["北京仓", "上海仓", "广州仓", "深圳仓", "成都仓", "武汉仓", "西安仓", "杭州仓", "南京仓", "重庆仓", "天津仓", "东北仓", "华中仓", "华南仓", "华北仓"]}, "data_category": "text"}, "进货价": {"generator_type": "numerical_range_formatted", "params": {"min_value": 10, "max_value": 5000, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}, "售价": {"generator_type": "numerical_range_formatted", "params": {"min_value": 15, "max_value": 10000, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}, "最后进货日期": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d"}, "data_category": "date"}}}, "hotel_reservation": {"display_name": "酒店预订", "table_title_template": "酒店预订信息表", "columns": {"预订编号": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["BK########", "HTL######", "RESV######", "HB########"]}, "data_category": "text"}, "客人姓名": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "联系电话": {"generator_type": "faker_phone_number", "params": {"locale": "zh_CN"}, "data_category": "text"}, "联系邮箱": {"generator_type": "faker_email", "params": {"locale": "zh_CN"}, "data_category": "text"}, "酒店名称": {"generator_type": "categorical_with_pattern", "params": {"prefixes": {"type": "common_data", "key": "major_cities"}, "suffixes": ["希尔顿酒店", "万豪酒店", "四季酒店", "洲际酒店", "凯悦酒店", "丽思卡尔顿酒店", "华尔道夫酒店", "香格里拉酒店", "喜达屋酒店", "雅高酒店", "文华东方酒店", "瑞士酒店", "威斯汀酒店", "温德姆酒店", "阿联酋大酒店", "半岛酒店", "大都会酒店", "滨江花园酒店", "君悦酒店", "瑞吉酒店", "速8酒店", "汉庭酒店", "如家酒店", "7天酒店", "格林豪泰酒店", "锦江之星酒店", "全季酒店", "汉莎酒店", "贝尔特酒店", "英迪格酒店", "假日酒店", "凯宾斯基酒店", "索菲特酒店", "贝尔法斯特酒店", "金陵酒店", "北京饭店", "上海外滩茂悦酒店", "布丁酒店", "优阁酒店", "维也纳酒店", "铂尔曼酒店", "南非大酒店", "澳大利亚环球酒店", "丽景酒店", "中信酒店", "千禧大酒店", "皇宫酒店", "青岛海尔美酒店", "圣淘沙酒店", "新加坡滨海酒店", "丽都酒店", "苏州大酒店", "杭州大酒店", "广州塔楼酒店", "泉州大酒店", "武汉国际酒店", "合肥万达酒店", "澳门银河酒店", "珠海长隆酒店", "丽悦酒店", "潮州市大酒店", "平遥古城酒店", "洛杉矶贝尔艾尔酒店", "拉斯维加斯金沙酒店", "巴厘岛梦幻酒店", "东京新宿酒店", "巴黎拉斐特酒店", "伦敦大都会酒店", "纽约曼哈顿酒店", "香港迪士尼乐园酒店", "大阪环球影城酒店", "温哥华海滨酒店", "巴塞罗那高尔夫酒店", "布鲁塞尔天际酒店", "西雅图滨江酒店", "马尔代夫海岛度假酒店", "菲律宾长滩岛酒店", "哥本哈根哥本哈根塔酒店", "阿姆斯特丹皇家酒店", "洛杉矶海滨酒店", "蒙特利湾酒店", "温哥华里士满酒店", "瑞士湖畔酒店", "新西兰奥克兰酒店", "新加坡滨海湾酒店", "北京王府井大酒店", "上海浦东大酒店", "重庆喜来登大酒店", "成都富力丽思卡尔顿酒店", "南京希尔顿酒店", "济南舜耕大酒店", "台北福华大酒店", "香港柏丽酒店", "澳大利亚悉尼大酒店", "东京王子大酒店", "伦敦绿宝石酒店", "巴塞罗那圣胡安酒店", "斯里兰卡度假酒店", "马德里科尔多瓦酒店", "曼谷曼谷酒店", "阿根廷阿尔卡酒店"]}, "data_category": "text"}, "酒店地址": {"generator_type": "faker_address", "params": {"locale": "zh_CN"}, "data_category": "text"}, "房间类型": {"generator_type": "categorical", "params": {"values": ["单人间", "双人间", "豪华单人间", "豪华双人间", "套房", "总统套房", "商务套房", "家庭套房", "海景房", "山景房", "花园房", "湖景房", "行政套房", "蜜月套房", "日式房间", "标准间", "高级标准间", "行政楼层房", "大床房", "双床房", "连通房", "单间", "经济舱房", "高级经济舱房", "情侣套房", "亲子房", "贵宾房", "豪华套房", "迷你套房", "商务房", "阁楼房", "高级阁楼房", "复式房", "庭院房", "露台房", "观景房", "VIP房", "总统房", "标准豪华间", "海滨房", "温泉房", "按摩房", "健身房", "浴室套房", "防噪音房", "无障碍房", "宠物友好房", "纯净房", "贵族房", "网红房", "超大房", "豪华单人海景房", "豪华双人海景房", "超豪华套房", "精品房", "奢华房", "景观房", "温馨房", "商务单人房", "豪华家庭房", "VIP海景房", "天空之城房", "顶楼套房", "浪漫套房", "情侣蜜月房", "温泉套房", "钢琴套房", "白金套房", "艺术房", "经典房", "舒适单人房", "舒适双人房", "豪华浴室房", "景观观光房", "天窗房", "私人泳池房", "别墅房", "海滩别墅房", "度假套房", "家庭亲子房", "滑雪房", "高尔夫房", "私人海滩房", "度假村房", "游艇房", "瑜伽房", "商务会议室房", "Spa房", "电影主题房", "艺术工作室房", "游戏房", "摄影房", "奢华日式房", "超豪华家庭房", "露营房", "泳池别墅房", "星空房", "全景房", "湖畔别墅房", "创意房", "未来房", "极简房", "复古房", "摩登风格房", "蓝色海岸房", "阳光房", "城市景观房", "花园庭院房", "高端会所房", "民宿房", "精品艺术房"]}, "data_category": "text"}, "入住日期": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d"}, "data_category": "date"}, "离店日期": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d"}, "data_category": "date"}, "入住天数": {"generator_type": "integer_range", "params": {"min": 1, "max": 30}, "data_category": "numeric"}, "入住人数": {"generator_type": "integer_range", "params": {"min": 1, "max": 6}, "data_category": "numeric"}, "预订渠道": {"generator_type": "categorical", "params": {"values": ["携程网", "飞猪", "去哪儿网", "阿里旅行", "艺龙", "Booking.com", "Agoda", "Expedia", "Airbnb", "途牛网", "同程旅游", "马蜂窝", "京东旅游", "旅行社", "酒店官网", "优步", "穷游网", "旅游搜索引擎", "携程旅行", "百度旅行", "拼多多旅游", "途家网", "美团酒店", "旅游套餐平台", "住客之家", "驴妈妈", "有米酒店", "高端旅游平台", "旅游代理商", "专车服务", "旅行达人平台", "省钱预定平台", "全球酒店平台", "度假村官网", "特色民宿平台", "国际航班平台", "自驾游平台", "巴士预订平台", "豪华旅行社", "奢华度假平台", "短租公寓平台", "中国铁路客户服务中心", "国际机票平台", "火车票预订平台", "在线旅游服务商", "全球航班预订", "本地旅行平台", "国内机票平台", "国际机票代理", "环球邮轮平台", "旅游代理商平台", "跟团游平台", "自助游平台", "高端自驾旅游平台", "城市旅游平台", "个人旅游推荐平台", "奢华私人定制平台", "旅游论坛", "度假村预定", "酒店及民宿预定", "全国旅游信息平台", "高端客房平台", "特色酒店预定", "全球短租平台", "旅行酒店顾问平台", "在线旅游咨询平台", "专业旅行顾问平台", "旅行信息发布平台", "景区门票预定平台", "节庆活动预定平台", "旅行会员平台", "航空公司官网", "跨国旅游平台", "航班与酒店预订平台", "全球旅游信息平台", "旅游规划平台", "国内旅游规划平台", "国际旅游规划平台", "旅游需求平台", "出行预定平台", "共享住宿平台", "高端旅行体验平台", "门票预订平台", "旅行拍摄服务平台", "个性化旅游平台", "人气旅游平台", "背包客平台", "精品旅行网站", "家庭旅游平台", "商务出行平台", "会议酒店预订平台", "特色度假村平台", "全球度假酒店平台", "会员预定平台", "汽车租赁平台", "旅游活动预定平台", "国际旅游套餐平台", "家庭度假平台", "酒店代理平台", "自驾游网站", "世界旅游组织官网", "环球自驾平台", "移动旅行平台", "轻奢旅行平台"]}, "data_category": "text"}, "房费": {"generator_type": "numerical_range_formatted", "params": {"min_value": 100, "max_value": 10000, "decimals": 0, "format_string": "{:d}"}, "data_category": "numeric"}, "总费用": {"generator_type": "numerical_range_formatted", "params": {"min_value": 300, "max_value": 50000, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}, "支付状态": {"generator_type": "categorical", "params": {"values": ["已支付", "未支付", "部分支付", "待支付", "已取消", "已退款", "预授权"]}, "data_category": "text"}, "支付方式": {"generator_type": "categorical", "params": {"values": ["支付宝", "微信支付", "银联支付", "信用卡", "借记卡", "PayPal", "Apple Pay", "Google Pay", "微信钱包", "支付宝钱包", "Stripe", "Visa卡", "MasterCard", "American Express", "Discover", "Diners Club", "JCB卡", "中国银行支付", "招商银行支付", "建设银行支付", "工商银行支付", "农业银行支付", "交通银行支付", "浦发银行支付", "中信银行支付", "光大银行支付", "汇丰银行支付", "三菱UFJ支付", "贝宝支付", "快捷支付", "网银支付", "在线支付", "银行卡支付", "银行卡转账", "小额支付", "无卡支付", "二维码支付", "POS机支付", "分期付款", "余额支付", "电子钱包", "虚拟货币支付", "比特币支付", "以太坊支付", "USDT支付", "Payoneer支付", "银行转账", "货到付款", "现金支付", "银联云闪付", "Apple Pay Express", "京东支付", "美团支付", "腾讯云支付", "百度钱包", "京东白条", "蚂蚁花呗", "京东支付白条", "金融分期支付", "京东金融", "京东信用卡支付", "消费积分支付", "积分兑换", "优惠券支付", "红包支付", "商家账户余额", "支付宝余额宝支付", "支付宝花呗分期", "腾讯信用支付", "滴滴支付", "淘宝支付", "新浪支付", "58支付", "支付宝账户余额", "余额宝支付", "京东金融账户支付", "<PERSON><PERSON>支付", "Venmo支付", "Android Pay", "ECheck支付", "FastPay支付", "支付宝实时支付", "微信实时支付", "银联支付扫码", "支付宝扫码支付", "微信扫码支付", "支付宝快捷支付", "信用卡分期支付", "京东支付", "淘宝支付宝", "支付宝生活号支付", "餐饮支付平台", "零钱支付", "跨境支付", "预付卡支付", "客户充值支付", "虚拟支付平台", "国际卡支付", "信用卡本地支付", "数字钱包支付", "信用卡在线支付", "云支付平台", "Tether支付", "买单支付", "消费积分支付", "信用账户支付", "ZPay支付", "Face Pay支付", "指纹支付", "人脸识别支付"]}, "data_category": "text"}, "预订状态": {"generator_type": "categorical", "params": {"values": ["已确认", "待确认", "已取消", "已完成", "已入住", "提前退房", "未入住", "延期", "爽约"]}, "data_category": "text"}}}, "flight_itinerary": {"display_name": "航班行程", "table_title_template": "航班行程信息表", "columns": {"订单编号": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["FLT########", "AIR######", "TKT########", "F########"]}, "data_category": "text"}, "乘客姓名": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "航班号": {"generator_type": "categorical_with_pattern", "params": {"prefixes": ["CA", "MU", "CZ", "HU", "MF", "ZH", "3U", "HO", "GS", "SC", "JD", "KN", "EU", "FM", "NH", "UA", "AA", "DL", "SQ", "CX", "KE", "JL", "BR", "TG", "OZ", "LH", "AF", "BA", "EK", "QR", "TK"], "suffixes": ["1234", "2345", "3456", "4567", "5678", "6789", "7890", "8901", "9012", "0123"]}, "data_category": "text"}, "航空公司": {"generator_type": "categorical", "params": {"values": ["中国国际航空公司", "南方航空", "东方航空", "海南航空", "深圳航空", "吉祥航空", "四川航空", "重庆航空", "春秋航空", "乌鲁木齐航空", "天津航空", "山东航空", "中国南方航空", "国航", "厦门航空", "深圳航空", "中东航空", "阿联酋航空", "卡塔尔航空", "阿提哈德航空", "土耳其航空", "大韩航空", "新加坡航空", "国泰航空", "汉莎航空", "法航", "荷兰皇家航空", "英国航空", "瑞士国际航空", "芬兰航空", "俄罗斯航空", "日本航空", "全日空", "泰国国际航空", "印度航空", "马来西亚航空", "澳大利亚航空", "新西兰航空", "加拿大航空", "美国联合航空", "美国航空", "达美航空", "阿拉斯加航空", "西南航空", "捷蓝航空", "美国鹰航空", "拉丁美洲航空", "哥伦比亚航空", "巴西航空公司", "巴拿马航空", "墨西哥航空", "波兰航空", "奥斯曼航空", "瑞典航空", "中国台湾中华航空", "中国台湾长荣航空", "中华航空", "澳门航空", "香港航空", "港龙航空", "日本邮轮航空", "新加坡航空", "沙特阿拉伯航空", "以色列航空", "冰岛航空", "奥地利航空", "瑞士国际航空", "斯堪的纳维亚航空", "中国快运航空", "法国航空", "波音航空", "艾尔兰航空", "皇家约旦航空", "埃及航空", "突尼斯航空", "埃塞俄比亚航空", "南非航空", "肯尼亚航空", "马里航空", "安哥拉航空", "土库曼航空", "哈萨克斯坦航空", "乌兹别克斯坦航空", "塔吉克航空", "吉尔吉斯航空", "阿尔及利亚航空", "摩洛哥航空", "缅甸航空", "老挝航空", "柬埔寨航空", "斯里兰卡航空", "马尔代夫航空", "哈萨克航空", "孟加拉航空", "巴基斯坦国际航空", "尼泊尔航空", "马尔代夫航空", "菲律宾航空", "印度斯里兰卡航空", "中非航空", "乌干达航空", "卢旺达航空", "坦桑尼亚航空", "卡塔尔航空", "柬埔寨航空", "博茨瓦纳航空", "赞比亚航空", "津巴布韦航空"]}, "data_category": "text"}, "出发城市": {"generator_type": "categorical", "params": {"values": {"type": "common_data", "key": "cities"}}, "data_category": "text"}, "到达城市": {"generator_type": "categorical", "params": {"values": {"type": "common_data", "key": "cities"}}, "data_category": "text"}, "出发机场": {"generator_type": "categorical_with_pattern", "params": {"prefixes": {"type": "common_data", "key": "major_cities"}, "suffixes": ["北京首都国际机场", "上海浦东国际机场", "广州白云国际机场", "深圳宝安国际机场", "成都双流国际机场", "杭州萧山国际机场", "武汉天河国际机场", "重庆江北国际机场", "天津滨海国际机场", "南京禄口国际机场", "昆明长水国际机场", "西安咸阳国际机场", "青岛流亭国际机场", "大连周水子国际机场", "沈阳桃仙国际机场", "厦门高崎国际机场", "福州长乐国际机场", "哈尔滨太平国际机场", "济南遥墙国际机场", "郑州新郑国际机场", "长春龙嘉国际机场", "合肥新桥国际机场", "南昌昌北国际机场", "兰州中川国际机场", "乌鲁木齐地窝堡国际机场", "贵阳龙洞堡国际机场", "呼和浩特白塔国际机场", "海口美兰国际机场", "石家庄正定国际机场", "香港国际机场", "澳门国际机场", "台北松山机场", "台北桃园国际机场", "高雄国际机场", "东京成田国际机场", "东京羽田机场", "大阪关西国际机场", "名古屋中部国际机场", "福冈机场", "札幌新千岁机场", "新加坡樟宜机场", "吉隆坡国际机场", "曼谷素万那普国际机场", "巴厘岛伍拉·赖国际机场", "首尔仁川国际机场", "釜山金海国际机场", "汉堡机场", "法兰克福机场", "慕尼黑机场", "巴黎戴高乐机场", "阿姆斯特丹史基浦机场", "伦敦希思罗机场", "伦敦盖特威克机场", "苏黎世机场", "洛杉矶国际机场", "旧金山国际机场", "纽约肯尼迪国际机场", "纽约拉瓜迪亚机场", "芝加哥奥黑尔国际机场", "迈阿密国际机场", "达拉斯福特沃斯国际机场", "华盛顿杜勒斯国际机场", "阿特兰大哈茨菲尔德国际机场", "多伦多皮尔逊国际机场", "温哥华国际机场", "蒙特利尔皮埃尔·埃利奥特·特鲁多国际机场", "墨西哥城贝尼托·胡亚雷斯国际机场", "里约热内卢加利昂国际机场", "圣保罗瓜鲁柳斯国际机场", "布宜诺斯艾利斯埃塞萨国际机场", "开普敦国际机场", "约翰内斯堡奥利弗·坦博国际机场", "迪拜国际机场", "阿布扎比国际机场", "多哈哈马德国际机场", "马尼拉尼诺·阿基诺国际机场", "悉尼金斯福德·史密斯国际机场", "墨尔本塔拉马林机场", "布里斯班机场", "奥克兰国际机场", "霍巴特机场", "加尔各答内塔吉·苏巴什·昌德博机场", "孟买贾特拉帕蒂·希瓦吉国际机场", "德里英迪拉·甘地国际机场", "新德里市机场", "雅加达苏加诺-哈达国际机场", "开罗国际机场", "伊斯坦布尔阿塔图尔克国际机场", "阿尔及尔胡阿里·布迈丁国际机场", "杜塞尔多夫机场", "华沙弗雷德里克·肖邦机场", "科伦坡班达拉奈克国际机场", "吉布提国际机场", "里斯本胡贝尔图·佩雷拉国际机场", "柏林泰格尔机场"]}, "data_category": "text"}, "到达机场": {"generator_type": "categorical_with_pattern", "params": {"prefixes": {"type": "common_data", "key": "major_cities"}, "suffixes": ["北京首都国际机场", "上海浦东国际机场", "广州白云国际机场", "深圳宝安国际机场", "成都双流国际机场", "杭州萧山国际机场", "武汉天河国际机场", "重庆江北国际机场", "天津滨海国际机场", "南京禄口国际机场", "昆明长水国际机场", "西安咸阳国际机场", "青岛流亭国际机场", "大连周水子国际机场", "沈阳桃仙国际机场", "厦门高崎国际机场", "福州长乐国际机场", "哈尔滨太平国际机场", "济南遥墙国际机场", "郑州新郑国际机场", "长春龙嘉国际机场", "合肥新桥国际机场", "南昌昌北国际机场", "兰州中川国际机场", "乌鲁木齐地窝堡国际机场", "贵阳龙洞堡国际机场", "呼和浩特白塔国际机场", "海口美兰国际机场", "石家庄正定国际机场", "香港国际机场", "澳门国际机场", "台北松山机场", "台北桃园国际机场", "高雄国际机场", "东京成田国际机场", "东京羽田机场", "大阪关西国际机场", "名古屋中部国际机场", "福冈机场", "札幌新千岁机场", "新加坡樟宜机场", "吉隆坡国际机场", "曼谷素万那普国际机场", "巴厘岛伍拉·赖国际机场", "首尔仁川国际机场", "釜山金海国际机场", "汉堡机场", "法兰克福机场", "慕尼黑机场", "巴黎戴高乐机场", "阿姆斯特丹史基浦机场", "伦敦希思罗机场", "伦敦盖特威克机场", "苏黎世机场", "洛杉矶国际机场", "旧金山国际机场", "纽约肯尼迪国际机场", "纽约拉瓜迪亚机场", "芝加哥奥黑尔国际机场", "迈阿密国际机场", "达拉斯福特沃斯国际机场", "华盛顿杜勒斯国际机场", "阿特兰大哈茨菲尔德国际机场", "多伦多皮尔逊国际机场", "温哥华国际机场", "蒙特利尔皮埃尔·埃利奥特·特鲁多国际机场", "墨西哥城贝尼托·胡亚雷斯国际机场", "里约热内卢加利昂国际机场", "圣保罗瓜鲁柳斯国际机场", "布宜诺斯艾利斯埃塞萨国际机场", "开普敦国际机场", "约翰内斯堡奥利弗·坦博国际机场", "迪拜国际机场", "阿布扎比国际机场", "多哈哈马德国际机场", "马尼拉尼诺·阿基诺国际机场", "悉尼金斯福德·史密斯国际机场", "墨尔本塔拉马林机场", "布里斯班机场", "奥克兰国际机场", "霍巴特机场", "加尔各答内塔吉·苏巴什·昌德博机场", "孟买贾特拉帕蒂·希瓦吉国际机场", "德里英迪拉·甘地国际机场", "新德里市机场", "雅加达苏加诺-哈达国际机场", "开罗国际机场", "伊斯坦布尔阿塔图尔克国际机场", "阿尔及尔胡阿里·布迈丁国际机场", "杜塞尔多夫机场", "华沙弗雷德里克·肖邦机场", "科伦坡班达拉奈克国际机场", "吉布提国际机场", "里斯本胡贝尔图·佩雷拉国际机场", "柏林泰格尔机场"]}, "data_category": "text"}, "起飞时间": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d %H:%M"}, "data_category": "date"}, "到达时间": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d %H:%M"}, "data_category": "date"}, "航班状态": {"generator_type": "categorical", "params": {"values": ["计划", "准时", "延误", "取消", "已登机", "已起飞", "已降落", "备降", "返航", "改签"]}, "data_category": "text"}, "舱位等级": {"generator_type": "categorical", "params": {"values": ["经济舱", "高端经济舱", "商务舱", "头等舱", "超级经济舱", "公务舱"]}, "data_category": "text"}, "票价": {"generator_type": "numerical_range_formatted", "params": {"min_value": 300, "max_value": 20000, "decimals": 0, "format_string": "{:,}"}, "data_category": "numeric"}, "行李额": {"generator_type": "categorical", "params": {"values": ["20kg", "23kg", "30kg", "40kg", "无免费托运行李", "2件23kg", "1件23kg", "1件30kg", "2件30kg", "2件32kg", ["20公斤托运行李 + 5公斤手提行李", "30公斤托运行李 + 7公斤手提行李", "23公斤托运行李 + 5公斤手提行李", "15公斤托运行李 + 10公斤手提行李", "40公斤托运行李 + 10公斤手提行李", "2件行李，每件23公斤 + 7公斤手提行李", "2件行李，每件30公斤 + 5公斤手提行李", "20公斤托运行李 + 10公斤手提行李", "10公斤托运行李 + 7公斤手提行李", "2件行李，每件23公斤 + 10公斤手提行李", "2件行李，每件30公斤 + 7公斤手提行李", "25公斤托运行李 + 5公斤手提行李", "35公斤托运行李 + 5公斤手提行李", "30公斤托运行李 + 10公斤手提行李", "20公斤托运行李 + 12公斤手提行李", "18公斤托运行李 + 8公斤手提行李", "50公斤托运行李 + 10公斤手提行李", "10公斤托运行李 + 5公斤手提行李", "30公斤托运行李 + 5公斤手提行李", "25公斤托运行李 + 7公斤手提行李", "40公斤托运行李 + 7公斤手提行李", "15公斤托运行李 + 12公斤手提行李", "20公斤托运行李 + 8公斤手提行李", "2件行李，每件15公斤 + 7公斤手提行李", "5公斤托运行李 + 5公斤手提行李", "3件行李，每件20公斤 + 5公斤手提行李", "25公斤托运行李 + 5公斤手提行李", "28公斤托运行李 + 8公斤手提行李", "50公斤托运行李 + 5公斤手提行李", "15公斤托运行李 + 5公斤手提行李", "2件行李，每件18公斤 + 5公斤手提行李", "2件行李，每件25公斤 + 10公斤手提行李", "35公斤托运行李 + 5公斤手提行李", "40公斤托运行李 + 5公斤手提行李", "30公斤托运行李 + 15公斤手提行李", "23公斤托运行李 + 7公斤手提行李", "50公斤托运行李 + 5公斤手提行李", "20公斤托运行李 + 10公斤手提行李", "35公斤托运行李 + 7公斤手提行李", "40公斤托运行李 + 10公斤手提行李", "25公斤托运行李 + 10公斤手提行李", "10公斤托运行李 + 5公斤手提行李", "30公斤托运行李 + 12公斤手提行李", "15公斤托运行李 + 8公斤手提行李", "40公斤托运行李 + 5公斤手提行李", "45公斤托运行李 + 10公斤手提行李", "23公斤托运行李 + 7公斤手提行李", "20公斤托运行李 + 5公斤手提行李", "30公斤托运行李 + 7公斤手提行李", "2件行李，每件20公斤 + 10公斤手提行李", "35公斤托运行李 + 8公斤手提行李", "45公斤托运行李 + 7公斤手提行李", "50公斤托运行李 + 7公斤手提行李", "30公斤托运行李 + 5公斤手提行李", "25公斤托运行李 + 5公斤手提行李", "30公斤托运行李 + 7公斤手提行李", "23公斤托运行李 + 10公斤手提行李", "20公斤托运行李 + 7公斤手提行李", "40公斤托运行李 + 12公斤手提行李", "10公斤托运行李 + 7公斤手提行李", "20公斤托运行李 + 5公斤手提行李", "2件行李，每件23公斤 + 5公斤手提行李", "3件行李，每件10公斤 + 5公斤手提行李", "50公斤托运行李 + 5公斤手提行李", "20公斤托运行李 + 15公斤手提行李", "30公斤托运行李 + 10公斤手提行李", "2件行李，每件18公斤 + 7公斤手提行李", "2件行李，每件25公斤 + 5公斤手提行李", "10公斤托运行李 + 7公斤手提行李", "20公斤托运行李 + 10公斤手提行李", "35公斤托运行李 + 10公斤手提行李", "40公斤托运行李 + 5公斤手提行李", "45公斤托运行李 + 5公斤手提行李", "15公斤托运行李 + 5公斤手提行李", "23公斤托运行李 + 7公斤手提行李", "20公斤托运行李 + 12公斤手提行李", "30公斤托运行李 + 8公斤手提行李", "50公斤托运行李 + 7公斤手提行李", "30公斤托运行李 + 5公斤手提行李", "2件行李，每件23公斤 + 10公斤手提行李", "35公斤托运行李 + 5公斤手提行李", "28公斤托运行李 + 7公斤手提行李", "50公斤托运行李 + 5公斤手提行李", "30公斤托运行李 + 7公斤手提行李"]]}, "data_category": "text"}}}, "payment_records": {"display_name": "支付流水", "table_title_template": "支付流水记录表", "columns": {"流水号": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["PAY########", "TX########", "P########", "PMT########"]}, "data_category": "text"}, "交易时间": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d %H:%M:%S"}, "data_category": "date"}, "用户ID": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["UID####", "U######", "USER####"]}, "data_category": "text"}, "用户名": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "交易类型": {"generator_type": "categorical", "params": {"values": ["消费", "充值", "提现", "转账", "退款", "收款", "还款", "缴费", "投资", "分红", "借款", "红包", "工资", "报销", "奖金", "利息", "租金", "保险理赔", "退税"]}, "data_category": "text"}, "交易金额": {"generator_type": "numerical_range_formatted", "params": {"min_value": 0.01, "max_value": 100000, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}, "支付方式": {"generator_type": "categorical", "params": {"values": ["支付宝", "微信支付", "银行卡", "信用卡", "Apple Pay", "花呗", "借呗", "PayPal", "现金", "余额支付", "积分抵扣", "银联", "Google Pay", "云闪付", "快捷支付"]}, "data_category": "text"}, "交易状态": {"generator_type": "categorical", "params": {"values": ["成功", "失败", "处理中", "已退款", "部分退款", "已撤销", "已冻结", "待确认", "异常"]}, "data_category": "text"}, "支付渠道": {"generator_type": "categorical", "params": {"values": ["APP", "网页", "小程序", "H5", "扫码", "POS机", "自动柜员机", "线下门店", "智能终端", "语音助手", "第三方平台", "API接口"]}, "data_category": "text"}, "收款方": {"generator_type": "faker_company", "params": {"locale": "zh_CN"}, "data_category": "text"}, "交易备注": {"generator_type": "categorical", "params": {"values": ["订单支付成功", "感谢您的购买", "退款已处理", "支付失败，请重试", "订单已取消", "发货完成", "交易完成", "货到付款", "商品已发货", "支付成功", "正在处理中", "订单待确认", "付款成功，准备发货", "感谢您的支持", "订单确认中", "正在备货", "感谢光临", "订单正在配送", "商品退货中", "发货中", "付款处理中", "您的商品已发出", "订单取消成功", "退款处理中", "支付确认中", "交易处理中", "退货成功", "支付超时，请重新支付", "发货提醒", "订单已打包", "发货完成，等待送达", "已收到付款", "订单已发货，请查收", "交易成功", "订单已完成", "支付确认", "订单处理中", "库存不足，等待补货", "订单已退货", "商品已收回", "等待确认支付", "付款已到账", "商品补发中", "退货退款申请成功", "订单已处理", "支付后请耐心等待", "物流已安排", "退款成功", "支付失败，请联系", "订单已完成，感谢", "请尽快完成支付", "正在处理订单", "已为您处理退款", "订单已发货，正在途中", "支付确认成功", "已退款，感谢等待", "等待货物发出", "商品库存不足", "订单正在出库", "配送途中", "付款已完成", "订单待发货", "商品发出，预计送达", "已确认支付", "等待发货", "订单已转交快递", "正在进行中", "商品已发出，请耐心等候", "订单取消处理中", "退款中，请稍等", "订单已暂停", "支付成功，开始处理", "退款已处理完成", "发货待处理", "订单已确认", "处理中，请耐心等待", "已处理您的订单", "等待配送中", "商品已退货", "退款已到账", "订单已支付", "发货提醒，注意查收", "订单提交成功", "订单审核中", "订单创建成功", "商品已发货，请查收", "订单被取消", "商品正在包装中", "订单已取消，请知悉", "支付处理中", "配送已安排", "付款已确认", "订单已出库", "支付完成，准备发货", "订单已付款，正在处理", "商品已退回", "订单取消已完成", "商品补发完成"]}, "data_category": "text"}, "手续费": {"generator_type": "numerical_range_formatted", "params": {"min_value": 0, "max_value": 100, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}}}, "api_request_logs": {"display_name": "API请求日志", "table_title_template": "API请求日志数据表", "columns": {"请求ID": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["REQ########", "API#######", "R########", "RQ########"]}, "data_category": "text"}, "时间戳": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d %H:%M:%S.%f"}, "data_category": "date"}, "接口路径": {"generator_type": "categorical", "params": {"values": ["/api/v1/user/login", "/api/v1/user/logout", "/api/v1/user/register", "/api/v1/user/profile", "/api/v1/user/update", "/api/v1/user/delete", "/api/v1/products/list", "/api/v1/products/details", "/api/v1/products/create", "/api/v1/products/update", "/api/v1/products/delete", "/api/v1/orders/create", "/api/v1/orders/list", "/api/v1/orders/details", "/api/v1/orders/update", "/api/v1/orders/cancel", "/api/v1/orders/confirm", "/api/v1/cart/add", "/api/v1/cart/remove", "/api/v1/cart/clear", "/api/v1/cart/view", "/api/v1/payment/initiate", "/api/v1/payment/verify", "/api/v1/payment/status", "/api/v1/payment/refund", "/api/v1/payment/cancel", "/api/v1/shipping/address", "/api/v1/shipping/track", "/api/v1/shipping/status", "/api/v1/reviews/create", "/api/v1/reviews/list", "/api/v1/reviews/update", "/api/v1/reviews/delete", "/api/v1/notifications/list", "/api/v1/notifications/read", "/api/v1/notifications/mark-read", "/api/v1/admin/users", "/api/v1/admin/users/update", "/api/v1/admin/users/delete", "/api/v1/admin/orders", "/api/v1/admin/orders/update", "/api/v1/admin/products", "/api/v1/admin/products/update", "/api/v1/admin/products/delete", "/api/v1/admin/reports", "/api/v1/admin/settings", "/api/v1/categories/list", "/api/v1/categories/create", "/api/v1/categories/update", "/api/v1/categories/delete", "/api/v1/search/products", "/api/v1/search/orders", "/api/v1/cart/checkout", "/api/v1/cart/apply-coupon", "/api/v1/cart/remove-coupon", "/api/v1/order/payment-status", "/api/v1/order/track", "/api/v1/order/confirm", "/api/v1/order/rate", "/api/v1/order/returns", "/api/v1/order/refund-status", "/api/v1/payment/gateway-status", "/api/v1/payment/transaction-history", "/api/v1/settings/language", "/api/v1/settings/currency", "/api/v1/settings/theme", "/api/v1/user/password-reset", "/api/v1/user/email-confirm", "/api/v1/user/phone-confirm", "/api/v1/user/change-password", "/api/v1/user/security", "/api/v1/user/notifications", "/api/v1/user/subscription", "/api/v1/user/subscribe", "/api/v1/user/unsubscribe", "/api/v1/user/verify-token", "/api/v1/cart/save-later", "/api/v1/cart/restore", "/api/v1/cart/purchase", "/api/v1/cart/empty", "/api/v1/reports/sales", "/api/v1/reports/customers", "/api/v1/reports/inventory", "/api/v1/reports/finance", "/api/v1/reports/user-activity", "/api/v1/reports/products", "/api/v1/reports/traffic", "/api/v1/user/wishlist", "/api/v1/user/wishlist/add", "/api/v1/user/wishlist/remove", "/api/v1/products/similar", "/api/v1/products/recommended", "/api/v1/products/best-sellers", "/api/v1/products/new-arrivals", "/api/v1/products/discounts", "/api/v1/shipping/options", "/api/v1/shipping/calculate", "/api/v1/admin/dashboard", "/api/v1/admin/notifications", "/api/v1/admin/reports", "/api/v1/admin/analytics"]}, "data_category": "text"}, "请求方法": {"generator_type": "categorical", "params": {"values": ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"]}, "data_category": "text"}, "状态码": {"generator_type": "categorical", "params": {"values": ["200", "201", "204", "400", "401", "403", "404", "405", "409", "422", "429", "500", "502", "503", "504"]}, "data_category": "text"}, "响应时间": {"generator_type": "numerical_range_formatted", "params": {"min_value": 5, "max_value": 5000, "format_string": "{:d}ms"}, "data_category": "numeric"}, "调用方IP": {"generator_type": "categorical", "params": {"values": ["***********", "***********", "***********", "***********", "***********", "********", "********", "********", "********", "********", "**********", "**********", "**********", "**********", "**********", "***********", "***********", "***********", "***********", "***********", "*******", "*******", "*******", "*******", "*******", "2001:0db8:85a3:0000:0000:8a2e:0370:7334", "2001:0db8:85a3:0000:0000:8a2e:0370:7335", "************", "************", "************", "************", "************", "*********", "*********", "*********", "*********", "*********", "***************", "*********", "*********", "*********", "*********", "*********", "********", "********", "********", "********", "********", "***********", "***********", "***********", "***********", "***********", "**********", "**********", "**********", "**********", "**********", "***********", "***********", "***********", "***********", "***********", "172.18.0.1", "172.18.0.2", "172.18.0.3", "172.18.0.4", "172.18.0.5", "192.168.100.1", "192.168.100.2", "192.168.100.3", "192.168.100.4", "192.168.100.5", "***********0", "***********1", "***********2", "***********3", "***********4", "8.8.8.10", "8.8.8.20", "*******0", "1.1.1.20", "9.9.9.10", "9.9.9.20", "192.168.50.1", "192.168.50.2", "192.168.50.3", "192.168.50.4", "192.168.50.5", "10.2.2.1", "10.2.2.2", "10.2.2.3", "10.2.2.4", "10.2.2.5", "172.20.0.1", "172.20.0.2", "172.20.0.3", "172.20.0.4", "172.20.0.5", "128.1.1.1", "128.1.1.2", "128.1.1.3", "128.1.1.4", "128.1.1.5", "169.254.2.1", "169.254.2.2", "169.254.2.3", "169.254.2.4", "169.254.2.5"]}, "data_category": "text"}, "请求体大小": {"generator_type": "numerical_range_formatted", "params": {"min_value": 0, "max_value": 10000, "format_string": "{:d}B"}, "data_category": "numeric"}, "响应体大小": {"generator_type": "numerical_range_formatted", "params": {"min_value": 0, "max_value": 50000, "format_string": "{:d}B"}, "data_category": "numeric"}, "API版本": {"generator_type": "categorical", "params": {"values": ["v1.0", "v1.1", "v1.2", "v2.0", "v2.1", "v3.0", "beta"]}, "data_category": "text"}, "用户ID": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["UID####", "USER####", "U######"]}, "data_category": "text"}, "用户代理": {"generator_type": "categorical", "params": {"values": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36"]}, "data_category": "text"}, "错误信息": {"generator_type": "categorical", "params": {"values": ["", "参数验证失败", "资源不存在", "未授权访问", "权限不足", "请求超时", "服务暂不可用", "资源冲突", "请求过于频繁", "内部服务错误", "无效的请求体", "Token已过期", "签名验证失败", "用户已锁定", "资源已存在"]}, "data_category": "text"}, "请求参数": {"generator_type": "categorical", "params": {"values": ["{\"user_id\": 123}", "{\"username\": \"john_doe\"}", "{\"email\": \"<EMAIL>\"}", "{\"password\": \"securepassword123\"}", "{\"token\": \"abcd1234\"}", "{\"page\": 1, \"pageSize\": 20}", "{\"order_id\": 456}", "{\"product_id\": 789}", "{\"quantity\": 2}", "{\"price\": 199.99}", "{\"status\": \"pending\"}", "{\"address\": \"123 Main St, Springfield\"}", "{\"shipping_method\": \"express\"}", "{\"payment_method\": \"credit_card\"}", "{\"coupon_code\": \"SUMMER21\"}", "{\"discount\": 15.5}", "{\"currency\": \"USD\"}", "{\"amount\": 199.99}", "{\"order_date\": \"2025-05-01\"}", "{\"ship_date\": \"2025-05-02\"}", "{\"track_id\": \"TRK123456\"}", "{\"comments\": \"Please handle with care\"}", "{\"search_query\": \"laptop\"}", "{\"category_id\": 101}", "{\"category_name\": \"electronics\"}", "{\"sub_category\": \"laptops\"}", "{\"region\": \"North America\"}", "{\"country\": \"USA\"}", "{\"city\": \"New York\"}", "{\"zip_code\": \"10001\"}", "{\"phone_number\": \"+1234567890\"}", "{\"confirm_password\": \"securepassword123\"}", "{\"start_date\": \"2025-01-01\"}", "{\"end_date\": \"2025-12-31\"}", "{\"payment_status\": \"paid\"}", "{\"delivery_status\": \"shipped\"}", "{\"shipping_address\": \"123 Main St, Springfield\"}", "{\"billing_address\": \"456 Oak St, Springfield\"}", "{\"profile_picture\": \"image_url\"}", "{\"dob\": \"1990-01-01\"}", "{\"gender\": \"male\"}", "{\"terms_accepted\": true}", "{\"ip_address\": \"***********\"}", "{\"browser_info\": \"Chrome 90\"}", "{\"device_type\": \"mobile\"}", "{\"auth_code\": \"XYZ123\"}", "{\"referral_code\": \"REFERRAL2025\"}", "{\"review_score\": 5}", "{\"review_comment\": \"Excellent product!\"}", "{\"latitude\": 40.7128, \"longitude\": -74.0060}", "{\"image_url\": \"https://example.com/product.jpg\"}", "{\"video_url\": \"https://example.com/video.mp4\"}", "{\"file\": \"file_url\"}", "{\"order_type\": \"online\"}", "{\"cart_id\": 1001}", "{\"session_id\": \"abc123xyz\"}", "{\"language\": \"en\"}", "{\"theme\": \"dark\"}", "{\"timezone\": \"GMT-5\"}", "{\"event_id\": 202501}", "{\"ticket_type\": \"VIP\"}", "{\"seat_number\": \"A1\"}", "{\"payment_token\": \"PAY123TOKEN\"}", "{\"delivery_date\": \"2025-05-10\"}", "{\"delivery_time\": \"14:00\"}", "{\"is_gift\": true}", "{\"gift_message\": \"Happy Birthday!\"}", "{\"is_vip\": true}", "{\"coupon_value\": 10.0}", "{\"discount_code\": \"DISCOUNT10\"}", "{\"transaction_id\": \"TX12345\"}", "{\"invoice_id\": \"INV12345\"}", "{\"receipt_number\": \"RC12345\"}", "{\"product_rating\": 4}", "{\"is_subscribed\": true}", "{\"notification_type\": \"email\"}", "{\"shipping_fee\": 5.99}", "{\"extra_notes\": \"Leave at the door\"}", "{\"user_type\": \"customer\"}", "{\"is_active\": true}", "{\"is_verified\": true}", "{\"review_id\": 1001}", "{\"custom_field\": \"value\"}", "{\"shipping_provider\": \"FedEx\"}", "{\"sku\": \"ABC123\"}", "{\"is_express\": true}", "{\"is_gift_wrapped\": true}", "{\"promo_code\": \"PROMO50\"}", "{\"is_returned\": false}", "{\"tracking_number\": \"TRACK123\"}", "{\"address_line_1\": \"123 Main St\"}", "{\"address_line_2\": \"Apt 4B\"}", "{\"country_code\": \"US\"}", "{\"city_code\": \"NYC\"}", "{\"payment_gateway\": \"Stripe\"}", "{\"total_amount\": 199.99}", "{\"order_status\": \"completed\"}", "{\"items_count\": 3}", "{\"product_name\": \"<PERSON><PERSON><PERSON>\"}", "{\"discount_amount\": 10.0}", "{\"coupon_value\": 5.0}", "{\"discount_percentage\": 10}", "{\"sub_total\": 150.0}", "{\"grand_total\": 140.0}"]}, "data_category": "text"}}}, "news_release": {"display_name": "新闻稿", "table_title_template": "新闻稿数据表", "columns": {"新闻ID": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["NEWS####", "NR####", "N####", "PR####"]}, "data_category": "text"}, "标题": {"generator_type": "categorical", "params": {"values": ["苹果公司宣布新一轮融资计划", "三星品牌发布新款智能手机", "华为企业完成全球扩展", "阿里巴巴公司与国际巨头达成战略合作", "腾讯公司启动海外市场拓展", "京东企业推出全新产品线", "百度公司宣布收购腾讯企业", "索尼品牌推出限量版高端系列", "字节跳动公司完成C轮融资", "微软公司发布全新移动应用", "亚马逊企业开设全新办事处", "谷歌品牌发布创新解决方案", "Facebook公司宣布进行战略重组", "英特尔公司开始进军人工智能领域", "耐克品牌举行年度发布会", "小米公司正式进驻东南亚市场", "博世公司取得重大技术突破", "富士康企业发布季度财报", "沃尔玛公司开设首家旗舰店", "Spotify品牌推出全新营销计划", "沃尔玛公司完成首轮募资", "三星企业推出革命性产品", "特斯拉公司正式上市", "苹果品牌发布最新产品测试报告", "松下公司宣布战略并购计划", "华为企业发布全新创意广告", "戴尔公司宣布加强全球供应链管理", "施耐德品牌推出新型环保包装材料", "阿里巴巴公司推出新的定价策略", "腾讯企业投资创新科研项目", "三星品牌推出与明星联名系列", "台积电公司达成跨国并购协议", "华为公司发布年度财务报告", "索尼企业开启全球技术研发合作", "百度品牌取得全球专利认证", "京东公司发布全新社会责任计划", "谷歌公司发布智能家居新品", "苹果企业推出创新型服务平台", "腾讯公司宣布进入全球市场", "英特尔品牌推出全新智能设备", "微软公司推出新型电动汽车", "亚马逊公司启动国际化战略", "耐克品牌举办消费者回馈活动", "博世公司推进环保项目", "阿里巴巴品牌推出全新会员计划", "沃尔玛公司提升产品质量控制", "Spotify品牌发布年度行业报告", "Facebook公司宣布加强品牌推广", "特斯拉公司投资新的生产基地", "微软企业发布移动支付解决方案", "英特尔公司宣布技术合作协议", "字节跳动品牌发布全球市场报告", "亚马逊公司发布年度发展计划", "耐克品牌启动全球项目合作", "松下企业发布年度发展计划", "博世公司推出新一代智能硬件", "Facebook公司宣布新一代产品发布", "小米品牌发布创新型解决方案", "华为公司推出全球金融服务", "台积电品牌发布数字化战略", "京东公司发布全球业务拓展计划", "阿里巴巴品牌推出全球市场战略", "腾讯公司发布年度创新计划", "微软公司宣布进军新兴市场", "英特尔品牌发布数字化产品", "谷歌公司发布全球技术平台", "亚马逊公司宣布扩大生产线", "耐克品牌发布创新型技术方案", "博世公司宣布全球合作战略", "华为企业发布最新技术进展", "特斯拉公司发布年度市场报告", "小米品牌推出创新型服务平台", "字节跳动公司宣布全球扩展计划", "京东公司发布最新的财务成果", "阿里巴巴公司发布技术研发报告", "亚马逊品牌发布全球环保计划", "英特尔公司宣布增加研发投入", "微软公司发布新一代智能设备", "Facebook公司推出全球营销活动", "三星品牌推出智能家居新产品", "苹果公司启动全球推广计划", "华为品牌发布全新市场报告", "谷歌公司发布智能家居产品", "沃尔玛公司宣布新的物流网络", "耐克公司进行数字化转型", "特斯拉品牌发布智能出行解决方案", "亚马逊公司宣布进入跨境电商市场", "博世公司发布人工智能解决方案", "台积电公司推出智能硬件平台", "华为企业发布年终财务报告", "字节跳动公司启动全球技术合作", "阿里巴巴品牌发布最新产品设计", "腾讯公司推出全新消费者服务", "小米公司宣布加入5G通信网络", "Facebook品牌发布全球电商战略", "微软公司发布智能办公解决方案", "英特尔公司发布最新处理器", "沃尔玛公司宣布绿色供应链计划", "亚马逊企业发布客户反馈系统", "谷歌品牌推出智能城市解决方案", "耐克公司启动全球环保项目", "博世公司推出可持续发展计划", "阿里巴巴品牌发布年度科技趋势", "华为公司启动新一轮融资计划", "小米品牌发布智能家居解决方案", "字节跳动公司发布最新创新项目", "腾讯企业启动全球广告投放", "亚马逊品牌宣布全球市场拓展", "苹果公司发布年度研发报告", "特斯拉公司推出全新能源项目", "Facebook品牌推出最新支付工具", "谷歌公司发布跨国数字化战略", "耐克公司发布全球健康倡导计划", "微软企业宣布进军全球医疗健康市场", "京东公司宣布国际化战略升级", "华为品牌发布全球技术合作计划", "亚马逊公司推出智能物流平台", "博世公司发布年度技术创新报告", "微软公司发布跨国电子商务平台", "字节跳动公司发布全新品牌升级计划", "英特尔公司宣布进军智能硬件市场", "沃尔玛公司启动全球绿色战略", "亚马逊品牌发布全新智能产品", "小米公司宣布与谷歌达成战略合作", "华为公司启动跨境技术合作", "字节跳动品牌发布全球电商平台"]}, "data_category": "text"}, "发布日期": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d"}, "data_category": "date"}, "来源": {"generator_type": "categorical", "params": {"values": ["公司官网", "官方微博", "官方微信", "新闻发布会", "公司公告", "行业媒体", "科技博客", "投资者关系页面", "合作伙伴网站", "社交媒体", "行业论坛", "新闻网站", "公司App", "第三方数据分析", "微信公众号", "推特", "LinkedIn", "YouTube", "Instagram", "Snapchat", "Facebook", "百度新闻", "谷歌新闻", "知乎", "豆瓣", "新浪微博", "腾讯新闻", "网易新闻", "腾讯视频", "腾讯云", "天猫", "京东", "拼多多", "网易云音乐", "小红书", "快手", "抖音", "直播平台", "直播间", "线上新闻发布", "线下活动", "博主发布", "媒体报道", "用户分享", "产品评测", "大数据报告", "新闻稿", "品牌活动", "产品发布会", "投资人见面会", "创始人访谈", "产品体验", "跨平台广告", "活动报道", "微博话题", "线上广告", "搜索引擎", "合作媒体", "行业协会", "公司简报", "网络论坛", "团购平台", "线上教育平台", "网络广告", "产品展示会", "产品推荐", "营销活动", "客户支持页面", "客服热线", "产品使用手册", "电商平台", "直播电商", "社交平台群组", "粉丝网站", "社群互动", "跨界合作", "公益活动", "在线商店", "品牌官网", "用户评论", "网络口碑", "专题报道", "用户反馈", "电商活动", "公开演讲", "企业年报", "媒体采访", "集团新闻", "合作公告", "论坛话题", "产品评价网站", "测评网站", "电商直播", "市场调研报告", "数字营销平台", "朋友圈", "合作发布", "淘宝直播", "拼团平台", "外媒报道", "微博直播", "媒体采访", "公开信"]}, "data_category": "text"}, "作者": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "分类": {"generator_type": "categorical", "params": {"values": ["公司新闻", "产品发布", "财务报告", "行业动态", "技术创新", "市场营销", "投资动态", "公司活动", "管理层变动", "合作伙伴", "市场分析", "企业社会责任", "法律事务", "市场拓展", "战略规划", "股东大会", "并购重组", "国际化布局", "环保计划", "员工福利", "领导讲话", "产品更新", "财务数据", "资本运作", "股价走势", "年度总结", "产业链合作", "客户故事", "行业评测", "用户体验", "品牌建设", "市场调查", "投资建议", "股市分析", "企业合作", "产品功能", "商业模式", "管理创新", "企业文化", "市场预测", "活动报道", "商业拓展", "客户反馈", "线上活动", "科技成果", "供应链管理", "创新案例", "产品策略", "生态合作", "研发进展", "市场竞态", "行业展会", "新产品测试", "数字化转型", "行业标准", "品牌合作", "大数据分析", "行业评审", "市场竞争力", "产品测评", "产业趋势", "企业融资", "商业资讯", "产品试用", "用户推荐", "财务审计", "社会责任报告", "战略收购", "创新模式", "市场规模", "股东关系", "绿色环保", "业绩预告", "价格策略", "技术发展", "新市场开拓", "社交媒体营销", "线上推广", "技术合作", "行业奖项", "项目进度", "品牌推广", "创新挑战", "合作签约", "企业年会", "产品定价", "技术合作", "客户案例", "股东大会报告", "行业交流", "社会影响力", "资本市场", "合作模式", "供应商合作", "生态环保", "产品认证", "员工活动", "财务健康", "客户需求", "技术评审", "供应链创新", "跨界合作", "国际合作", "品牌发展", "客户支持", "产品展示", "企业战略", "业务拓展", "投资计划"]}, "data_category": "text"}, "阅读量": {"generator_type": "integer_range", "params": {"min": 100, "max": 1000000}, "data_category": "numeric"}, "转发数": {"generator_type": "integer_range", "params": {"min": 5, "max": 20000}, "data_category": "numeric"}}}, "library_lending": {"display_name": "图书馆借阅", "table_title_template": "图书馆借阅记录表", "columns": {"借阅编号": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["LIB########", "BOR######", "LB########", "L########"]}, "data_category": "text"}, "读者ID": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["R####", "RD####", "REA######", "USER####"]}, "data_category": "text"}, "读者姓名": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "读者类型": {"generator_type": "categorical", "params": {"values": ["本科生", "研究生", "博士生", "教师", "职工", "校外读者", "VIP读者", "特殊读者", "访问学者", "校友", "社会读者"]}, "data_category": "text"}, "图书ISBN": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["978-7-####-####-#", "978-0-###-#####-#", "979-1-####-####-#"]}, "data_category": "text"}, "图书名称": {"generator_type": "categorical", "params": {"values": ["人工智能导论", "数据结构与算法分析", "计算机网络原理", "数据库系统概念", "操作系统设计与实现", "软件工程实践", "机器学习基础", "深度学习", "大数据分析技术", "云计算架构设计", "物联网技术与应用", "信息安全原理", "编程语言实现", "计算机图形学", "集合论与图论", "高等数学", "线性代数", "离散数学", "概率论与数理统计", "微积分学教程", "量子力学", "固体物理学", "电动力学", "热力学与统计物理", "普通物理学", "有机化学", "无机化学", "物理化学", "分析化学", "生物化学", "分子生物学", "细胞生物学", "遗传学", "生态学", "进化生物学", "西方哲学史", "中国哲学史", "伦理学导论", "美学原理", "逻辑学基础", "世界史", "中国古代史", "中国近代史", "中国现代史", "世界经济学", "微观经济学", "宏观经济学", "金融学原理", "管理学原理", "市场营销学", "活着", "追风筝的人", "平凡的世界", "三体", "解忧杂货店", "岛上书店", "嫌疑人X的献身", "挪威的森林", "月亮与六便士", "百年孤独", "穆斯林的葬礼", "人类简史", "未来简史", "资治通鉴", "大卫·科波菲尔", "霍乱时期的爱情", "瓦尔登湖", "活下去的理由", "小王子", "时间简史", "动物庄园", "简爱", "悲惨世界", "唐吉诃德", "追忆似水年华", "百年孤独", "圣经", "福尔摩斯探案集", "大闹天宫", "心理学与生活", "如何高效阅读", "从零到一", "黑客与画家", "人性的弱点", "小宇宙", "计算机程序设计艺术", "思考，快与慢", "原则", "天才在左，疯子在右", "万历十五年", "活在当下", "万物简史", "人类的群星闪耀时", "成为", "秘密", "改变的力量", "曾国藩", "极简主义", "教父", "简约人生", "夜色温柔", "将进酒", "百年孤独", "摆渡人", "周易", "长尾理论", "第三类接触", "大数据时代", "丰乳肥臀", "月亮与六便士", "霍乱时期的爱情", "鲁迅全集", "战争与和平", "失落的秘符", "沉默的大多数", "杀死一只知更鸟", "卓越经理人的修炼", "高效能人士的七个习惯", "游戏改变世界", "反脆弱", "解忧杂货店", "白夜行", "时间的皱纹", "天使与魔鬼", "魔戒", "黑暗塔", "银河帝国", "去月球", "挪威的森林", "歌德全集", "武侠小说大系", "水浒传", "唐诗三百首", "明朝那些事儿", "资治通鉴", "草房子", "红与黑", "死亡诗社", "白雪公主", "无声告白", "解密", "名利场", "美丽新世界", "丧家之犬", "毒液", "狄仁杰探案全集", "远大前程", "贞观之治", "步步惊心", "孙子兵法", "愤怒的葡萄", "高原上的星星", "狼图腾", "最后的晚餐", "时间的潮流", "李鸿章传", "毛泽东传", "哈利·波特与魔法石", "猫鼠游戏", "青春纪实", "知行合一"]}, "data_category": "text"}, "作者": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "出版社": {"generator_type": "categorical", "params": {"values": ["人民文学出版社", "长江文艺出版社", "中信出版社", "作家出版社", "华东师范大学出版社", "译林出版社", "上海人民出版社", "商务印书馆", "新星出版社", "北京联合出版公司", "清华大学出版社", "中国工人出版社", "中国青年出版社", "社会科学文献出版社", "浙江人民出版社", "湖南文艺出版社", "中国广播电视出版社", "南海出版公司", "华文出版社", "中华书局", "陕西师范大学出版社", "科学出版社", "吉林出版集团", "人民出版社", "外文出版社", "中国大百科全书出版社", "山东画报出版社", "广西师范大学出版社", "中国社会科学出版社", "北京出版社", "四川人民出版社", "云南人民出版社", "华夏出版社", "北京大学出版社", "知识产权出版社", "中国社会出版社", "光明日报出版社", "中国法制出版社", "华中科技大学出版社", "天津人民出版社", "外语教学与研究出版社", "华东师范大学出版社", "福建人民出版社", "南京出版社", "中国经济出版社", "当代世界出版社", "重庆出版社", "首都经济贸易大学出版社", "现代出版社", "天津科技出版社", "安徽人民出版社", "北京时代华文书局", "中南出版传媒集团", "天桥出版社", "甘肃人民出版社", "现代教育出版社", "中国华侨出版社", "中国画报出版社", "北方文艺出版社", "香港中文大学出版社", "东南大学出版社", "甘肃科技出版社", "中国财政经济出版社", "国防工业出版社", "南方出版传媒公司", "北京科学技术出版社", "江西人民出版社", "上海科学技术出版社", "上海财经大学出版社", "内蒙古人民出版社", "北京外语教学与研究出版社", "沈阳出版社", "浙江大学出版社", "东北财经大学出版社", "新疆人民出版社", "安徽教育出版社", "中央编译出版社", "华东出版社", "中国民族出版社", "台海出版社", "湖北人民出版社", "中国农业出版社", "浙江文艺出版社", "石油工业出版社", "上海音乐出版社", "现代通信出版社", "澳门新世纪出版社", "文汇出版社", "北京理工大学出版社", "安徽文艺出版社", "新世界出版社", "中南大学出版社", "中国建设出版社", "军事科学出版社", "外文出版社", "兰州大学出版社", "无锡出版社", "东方出版社", "商务印书馆", "华东理工大学出版社", "北京国风出版社"]}, "data_category": "text"}, "借出日期": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d"}, "data_category": "date"}, "应还日期": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d"}, "data_category": "date"}, "实际归还日期": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d"}, "data_category": "date"}, "借阅状态": {"generator_type": "categorical", "params": {"values": ["借出", "已归还", "逾期未还", "续借", "预约", "遗失", "损坏", "正在处理"]}, "data_category": "text"}, "续借次数": {"generator_type": "integer_range", "params": {"min": 0, "max": 3}, "data_category": "numeric"}, "借阅地点": {"generator_type": "categorical", "params": {"values": ["图书馆一楼", "图书馆二楼", "市中心图书馆", "大学图书馆", "科技馆借阅处", "社区图书馆", "公共图书馆", "文化馆", "书店借阅区", "学校图书馆", "图书馆借书台", "大学生服务中心", "校园内书吧", "网络借阅平台", "乡村图书室", "市图书馆阅览室", "自助借阅机", "移动图书车", "区级图书馆", "市图书馆阅览室", "教育馆借阅区", "儿童图书馆", "市立图书馆", "书屋", "科技图书室", "小区图书角", "学校教辅中心", "学校电子书库", "图书馆阅览区", "办公室书架", "线上借阅平台", "学术图书馆", "远程借阅中心", "电子书借阅站", "书籍自助借阅机", "学校教室书架", "公共文化中心", "科研中心图书馆", "网吧借书处", "商业大厦借阅区", "公园图书馆", "图书馆文献中心", "新华书店", "网点自取借阅机", "数字图书馆", "邮局借阅点", "小区自取点", "旅游景区图书角", "公交车站借书点", "社区阅览室", "学术交流中心", "市文化馆", "专门书店", "网络借阅平台", "线上借书网站", "机场借书区", "车站借书角", "书店自取点", "公司阅览区", "茶馆借阅角", "校外阅览区", "学校宿舍借阅点", "商场图书角", "校园网络书库", "临时借阅点", "社区活动中心", "移动借阅站", "写字楼阅览室", "自行车站借书点", "酒店借书台", "乡镇图书室", "书架借阅点", "市图书分馆", "线上学习平台", "大学校园图书角", "青少年文化中心", "县图书馆", "历史博物馆阅览室", "剧院借阅点", "艺术馆图书区", "体育馆借书区", "生活服务中心", "艺术展览馆", "大厦阅览台", "企业借书站", "购物中心图书角", "食品广场借书区", "公共卫生图书馆", "博物馆图书馆", "科技研究中心", "数字文化图书馆", "学生会借书区", "大学生交流中心", "科普中心阅览室", "文艺中心借阅处", "旅行社借阅处", "学术园区图书角", "技术馆借阅区"]}, "data_category": "text"}}}, "iot_sensor_data": {"display_name": "IoT传感器数据", "table_title_template": "IoT传感器数据表", "columns": {"数据ID": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["IOT########", "SEN########", "DATA########", "SD########"]}, "data_category": "text"}, "设备ID": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["DEV####", "DEVICE######", "SN########", "IOT-DEV-####"]}, "data_category": "text"}, "设备名称": {"generator_type": "categorical", "params": {"values": ["温度传感器", "湿度传感器", "光照传感器", "气压传感器", "二氧化碳传感器", "运动传感器", "烟雾传感器", "门窗传感器", "水浸传感器", "振动传感器", "噪音传感器", "PM2.5传感器", "气体传感器", "电流传感器", "红外传感器", "超声波传感器", "热成像传感器", "GPS定位器", "智能摄像头", "能耗监测器"]}, "data_category": "text"}, "设备类型": {"generator_type": "categorical", "params": {"values": ["环境监测", "安防设备", "能源管理", "资产追踪", "健康监测", "工业监控", "农业监测", "建筑监测", "水质监测", "交通监测", "家庭自动化", "可穿戴设备", "环境监测", "安防设备", "能源管理", "家电设备", "智能家居", "医疗设备", "教育设备", "办公设备", "音响设备", "计算机设备", "通信设备", "汽车设备", "厨房设备", "交通监控", "智能穿戴设备", "测量仪器", "3D打印设备", "机器人设备", "无线设备", "传感器设备", "无人机设备", "投影设备", "电动工具", "办公自动化设备", "智能灯光设备", "激光设备", "空气净化设备", "水处理设备", "能源储存设备", "照明设备", "水质监测设备", "温控设备", "食品加工设备", "安防监控设备", "导航设备", "声学设备", "健康监测设备", "电力监控设备", "虚拟现实设备", "家用电器", "多媒体设备", "电子支付设备", "计算机外围设备", "电池管理设备", "光伏设备", "车载设备", "工业控制设备", "娱乐设备", "无人零售设备", "数据存储设备", "自动化设备", "自助设备", "电动汽车充电设备", "激光打印设备", "电视设备", "智能音响设备", "安防报警设备", "空调设备", "自动售货机设备", "高压设备", "机器人视觉设备", "实验室设备", "通信基站设备", "光纤设备", "智能开关设备", "智能电表", "电梯设备", "液晶显示设备", "智能电视设备", "无线通信设备", "视频监控设备", "智能地板设备", "车载导航设备", "智能手表设备", "智能体重秤设备", "电力设备", "电子设备", "生活家电", "声波设备", "热成像设备", "车载监控设备", "视听设备", "空气质量设备", "电动滑板车设备", "智能门锁设备", "智能洗衣机设备", "全景设备", "测量工具设备", "实验设备", "工业监控设备", "生物识别设备", "自动化生产设备", "自动化物流设备", "打印机设备", "水泵设备", "建筑监控设备", "液压设备", "高精度设备", "太阳能设备", "智能厨房设备", "厨房家电设备", "电动牙刷设备", "传动设备", "智能摄像头设备", "自适应控制设备", "商用音响设备", "嵌入式设备", "智能健身设备", "计算机网络设备", "检测仪器设备", "工业自动化设备", "智能机器人设备"]}, "data_category": "text"}, "位置": {"generator_type": "categorical", "params": {"values": ["办公室", "会议室", "走廊", "大厅", "车间", "厨房", "客厅", "卧室", "花园", "车库", "仓库", "实验室", "服务器机房", "配电室", "停车场", "大门", "屋顶", "围墙", "地下室", "田间", "温室", "道路", "桥梁", "河流", "办公室", "会议室", "走廊", "大厅", "车间", "实验室", "仓库", "厨房", "卫生间", "停车场", "电梯口", "楼梯间", "接待区", "休息区", "前台", "门厅", "大厅座位区", "展厅", "仓储区", "生产线", "工程室", "办公室前台", "文件室", "员工休息室", "打印区", "数据中心", "机房", "厨房角落", "会议室A", "会议室B", "休闲区", "客户服务区", "售后服务区", "客户接待室", "项目室", "资料室", "会议室C", "VIP接待区", "楼层通道", "空调机房", "洗手间", "书房", "网络机房", "品牌展示区", "会议室D", "公共区域", "会议室E", "多功能厅", "餐厅", "办公室后勤区", "外包工作区", "生产管理室", "研发部", "软件开发室", "技术支持区", "区域经理办公室", "系统维护室", "调度中心", "休闲娱乐区", "健康监测室", "仓库一楼", "仓库二楼", "楼顶花园", "阳台", "屋顶设备区", "电气室", "多媒体演播室", "综合服务区", "场地展示区", "员工会议室", "智能设备室", "包装区", "美术室", "财务办公室", "开发部", "研发实验室", "车库", "智能办公室", "机电设备区", "网络控制室", "网络机柜区", "监控中心", "清洁用具室", "化学实验室", "文件存储区", "音响设备室", "空调调节室", "垃圾处理区", "消防控制室", "客服中心", "技术培训室", "信息发布区", "无线网络调试区", "包装材料区", "行政办公室", "物资管理区", "接待室", "外卖接待区", "邮件存取区", "无障碍卫生间", "停车库", "电池充电区", "高级会议室", "大厅前台", "二楼工作区", "地面停车区", "保安室", "实验操作室", "供应商接待区", "数字化展示区", "信息技术部", "特需客户区"]}, "data_category": "text"}, "数据类型": {"generator_type": "categorical", "params": {"values": ["温度", "湿度", "光照强度", "气压", "二氧化碳浓度", "运动状态", "烟雾浓度", "开关状态", "水位", "振动强度", "噪音分贝", "PM2.5浓度", "气体浓度", "电流强度", "红外信号", "距离", "热成像", "GPS坐标", "图像数据", "能耗数据", "温度", "湿度", "光照强度", "气压", "二氧化碳浓度", "空气质量", "风速", "噪音级别", "PM2.5浓度", "PM10浓度", "紫外线指数", "辐射强度", "气体浓度", "甲烷浓度", "臭氧浓度", "氮氧化物浓度", "温差", "土壤湿度", "土壤温度", "水质pH值", "水温", "溶解氧", "COD浓度", "化学需氧量", "氨氮浓度", "悬浮物浓度", "电压", "电流", "电阻", "电力消耗", "功率", "频率", "数据吞吐量", "存储容量", "CPU负载", "内存使用率", "磁盘空间", "网络延迟", "带宽", "文件大小", "请求次数", "数据库响应时间", "数据传输速度", "HTTP响应时间", "API调用频率", "车辆速度", "车辆里程", "车辆油耗", "空气温度", "水位", "风向", "风速方向", "光照时间", "土壤养分", "降水量", "流量", "水流速", "气体流速", "火焰温度", "火灾烟雾浓度", "流量压力", "无线信号强度", "网络流量", "视频帧率", "图像分辨率", "图像对比度", "图像清晰度", "声音频率", "声音音量", "振动频率", "振动幅度", "电池电量", "电池温度", "电池电流", "温湿度差值", "雨量", "辐射强度值", "压强变化", "水流量", "湿度变化率", "空气湿度变化", "噪音强度", "辐射剂量", "电动机转速", "能量消耗量", "设备运行时间", "设备功率因数", "室内空气质量", "遥控信号强度", "太阳辐射强度", "海拔高度", "海水温度", "电池电压", "电池电流变化", "温湿度合成指数", "健康指标", "污染物浓度", "风力发电效率", "气体泄漏量", "空气流动速度", "土壤养分浓度", "电池寿命", "系统响应时间", "电池充电率", "机器温度", "设备状态", "设备健康指数"]}, "data_category": "text"}, "时间戳": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d %H:%M:%S.%f"}, "data_category": "date"}, "数值": {"generator_type": "numerical_range_formatted", "params": {"min_value": 0, "max_value": 1000, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}, "单位": {"generator_type": "categorical", "params": {"values": ["°C", "%RH", "lux", "hPa", "ppm", "是/否", "mg/m³", "开/关", "cm", "Hz", "dB", "μg/m³", "V", "A", "m", "°", "kWh", "pixel", "km/h", "m³", "°C", "%RH", "lux", "Pa", "ppm", "m/s", "dB", "ug/m³", "kg/m³", "m³/s", "L/min", "g/m³", "kW", "W", "V", "A", "Ω", "Hz", "Mbps", "Gbps", "km/h", "rpm", "kWh", "Wh", "m²", "cm²", "kg", "g", "mg", "L", "mL", "m", "cm", "mm", "μm", "km", "m³", "kPa", "MPa", "kg/s", "L/s", "m/s²", "N", "J", "W·h", "d", "s", "min", "h", "ft", "in", "yd", "mile", "sq.ft", "sq.m", "sq.in", "sq.yd", "g/m²", "kg/m²", "mg/m²", "Hz/m²", "mmHg", "cmHg", "ft-lb", "kJ", "eV", "Bq", "Gy", "Sv", "cps", "m/s²·s", "pH", "s/cm", "lm", "lm²", "cd", "mol/L", "g/mol", "Btu", "BTU/hr", "ft³", "cm³", "mL/s", "km/h·h", "inch", "foot", "yard", "m²/s", "L/h", "s/m", "ohm·m", "g/m²·day", "L/m²", "cm/h", "kPa·s", "J/kg", "mol", "moles", "m³/h", "cm/s", "kg/m", "g/s", "L·kg", "mV", "μV", "s/°C", "μg/L", "g·m²", "ppm·h", "ng/m³"]}, "data_category": "text"}, "状态": {"generator_type": "categorical", "params": {"values": ["正常", "异常", "离线", "警告", "超出范围", "低电量", "故障", "维护中", "无数据", "已校准", "待校准", "更新中", "重启中", "正常", "异常", "离线", "警告", "超出范围", "低电量", "故障", "运行中", "暂停", "已停用", "待机", "待处理", "忙碌", "恢复中", "不可用", "过载", "报警", "运行正常", "不可连接", "已连接", "初始化", "重启", "自检", "未授权", "待授权", "异常中", "系统繁忙", "验证失败", "缺少信号", "系统错误", "设备故障", "自动修复", "超时", "中断", "未响应", "在线", "离线维护", "数据丢失", "未启用", "准备就绪", "高温", "低温", "过期", "正在升级", "需要维护", "未配置", "配置错误", "传输失败", "数据错误", "调试中", "未识别", "连接失败", "过期认证", "处理中", "已解锁", "已加密", "已恢复", "待审核", "冻结", "启动中", "未启动", "服务中断", "初始化失败", "重试中", "正在解锁", "手动模式", "自动模式", "挂起", "运行中(暂停)", "重启中", "网络异常", "需要重启", "缺少资源", "资源充足", "备份中", "恢复失败", "不可操作", "系统异常", "待检测", "低速", "稳定", "高负载", "无响应", "已同步", "未同步", "已停止", "开机中", "关机中", "存储满", "空闲", "已卸载", "加载中", "恢复失败", "正在激活", "激活失败", "排队中", "正在安装", "安装失败", "恢复正常", "待处理事件", "未激活", "已封禁", "启用中", "禁用中", "低性能", "配置中", "需要修复", "验证中", "同步失败", "已备份", "恢复中", "设备未启用", "网络连接中", "连接中", "传输中"]}, "data_category": "text"}, "电池电量": {"generator_type": "percentage", "params": {"min": 0, "max": 100}, "data_category": "numeric"}, "信号强度": {"generator_type": "categorical", "params": {"values": ["优", "良", "中", "差", "无信号"]}, "data_category": "text"}}}, "insurance_claims": {"display_name": "保险理赔", "table_title_template": "保险理赔记录表", "columns": {"理赔编号": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["CLM########", "INS######", "IC########", "CLAIM######"]}, "data_category": "text"}, "保单号": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["POL########", "P########", "POLICY######", "INS-POL-####"]}, "data_category": "text"}, "客户姓名": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "保险类型": {"generator_type": "categorical", "params": {"values": ["车险", "寿险", "健康险", "意外险", "财产险", "旅游险", "责任险", "医疗险", "重疾险", "养老险", "教育险", "家财险", "企业财产险", "团体险", "车险", "寿险", "健康险", "意外险", "财产险", "旅行险", "航空险", "家庭财产险", "宠物险", "人身险", "企业财产险", "责任险", "综合险", "重大疾病险", "防盗险", "火灾险", "海上险", "水灾险", "失业险", "养老险", "教育险", "车上人员险", "第三者责任险", "车损险", "车玻璃险", "车盗抢险", "车划痕险", "车险附加险", "道路救援险", "医疗险", "住院险", "门诊险", "长期护理险", "人身伤害险", "交通事故险", "雇主责任险", "工伤险", "集体险", "团体险", "企业保险", "高风险险", "环保险", "气候险", "信用险", "建筑工程险", "车险责任险", "火灾责任险", "铁路运输险", "船舶险", "航空运输险", "外汇风险险", "农险", "农业保险", "农业灾害险", "生物保险", "农业种植险", "水产养殖险", "农业机械险", "农田设施险", "农业房屋险", "农业作物险", "信用贷款险", "购房险", "租赁险", "个人健康险", "住院医疗险", "医疗补充险", "学费险", "父母责任险", "定期寿险", "终身寿险", "年金保险", "双倍赔付险", "储蓄型险", "教育储蓄险", "重疾险", "癌症险", "团体健康险", "失能险", "护理险", "父母健康险", "家庭医疗险", "重大事故险", "机动车责任险", "遗产税险", "职工保险", "失业救济险", "公共责任险", "法律责任险", "知识产权险", "工程险", "创业险", "投资险", "银行保险", "投资理财险", "数字货币保险", "高端医疗险", "高风险健康险", "糖尿病险", "老年险", "人寿保险", "提前支取险", "跨境险"]}, "data_category": "text"}, "保险公司": {"generator_type": "categorical", "params": {"values": ["中国人寿保险公司", "平安保险公司", "太保人寿保险公司", "新华保险公司", "中国太平洋保险公司", "中国人民保险公司", "友邦保险公司", "阳光保险公司", "中邮人寿保险公司", "安邦保险公司", "大都会人寿保险公司", "中华联合保险公司", "富邦保险公司", "汇丰人寿保险公司", "民生保险公司", "国华人寿保险公司", "中华人寿保险公司", "国寿安保人寿保险公司", "泰康人寿保险公司", "人保财险公司", "中信保诚人寿保险公司", "大华保险公司", "万寿保险公司", "瑞士再保险公司", "中银保险公司", "中信保险公司", "合众人寿保险公司", "东吴人寿保险公司", "华泰保险公司", "苏黎世保险公司", "富安保险公司", "安盛天平保险公司", "中国大地保险公司", "美国国际集团", "中华联合财产保险公司", "国富保险公司", "贝尔保险公司", "都邦保险公司", "宝盛人寿保险公司", "华盛保险公司", "安盛保险公司", "人寿大都会保险公司", "恒安标准人寿保险公司", "长安责任保险公司", "达信保险公司", "理赔保险公司", "长城保险公司", "中国再保险公司", "安邦财产保险公司", "中华财产保险公司", "中银保险财产公司", "泛华保险公司", "大都会保险公司", "中国人寿财产保险公司", "世嘉保险公司", "天安保险公司", "平安财产保险公司", "安诚保险公司", "中意保险公司", "长生保险公司", "同方保险公司", "中财保险公司", "众安保险公司", "泰康财产保险公司", "华安保险公司", "天平保险公司", "新华财产保险公司", "华贵人寿保险公司", "安诚财产保险公司", "普惠保险公司", "天壕保险公司", "中保保险公司", "瑞士保险公司", "中国外运保险公司", "南京人寿保险公司", "富国保险公司", "优选保险公司", "万安保险公司", "中银保诚保险公司", "国富人寿保险公司", "安邦财产保险公司", "中国金融保险公司", "江泰保险公司", "华东保险公司", "宏利保险公司", "招商信诺人寿保险公司", "平安养老保险公司", "国寿保险公司", "中德安联人寿保险公司", "中国保险公司", "金盾保险公司", "和谐保险公司", "北京人寿保险公司", "华北人寿保险公司", "百年人寿保险公司", "上海人寿保险公司", "国民保险公司", "云上保险公司", "建信人寿保险公司", "安盛保险财产公司", "中信财产保险公司", "艾文保险公司", "华创保险公司"]}, "data_category": "text"}, "出险日期": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d"}, "data_category": "date"}, "申请日期": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d"}, "data_category": "date"}, "出险原因": {"generator_type": "categorical", "params": {"values": ["交通事故", "疾病", "自然灾害", "火灾", "盗窃", "水灾", "设备故障", "工伤", "暴风雨", "地震", "暴雪", "雷击", "洪水", "爆炸", "恐怖袭击", "盗窃行为", "人为破坏", "电力故障", "航空事故", "船舶事故", "煤气泄漏", "油品泄漏", "食物中毒", "急性疾病", "中暑", "过敏反应", "交通堵塞", "道路滑坡", "高温天气", "不当操作", "设备损坏", "宠物伤害", "家庭暴力", "施工事故", "建筑物坍塌", "滑倒摔伤", "迷路", "缺氧", "突发疾病", "流感", "病毒感染", "高空坠物", "屋顶漏水", "偷盗", "手机被盗", "火灾爆炸", "电器故障", "电梯故障", "邻里纠纷", "毒物接触", "误食毒品", "孕妇并发症", "车祸", "肠胃炎", "肺炎", "心脏病发作", "脑卒中", "癌症", "溺水", "中毒", "内伤", "外伤", "车祸受伤", "意外摔伤", "冰雹", "洗车误操作", "旅游意外", "航空延误", "急性中毒", "电器火灾", "建筑施工事故", "野外露营事故", "登山事故", "滑雪事故", "潜水事故", "火灾中毒", "气体泄漏", "管道爆裂", "战争", "恐怖袭击受伤", "环境污染", "粉尘暴露", "电击", "非法入侵", "冰冻损坏", "失火", "非自然死亡", "暴力袭击", "炸药爆炸", "高空作业事故", "危险化学品泄漏", "建筑外墙脱落", "寒潮天气", "雷暴", "盗窃造成损失", "无人机事故", "机械伤害", "电池爆炸", "交通肇事", "恶劣天气", "矿山事故", "雪崩", "飞行事故", "地质灾害", "风暴潮", "火山爆发", "矿物中毒"]}, "data_category": "text"}, "理赔金额": {"generator_type": "numerical_range_formatted", "params": {"min_value": 500, "max_value": 100000, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}, "理赔状态": {"generator_type": "categorical", "params": {"values": ["已申请", "审核中", "已赔付", "部分赔付", "拒赔", "补充材料中", "调查中", "待评估", "已撤销", "已结案", "申诉中", "重新评估"]}, "data_category": "text"}, "处理人员": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "处理时长": {"generator_type": "integer_range", "params": {"min": 1, "max": 30}, "data_category": "numeric"}, "关联文件": {"generator_type": "integer_range", "params": {"min": 1, "max": 10}, "data_category": "numeric"}}}, "vehicle_repair": {"display_name": "车辆维修", "table_title_template": "车辆维修记录表", "columns": {"维修单号": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["REP########", "VR########", "CW########", "WX########"]}, "data_category": "text"}, "车牌号": {"generator_type": "categorical_with_pattern", "params": {"prefixes": ["京", "沪", "粤", "津", "冀", "晋", "蒙", "辽", "吉", "黑", "苏", "浙", "皖", "闽", "赣", "鲁", "豫", "鄂", "湘", "粤", "桂", "琼", "渝", "川", "贵", "云", "藏", "陕", "甘", "青", "宁", "新"], "suffixes": ["A#####", "B#####", "C#####", "D#####", "E#####", "F#####", "G#####", "H#####", "J#####"]}, "data_category": "text"}, "车型": {"generator_type": "categorical", "params": {"values": ["大众帕萨特", "丰田卡罗拉", "本田雅阁", "奥迪A4L", "奔驰E级", "宝马3系", "福特福克斯", "雪佛兰科鲁兹", "日产天籁", "别克君越", "雷克萨斯ES", "凯迪拉克CTS", "沃尔沃S60", "马自达6", "起亚K5", "现代索纳塔", "吉利博瑞", "奇瑞艾瑞泽5", "长安CS75", "哈弗H6", "吉利帝豪", "宝骏510", "比亚迪秦", "荣威i5", "上汽大众途观L", "雪铁龙C5", "标致508", "讴歌TLX", "林肯MKZ", "英菲尼迪Q50", "奥迪Q5", "奔驰GLC", "宝马X5", "福特锐界", "丰田汉兰达", "本田CR-V", "日产X-Trail", "吉普自由光", "路虎发现神行", "沃尔沃XC60", "起亚智跑", "现代途胜", "雪佛兰探界者", "雪铁龙C3-XR", "长安CS35", "哈弗H2", "奇瑞瑞虎5", "奇瑞瑞虎8", "比亚迪宋", "荣威RX5", "传祺GS4", "东风风光580", "宝骏730", "江淮瑞风M4", "上汽大众途安", "五菱宏光", "奇瑞Tiggo 2", "广汽传祺GA6", "雪佛兰迈锐宝XL", "一汽奔腾B70", "北京现代悦动", "长安逸动", "东风日产轩逸", "上汽名爵6", "广汽传祺GA8", "丰田雷凌", "别克英朗", "本田飞度", "标致308", "雪铁龙C4L", "福特嘉年华", "日产逍客", "雪佛兰赛欧", "起亚K2", "大众高尔夫", "本田思域", "丰田RAV4", "奥迪A6L", "宝马5系", "奔驰C级", "雷克萨斯RX", "凯迪拉克XT5", "英菲尼迪QX50", "沃尔沃XC90", "路虎揽胜极光", "上汽大众帕萨特", "吉利远景", "奇瑞风云2", "江淮瑞风S5", "上汽大通MAXUS V80", "丰田兰德酷路泽", "本田Pilot", "日产途乐", "福特猛禽", "雪佛兰Tahoe", "雪铁龙C6", "比亚迪唐", "哈弗H9", "奇瑞虎8", "沃尔沃XC40"]}, "data_category": "text"}, "车辆品牌": {"generator_type": "categorical", "params": {"values": ["大众", "丰田", "本田", "别克", "奥迪", "宝马", "奔驰", "日产", "福特", "雪佛兰", "现代", "起亚", "吉利", "奇瑞", "长安", "哈弗", "比亚迪", "荣威", "标致", "雪铁龙", "林肯", "路虎", "沃尔沃", "凯迪拉克", "英菲尼迪", "斯巴鲁", "传祺", "广汽", "奇骏", "Jeep", "领克", "爱驰", "蔚来", "小鹏", "理想", "上汽", "一汽", "东风", "长城", "东风风光", "江淮", "宝骏", "比速", "红旗", "腾势", "广汽传祺", "欧拉", "沃尔沃", "路虎发现", "别克昂科威", "丰田皇冠", "本田雅阁", "雪佛兰科鲁兹", "现代索纳塔", "起亚K5", "吉利博瑞", "奇瑞艾瑞泽", "长安逸动", "哈弗H6", "比亚迪宋", "荣威RX5", "宝骏510", "广汽传祺GS4", "长城哈弗", "长安CS35", "雪铁龙C3", "丰田汉兰达", "大众途观", "本田CR-V", "奔驰GLC", "奥迪Q5", "沃尔沃XC60", "吉普自由光", "宝马X5", "雷克萨斯RX", "凯迪拉克XT5", "起亚智跑", "奇瑞瑞虎5", "吉利远景", "奇瑞虎8", "福特锐界", "广汽传祺GA6", "日产天籁", "雪佛兰赛欧", "丰田雷凌", "一汽奔腾", "本田飞度", "标致308", "雪铁龙C4L", "上汽名爵", "吉利帝豪", "别克英朗", "东风日产轩逸", "长安CS75", "比亚迪秦", "长安致尚XT", "现代途胜", "雪佛兰迈锐宝", "丰田RAV4", "奥迪A6L", "宝马3系", "奔驰E级", "凯迪拉克CTS", "雪铁龙C5", "英菲尼迪QX50"]}, "data_category": "text"}, "车主姓名": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "联系电话": {"generator_type": "faker_phone_number", "params": {"locale": "zh_CN"}, "data_category": "text"}, "维修类型": {"generator_type": "categorical", "params": {"values": ["常规保养", "故障维修", "事故维修", "保险理赔", "召回维修", "质保维修", "钣金喷漆", "轮胎更换", "电池更换", "空调维修", "发动机维修", "变速箱维修", "刹车系统", "悬挂系统", "电气系统", "内饰维修", "改装升级"]}, "data_category": "text"}, "维修项目": {"generator_type": "categorical", "params": {"values": ["机油更换", "机滤更换", "空滤更换", "汽滤更换", "刹车片更换", "刹车油更换", "火花塞更换", "变速箱油更换", "发动机清洗", "蓄电池更换", "轮胎更换", "轮胎修补", "冷却液更换", "刹车系统检查", "转向系统检查", "空调系统清洁", "空调滤芯更换", "发动机皮带检查", "发动机皮带更换", "防冻液更换", "燃油系统清洁", "喷油嘴清洗", "燃油滤芯更换", "车身底盘检查", "车灯检查与更换", "刮水器更换", "车内消毒", "车漆修复", "车窗玻璃修复", "变速箱修理", "发动机修理", "排气系统检查", "排气管修复", "轮毂修复", "传动轴检查", "动力系统检查", "悬挂系统检查", "车门检查", "车窗升降系统修复", "冷气管道检查", "天窗检查", "车内电气系统检查", "电瓶测试", "电池充电", "车载电脑故障诊断", "ABS系统检查", "ESP系统检查", "发动机气门调整", "发动机压缩测试", "离合器检查", "离合器更换", "转向助力液更换", "空调加氟", "汽车外观清洗", "轮胎动平衡调整", "车内空气净化", "车体防锈处理", "车身抛光打蜡", "座椅修复", "内饰清洁", "水箱修复", "发动机更换", "电动窗维修", "方向盘修复", "车载音响维修", "油门踏板更换", "刹车踏板检查", "发动机机脚胶检查", "空调系统更换", "水泵检查", "雨刮器电机更换", "车灯总成更换", "车内灯光更换", "车窗密封条更换", "车载充电器检查", "车顶行李架安装", "挡风玻璃修复", "油管检查", "防盗系统检查", "钥匙遥控器更换", "车内配件更换", "燃油泵更换", "汽车涂料修补", "机舱清洁", "空气流量计更换", "排气管清洗", "传感器更换", "行驶记录仪安装", "油箱修理", "刹车盘更换", "电动座椅维修", "车门锁修理", "油压检查", "引擎盖修复", "汽车防盗报警系统维修", "气囊检查", "挡泥板更换", "后视镜修复", "排气温度传感器更换", "车载导航系统修复", "行驶系统检测", "汽车声音系统检查", "全车复检", "全车喷漆", "车载空调系统维修"]}, "data_category": "text"}, "进厂时间": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d %H:%M"}, "data_category": "date"}, "完工时间": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d %H:%M"}, "data_category": "date"}, "维修技师": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "维修费用": {"generator_type": "numerical_range_formatted", "params": {"min_value": 100, "max_value": 50000, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}, "零件费用": {"generator_type": "numerical_range_formatted", "params": {"min_value": 50, "max_value": 30000, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}, "工时费用": {"generator_type": "numerical_range_formatted", "params": {"min_value": 50, "max_value": 20000, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}, "维修状态": {"generator_type": "categorical", "params": {"values": ["已接车", "检测中", "等待配件", "维修中", "已完工", "已交付", "返修中", "取消", "延期维修", "等待付款", "质保中"]}, "data_category": "text"}, "付款方式": {"generator_type": "categorical", "params": {"values": ["现金", "信用卡", "借记卡", "支付宝", "微信支付", "Apple Pay", "Google Pay", "PayPal", "银行转账", "银联支付", "分期付款", "现金支付", "二维码支付", "信用卡分期", "花呗", "京东支付", "苏宁支付", "美团支付", "京东白条", "余额宝", "刷卡支付", "代金券", "购物卡", "礼品卡", "积分兑换", "虚拟货币支付", "Paytm", "Ven<PERSON>", "<PERSON><PERSON>", "Stripe", "Amazon Pay", "Samsung Pay", "阿里云支付", "小程序支付", "店铺积分", "云闪付", "银行卡转账", "现金券", "闪付", "分期付", "多付", "社保卡支付", "京东金融支付", "快捷支付", "招商银行支付", "工行支付", "建设银行支付", "农业银行支付", "交通银行支付", "浦发银行支付", "信用卡还款", "支付宝花呗分期", "微信分期", "京东分期", "一卡通支付", "商户付款码", "家居卡", "会员卡", "支付宝收钱码", "加油卡支付", "宠物卡支付", "拼多多支付", "网络银行支付", "旅游卡", "票务卡", "预付卡", "短期信用支付", "大宗支付", "租赁支付", "车主卡支付", "保险卡支付", "快递卡支付", "电力卡支付", "水费卡支付", "邮政储蓄卡", "西联汇款", "MoneyGram", "Facebook Pay", "InstaPay", "Stripe Payments", "AliPay International", "OneCard", "Gift Cards", "<PERSON>oneer", "Cash on Delivery", "Green Dot", "Bitcoin", "Ethereum", "Litecoin", "<PERSON><PERSON><PERSON>", "Uphold", "Lemonade Pay", "Sezzle", "Afterpay", "<PERSON><PERSON><PERSON>", "Splitit", "Braintree", "Skrill", "Alipay HK", "WeChat HK", "Circle Pay", "KakaoPay", "Toss", "Rakuten <PERSON>", "Alipay Now", "WeChat Now", "Pundi X Pay", "Globe Pay"]}, "data_category": "text"}, "服务评分": {"generator_type": "integer_range", "params": {"min": 1, "max": 5}, "data_category": "numeric"}}}, "food_delivery": {"display_name": "外卖订单", "table_title_template": "外卖订单数据表", "columns": {"订单编号": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["WM########", "FD########", "ORDER######", "F########"]}, "data_category": "text"}, "用户ID": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["UID####", "U######", "USER####", "CUST######"]}, "data_category": "text"}, "用户姓名": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "联系电话": {"generator_type": "faker_phone_number", "params": {"locale": "zh_CN"}, "data_category": "text"}, "下单时间": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d %H:%M:%S"}, "data_category": "date"}, "商家名称": {"generator_type": "categorical", "params": {"values": ["川香酸菜鱼", "麻辣香锅", "汉堡王", "肯德基", "麦当劳", "必胜客", "华莱士", "味多美", "巴奴火锅", "湘鄂情", "小肥羊", "海底捞", "东来顺", "吉野家", "德克士", "大快活", "呷哺呷哺", "老乡鸡", "面包新语", "重庆小面", "酸菜鱼之家", "金鼎轩", "大食代", "汉餐馆", "尚品宅配", "火锅一号", "美滋滋", "张亮麻辣烫", "大龙燚火锅", "东南亚餐厅", "豪客来", "沙县小吃", "成都小吃", "醉鹅娘", "火宫殿", "辣味大排档", "千味涮", "小杨生煎", "川味火锅", "乌鲁木齐大盘鸡", "韩式烧烤", "抹茶点心屋", "粤菜馆", "汕头牛肉火锅", "海鲜自助餐", "吉祥馄饨", "鲍师傅", "大喜大", "阿甘锅盔", "汤姆之家", "丰味餐厅", "西贝莜面村", "绿茶餐厅", "蜂蜜小屋", "妈妈菜", "品味餐厅", "三顾冒菜", "串串香", "麻辣小龙虾", "袁记串串香", "圆缘火锅", "胖哥俩", "韩餐屋", "纯粹拉面", "乐凯撒", "生煎包", "牛肉面", "维尔顿餐厅", "烧烤达人", "源昌麻辣烫", "锅包肉", "馋嘴猫", "曹记泡馍", "煌上煌", "老妈米线", "翠华餐厅", "鲜芋仙", "鼎泰丰", "新味道", "乡村基", "二两生煎", "犀牛小镇", "煮妇厨房", "食客时代", "澳门豆捞", "酱香烤肉", "古老肉店", "幸福馄饨", "比萨手工坊", "金味餐厅", "老北京炸酱面", "食尚坊", "火星食堂", "过桥米线", "壹号烧烤", "牛仔餐厅", "悦达餐厅", "平江餐厅", "樱花日料", "九号餐厅", "外婆家", "潮汕砂锅粥", "滋味餐厅", "小龙坎火锅", "杨国福麻辣烫", "丧茶", "四季小厨", "新派快餐", "阿里郎韩国餐厅", "小南国", "胡辣汤", "大碗茶", "比萨客", "泰式风味", "永和大王", "每日黑巧", "辣妹子", "快餐小站", "千味涮肉", "鱼火锅"]}, "data_category": "text"}, "商家ID": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["SID####", "S######", "SHOP####", "VD######"]}, "data_category": "text"}, "订单内容": {"generator_type": "categorical", "params": {"values": ["红烧牛肉面+可乐", "黄焖鸡米饭(大)+冰红茶", "麻辣香锅(中)+米饭*2+雪碧", "宫保鸡丁+米饭", "炸鸡翅+薯条", "香辣小龙虾+啤酒", "牛排+沙拉", "酸辣粉+冰绿茶", "水煮鱼+米饭", "香煎三文鱼+果汁", "麻辣火锅(小)+饮料", "生煎包+豆浆", "炸春卷+啤酒", "披萨(大)+果汁", "碳烤鸡胸肉+矿泉水", "炸鸡+玉米浓汤", "番茄炒蛋+米饭", "酱爆鱿鱼+啤酒", "牛肉串+米饭", "卤味拼盘+冰水", "生蚝+青岛啤酒", "宫保鸡丁+米饭*2", "鱼香肉丝+米饭", "排骨饭+可乐", "麻辣香锅(大)+米饭", "牛肉馅饼+奶茶", "炸鱼薯条+可乐", "炖牛腩+米饭", "糖醋排骨+米饭", "东坡肉+米饭", "番茄牛腩面+酸奶", "红烧大虾+米饭", "回锅肉+米饭", "黑椒牛柳+米饭", "沙拉+鲜榨果汁", "咖喱鸡+米饭", "椒盐大虾+啤酒", "炸薯条+可乐", "炒年糕+橙汁", "日式炸鸡+米饭", "甜酸猪排+米饭", "爆炒虾仁+啤酒", "生煎包+豆浆", "炒米粉+绿茶", "炸鸡翅+橙汁", "日式拉面+绿茶", "红烧排骨+米饭", "宫保鸡丁+米饭*3", "北京烤鸭+啤酒", "羊肉串+米饭", "麻辣烫(大)+雪碧", "烤肉拼盘+啤酒", "海鲜炒饭+冰茶", "豉汁蒸排骨+米饭", "鱼头豆腐汤+米饭", "铁板烧+矿泉水", "小龙虾+冰啤酒", "茄汁虾仁+米饭", "炒土豆丝+米饭", "香菇滑鸡+米饭", "烧鸡翅+蔬菜沙拉", "泡菜炒饭+饮料", "酸菜鱼+米饭", "水煮牛肉+米饭", "香煎牛排+沙拉", "猪脚饭+可乐", "烤翅中+奶茶", "炸鸡+小菜", "素炒面+橙汁", "油炸臭豆腐+绿茶", "炒青菜+米饭", "火锅拼盘+啤酒", "干锅牛蛙+啤酒", "鱿鱼炒饭+绿茶", "蜜汁烤鸭+米饭", "回锅肉+米饭", "香煎鳕鱼+饮料", "炸串+可乐", "鸳鸯火锅+米饭", "酥炸大虾+啤酒", "糖醋里脊+米饭", "炖羊排+米饭", "白灼虾+啤酒", "酸辣面+饮料", "扒鸡+可乐", "日式炸酱面+绿茶", "铁板烧牛肉+米饭", "烤翅+啤酒", "香辣炸鸡+果汁", "炖排骨+米饭", "椒盐虾+绿茶", "韩式拌饭+饮料", "豆腐火锅+米饭", "黑椒牛排+蔬菜", "青椒肉丝+米饭", "香菜炒蛋+米饭", "铁板豆腐+米饭", "韩式泡菜炒饭+冰绿茶", "海鲜煲+米饭", "椒盐小龙虾+啤酒", "手抓羊肉+米饭", "排骨汤+米饭", "小笼包+茶", "干锅茄子+米饭", "香辣炸鸡翅+果汁", "泰式酸辣汤+米饭", "大盘鸡+米饭", "鸳鸯锅+啤酒", "海鲜粥+绿茶", "酱汁排骨+米饭", "红糖糍粑+可乐", "金针菇火锅+饮料", "牛肉火锅+米饭", "炸酱面+可乐", "麻辣串串+饮料", "小笼包+橙汁", "三杯鸡+米饭", "鱼香茄子+米饭", "麻辣小龙虾+饮料", "干锅田鸡+啤酒", "蒜蓉大虾+米饭", "酱烤翅+蔬菜沙拉", "泡椒凤爪+啤酒", "干锅花菜+米饭", "宫保鸡丁+饮料", "炸牛排+米饭", "田园沙拉+果汁", "炖汤排骨+米饭", "炒肝尖+米饭", "烤羊肉串+啤酒", "宫保豆腐+米饭", "香葱炒面+奶茶", "番茄鸡蛋面+可乐", "辣炒海带丝+米饭", "椒盐排骨+米饭", "海鲜沙拉+可乐", "鸭头+啤酒", "香煎大马哈鱼+米饭", "鲜虾粥+冰绿茶", "凉拌三丝+饮料", "手抓饭+可乐", "香煎鸡胸肉+沙拉", "牛肉煲+米饭", "炸牛肉串+啤酒", "黑椒牛柳+米饭", "鱼头泡饼+冰啤酒", "冰爽果汁+米饭", "大肚子炒饭+茶", "土豆炖牛肉+米饭", "果仁焗鸡+米饭", "蒜香大虾+米饭", "蜜汁烤鱼+米饭", "素三明治+可乐", "炸大肠+橙汁", "香草奶油面包+果汁", "香烤扇贝+啤酒", "五香牛肉干+奶茶", "泰式红咖喱+米饭", "牛肉炒面+啤酒", "酸辣酱鸡翅+米饭", "爆炒鸡丁+米饭", "椒盐鱿鱼+冰啤酒", "特色烧烤+果汁", "酥炸茄子+米饭", "紫菜蛋花汤+米饭", "麻辣水煮牛肉+米饭", "三文鱼刺身+饮料", "鲍汁花菇+米饭", "香辣虾仁+啤酒", "麻辣小火锅+饮料", "酸菜牛肉面+绿茶", "荔枝雪糕+奶茶"]}, "data_category": "text"}, "商品数量": {"generator_type": "integer_range", "params": {"min": 1, "max": 10}, "data_category": "numeric"}, "订单金额": {"generator_type": "numerical_range_formatted", "params": {"min_value": 10, "max_value": 200, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}, "配送费": {"generator_type": "numerical_range_formatted", "params": {"min_value": 1, "max_value": 10, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}, "优惠金额": {"generator_type": "numerical_range_formatted", "params": {"min_value": 0, "max_value": 30, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}, "实付金额": {"generator_type": "numerical_range_formatted", "params": {"min_value": 10, "max_value": 200, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}, "支付方式": {"generator_type": "categorical", "params": {"values": ["支付宝", "微信支付", "信用卡", "借记卡", "PayPal", "Apple Pay", "银联支付", "百度钱包", "京东支付", "银行卡转账", "现金", "扫码支付", "USDT", "比特币", "Google Pay", "Samsung Pay", "微信小程序支付", "京东白条", "淘宝信用支付", "分期付款", "支付宝余额", "京东支付宝", "支付宝花呗", "支付宝借呗", "京东金融", "微信红包支付", "美团支付", "携程支付", "百度钱包扫码", "QQ钱包", "芒果支付", "京东到家支付", "银联云闪付", "苏宁支付", "有赞支付", "阿里巴巴支付宝", "银联支付二维码", "云闪付快捷支付", "云闪付二维码", "联通支付", "移动支付", "电信支付", "车主支付", "小米支付", "滴滴支付", "快钱支付", "车主卡支付", "美团钱包", "京东扫码支付", "百度支付", "农行支付", "工行支付", "中行支付", "建行支付", "交通银行支付", "广发支付", "兴业银行支付", "平安支付", "华夏银行支付", "招商银行支付", "光大银行支付", "中信银行支付", "浦发银行支付", "汇丰银行支付", "光大支付", "招商支付", "银联卡支付", "代扣支付", "分期付款计划", "还款支付", "商城优惠券支付", "电商优惠支付", "会员卡支付", "购物卡支付", "电子钱包支付", "信用卡分期", "POS机支付", "跨境支付", "代付支付", "现金支付", "二维码支付", "便捷支付", "虚拟货币支付", "自提支付", "移动支付二维码", "扫码支付二维码", "银行卡支付", "账户余额支付"]}, "data_category": "text"}, "配送员": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "配送地址": {"generator_type": "faker_address", "params": {"locale": "zh_CN"}, "data_category": "text"}, "订单状态": {"generator_type": "categorical", "params": {"values": ["待付款", "待接单", "已接单", "商家已接单", "商家制作中", "待配送", "配送中", "已送达", "已完成", "已取消", "已退款", "退款中", "部分退款"]}, "data_category": "text"}, "配送时长": {"generator_type": "integer_range", "params": {"min": 15, "max": 120}, "data_category": "numeric"}, "评分": {"generator_type": "integer_range", "params": {"min": 1, "max": 5}, "data_category": "numeric"}}}, "hospital_registration": {"display_name": "医院挂号", "table_title_template": "医院挂号信息表", "columns": {"挂号单号": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["REG########", "GH########", "HR########", "H########"]}, "data_category": "text"}, "患者ID": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["PID####", "P######", "PT########", "PAT######"]}, "data_category": "text"}, "患者姓名": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "性别": {"generator_type": "categorical", "params": {"values": ["男", "女"]}, "data_category": "text"}, "年龄": {"generator_type": "integer_range", "params": {"min": 0, "max": 100}, "data_category": "numeric"}, "联系电话": {"generator_type": "faker_phone_number", "params": {"locale": "zh_CN"}, "data_category": "text"}, "证件号码": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["####################"]}, "data_category": "text"}, "医院名称": {"generator_type": "categorical", "params": {"values": ["北京协和医院", "北京大学第一医院", "北京大学人民医院", "复旦大学附属华东医院", "上海交通大学医学院附属瑞金医院", "上海华东医院", "中南大学湘雅医院", "南京军区总医院", "中山大学附属第一医院", "广州军区总医院", "武汉协和医院", "重庆医科大学附属医院", "西京医院", "四川大学华西医院", "天津医科大学总医院", "中国医学科学院肿瘤医院", "浙江大学医学院附属第一医院", "山东大学附属医院", "上海市第一人民医院", "上海市第六人民医院", "华中科技大学同济医院", "广州医科大学附属医院", "陕西省人民医院", "四川省人民医院", "山东省立医院", "重庆三峡医院", "南京市鼓楼医院", "长春市第一医院", "沈阳军区总医院", "哈尔滨医科大学附属第一医院", "郑州大学第一附属医院", "安徽省立医院", "北京友谊医院", "北京同仁医院", "上海华东医院", "江西省人民医院", "广西医科大学附属医院", "湖北省人民医院", "福建省立医院", "辽宁省人民医院", "江苏省人民医院", "陕西省人民医院", "山西省人民医院", "河北医科大学附属医院", "内蒙古医科大学附属医院", "云南省人民医院", "甘肃省人民医院", "吉林大学第一医院", "湖南省人民医院", "陕西省肿瘤医院", "黑龙江省医院", "南京中医药大学附属医院", "北京中医医院", "上海中医药大学附属医院", "广东省中医院", "浙江中医药大学附属医院", "福建中医药大学附属医院", "重庆中医药大学附属医院", "北京安贞医院", "上海市第九人民医院", "武汉同济医院", "广东省人民医院", "吉林省人民医院", "内蒙古自治区医院", "四川省人民医院", "青岛市市立医院", "浙江省人民医院", "北京儿童医院", "上海儿童医学中心", "南京儿童医院", "广州儿童医院", "成都中医药大学附属医院", "天津中医药大学附属医院", "广西医科大学附属医院", "宁波市第一医院", "杭州市第一人民医院", "南京市鼓楼医院", "温州市人民医院", "常州市第一人民医院", "唐山市中心医院", "邯郸市中心医院", "长春市中心医院", "沈阳市中医院"]}, "data_category": "text"}, "科室": {"generator_type": "categorical", "params": {"values": ["内科", "外科", "儿科", "妇产科", "眼科", "耳鼻喉科", "口腔科", "皮肤科", "神经科", "精神科", "肿瘤科", "中医科", "急诊科", "康复科", "检验科", "放射科", "超声科", "心血管内科", "消化内科", "呼吸内科", "神经内科", "内分泌科", "血液科", "风湿免疫科", "普外科", "骨科", "泌尿外科", "心胸外科", "神经外科", "整形外科"]}, "data_category": "text"}, "医生": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "挂号类型": {"generator_type": "categorical", "params": {"values": ["普通门诊", "专家门诊", "特需门诊", "急诊", "专科门诊", "预约门诊", "复诊", "会诊", "远程会诊", "义诊", "专病门诊", "夜间门诊"]}, "data_category": "text"}, "挂号费用": {"generator_type": "numerical_range_formatted", "params": {"min_value": 5, "max_value": 500, "decimals": 2, "format_string": "{:.2f}"}, "data_category": "numeric"}, "挂号日期": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d"}, "data_category": "date"}, "就诊时间段": {"generator_type": "categorical", "params": {"values": ["上午8:00-9:00", "上午9:00-10:00", "上午10:00-11:00", "上午11:00-12:00", "下午1:00-2:00", "下午2:00-3:00", "下午3:00-4:00", "下午4:00-5:00", "晚上6:00-7:00", "晚上7:00-8:00", "晚上8:00-9:00"]}, "data_category": "text"}, "挂号状态": {"generator_type": "categorical", "params": {"values": ["已预约", "已挂号", "待就诊", "就诊中", "已就诊", "已取消", "爽约", "退号", "转诊", "改期"]}, "data_category": "text"}, "挂号渠道": {"generator_type": "categorical", "params": {"values": ["现场挂号", "电话预约", "网上预约", "APP预约", "自助机", "社区转诊", "家庭医生", "第三方平台", "医保平台", "医院微信公众号"]}, "data_category": "text"}, "支付方式": {"generator_type": "categorical", "params": {"values": ["现金", "医保卡", "银行卡", "微信支付", "支付宝", "医院储值卡", "第三方支付"]}, "data_category": "text"}, "是否初诊": {"generator_type": "categorical", "params": {"values": ["是", "否"]}, "data_category": "text"}, "病历号": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["MR########", "BL########", "CHART######"]}, "data_category": "text"}}}, "meeting_attendance": {"display_name": "会议签到", "table_title_template": "会议签到记录表", "columns": {"签到ID": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["SIN####", "ATT####", "QD####", "M####"]}, "data_category": "text"}, "会议名称": {"generator_type": "categorical", "params": {"values": ["年度工作总结大会", "季度业绩评审会", "新产品发布会", "项目启动会", "团队建设大会", "行业研讨会", "营销策略讨论会", "投资者见面会", "公司年会", "产品技术交流会", "股东大会", "客户答谢会", "员工培训会议", "预算审定会", "市场分析大会", "品牌战略发布会", "销售激励大会", "创新科技论坛", "CEO专场演讲", "项目汇报会", "年度客户答谢晚宴", "供应链管理大会", "新市场开拓会议", "全球合作伙伴大会", "线上产品发布会", "数据安全讨论会", "管理层沟通会", "行业峰会", "年度财务报告会", "国际学术会议", "全球战略布局会议", "市场趋势研讨会", "产品需求讨论会", "团队沟通会", "品牌策划会", "供应商大会", "研发成果分享会", "客户满意度座谈会", "项目评审会", "社会责任报告会", "企业文化分享会", "企业数字化转型论坛", "跨国公司合作会", "云计算技术大会", "电子商务发展论坛", "区域市场大会", "年度创新大赛", "技术支持大会", "人才招聘会", "国际合作交流会", "产品培训会议", "年度市场推广会", "金融合作会议", "智能制造论坛", "行业政策研讨会", "创新项目发布会", "企业可持续发展会议", "员工福利大会", "企业并购洽谈会", "集团战略规划会", "跨部门协调会议", "法律合规培训会", "合作伙伴交流会", "电子产品展示会", "健康产业大会", "企业社会责任会议", "物流管理论坛", "人工智能技术论坛", "跨界合作交流会", "营销发展大会", "新零售讨论会", "全球技术大会", "在线业务拓展会议", "数据分析技术研讨会", "自动化生产会议", "新能源产业大会", "大数据应用论坛", "供应商交流会", "网络安全研讨会", "品牌战略洽谈会", "投资趋势会议", "线上客户交流会", "商业模式创新大会", "技术研发展会", "销售策略发布会", "跨行业合作会议", "行业技术论坛", "股东年会", "年度业绩报告会", "产品升级研讨会", "企业发展规划会议", "智能科技产品发布会", "未来发展讨论会", "企业领导力培训", "项目经验分享会", "市场竞争分析会", "人才发展大会", "国际投资研讨会", "绿色环保产业会议", "电商运营大会", "研发团队会议", "智能硬件发展论坛", "网络营销大会", "云服务战略发布会", "业务转型会议", "企业改革座谈会", "社会媒体战略研讨会", "全球技术合作会议", "投资洽谈会", "市场调研讨论会", "客户资源整合会", "销售渠道拓展会", "产业链合作会议", "精益生产论坛", "经济发展战略会议", "营销案例分享会", "数据交换讨论会", "产品体验交流会", "风险管理会议", "企业外部沟通会", "国际市场拓展会", "科技创新交流会", "人才激励大会", "创新理念发布会", "项目投资评审会", "新技术应用讨论会", "公司财务计划会", "企业文化建设研讨会", "电商平台战略会议", "商业模式发展论坛", "社会公益活动座谈会", "全球人才招聘会", "供应商合作座谈会", "企业健康管理大会", "客户需求分析会", "营销渠道整合会", "公司技术发展大会", "职业发展培训会", "合作协议签署会", "科技企业投资大会", "行业发展趋势会", "金融科技论坛", "消费者权益保护会议", "项目管理大会", "全球网络安全论坛", "产品市场推广会", "高管战略会议", "区域经济发展论坛", "行业领先技术论坛", "技术合作洽谈会", "企业融资推介会", "智能家居产业研讨会", "商业合作大会", "创新创意交流会", "全球资源整合会议", "企业品牌升级会议", "财务战略会议", "跨文化交流座谈会", "创意设计发展论坛", "制造业技术大会", "科技产业园发展论坛", "品牌塑造研讨会", "绿色能源发展论坛", "项目可行性分析会议", "产业转型升级大会", "员工职业规划会议", "物流发展趋势会议", "智能物流平台讨论会", "行业竞争策略会议", "人工智能产品展示会", "云计算应用大会", "跨平台业务协同会", "高端人才引进座谈会", "生产管理论坛", "全球资本市场会议", "业务合作战略会", "市场营销专业研讨会", "金融产品发布会", "企业技术演示会", "新创企业发展大会", "产品售后服务会议", "企业价值分享会", "技术创新应用论坛", "大数据技术研讨会", "跨境电商发展会议", "公司管理技术研讨会", "智能医疗产业论坛", "物联网技术大会", "智能城市发展座谈会", "供应链优化讨论会", "数字化转型会议", "产业创新论坛", "海外投资交流会", "运营管理创新会议", "人工智能应用发布会", "环保技术研讨会", "资本运作论坛", "企业战略目标会议", "投资回报分析会", "合作协议谈判会", "智能硬件产业大会", "跨行业资源整合会", "数据科技产业峰会", "全球市场趋势论坛", "企业产品设计会", "国际商务洽谈会", "创新技术分享会", "绿色建筑发展会议", "项目风险评估会", "跨行业创新大会", "产业投资论坛", "未来科技大会"]}, "data_category": "text"}, "参会人员": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "部门": {"generator_type": "categorical", "params": {"values": ["总裁办", "人力资源部", "财务部", "市场部", "销售部", "研发部", "技术部", "产品部", "运营部", "客服部", "法务部", "采购部", "供应链管理部", "公关部", "行政部", "IT部", "战略部", "风险管理部", "质量管理部", "投资部", "品牌部", "设计部", "数据分析部", "商务拓展部", "渠道管理部", "内容运营部", "信息技术部", "审计部", "创新部", "售后服务部", "财务共享服务部", "市场研究部", "企业文化部", "数字营销部", "客户关系管理部", "国际业务部", "合规部", "项目管理部", "人工智能部", "企业战略规划部", "电子商务部", "智能制造部", "供应商管理部", "用户体验部", "网络安全部", "人事行政部", "社区运营部", "企业信息部", "生产部", "物流部", "智能科技部", "大数据部", "健康管理部", "教育培训部", "生态发展部", "物联网部", "技术支持部", "合伙人关系部", "中小企业部", "数字转型部", "环境保护部", "企业外联部", "文化传播部", "品牌推广部", "顾客体验部", "智能化部", "项目拓展部", "融资部", "产品研发部", "法务合规部", "地方合作部", "媒体事务部", "跨境电商部", "市场拓展部", "政府事务部", "财务计划部", "内部审计部", "客服支持部", "数据隐私部", "外贸部", "广告销售部", "运营支持部", "人力资源发展部", "销售运营部", "客户服务中心", "政策与合作部", "企业发展部", "全球供应链部", "售后支持部", "客户体验部", "战略合作部", "创意与设计部", "行业发展部", "云计算部", "网络营销部", "智能产品部", "跨部门合作部", "知识产权部", "市场营销部", "新媒体部", "绩效管理部", "数据治理部", "国际市场部", "市场推广部", "招聘部", "产品运营部", "企业风险管理部", "社会责任部", "环境监控部", "广告公关部", "战略伙伴部", "客户管理部", "体验创新部", "企业分析部", "项目执行部", "管理支持部"]}, "data_category": "text"}, "签到时间": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d %H:%M:%S"}, "data_category": "date"}, "签到状态": {"generator_type": "categorical", "params": {"values": ["正常签到", "迟到", "早退", "缺席", "请假", "代签", "线上参会", "会后补签", "预约参会", "调休", "出差", "外勤签到", "临时请假"]}, "data_category": "text"}}}, "blog_posts": {"display_name": "博客文章", "table_title_template": "博客文章数据表", "columns": {"文章ID": {"generator_type": "alphanumeric_pattern", "params": {"patterns": ["POST####", "BLOG####", "P####", "ART####"]}, "data_category": "text"}, "标题": {"generator_type": "categorical", "params": {"values": ["如何有效提高工作效率", "10个实用的时间管理技巧", "职场新人必备的软技能", "远程工作的挑战与应对策略", "人工智能在日常生活中的应用", "数据分析入门指南", "提升创造力的七种方法", "有效沟通的艺术", "如何培养良好的阅读习惯", "写作技巧分享：如何写出吸引人的内容", "健康饮食的重要性", "每天15分钟的冥想练习", "旅行摄影技巧指南", "个人财务管理初步", "职业规划与发展路径", "学习编程的最佳资源", "如何培养批判性思维", "环保生活方式的简单改变", "提高情商的实用技巧", "心理健康自我关爱指南", "区块链技术入门指南", "5G时代的商业机会", "大数据分析案例解析", "元宇宙概念及其未来发展", "云计算服务选择指南", "家庭装修经验分享", "亲子教育中的常见问题", "中国传统文化的现代价值", "跨境电商运营策略", "居家办公环境布置技巧", "减压瑜伽基础入门", "咖啡爱好者必知的冲泡技巧", "城市摄影的构图技巧", "数字营销趋势分析", "社交媒体运营指南", "投资理财风险控制", "科技产品评测方法论", "自媒体内容创作策略", "健身初学者完全指南", "素食烹饪的营养平衡", "如何有效提高工作效率", "10个实用的时间管理技巧", "职场新人必备的软技能", "远程工作的挑战与应对策略", "人工智能在日常生活中的应用", "数据分析入门指南", "提升创造力的七种方法", "有效沟通的艺术", "如何培养良好的阅读习惯", "写作技巧分享：如何写出吸引人的内容", "健康饮食的重要性", "每天15分钟的冥想练习", "旅行摄影技巧指南", "个人财务管理初步", "职业规划与发展路径", "精益创业的五大核心法则", "提高领导力的十种方式", "职场心理学：如何在压力中保持冷静", "高效能人士的七个习惯", "人工智能与大数据的未来", "如何克服拖延症", "创造力的培养与训练方法", "公共演讲的技巧与艺术", "如何制定成功的年度计划", "个人品牌建设指南", "自我激励的五个方法", "如何保持工作与生活的平衡", "成功创业的第一步", "情商与领导力的关系", "职场中的人际沟通技巧", "如何提高阅读速度与理解力", "团队管理的艺术", "时间管理：如何掌控一天24小时", "如何克服职场中的自我怀疑", "职业规划与转型", "心态调整与情绪管理", "高效的电子邮件写作技巧", "职场沟通中的非语言技巧", "如何实现工作与生活的双赢", "如何建立自信心", "数字化转型的最佳实践", "如何在忙碌中保持专注", "一小时内完成任务的秘诀", "个人理财：投资与储蓄技巧", "高效会议管理的技巧", "提高团队协作能力", "工作中的情绪管理技巧", "如何管理多重任务", "应对职场压力的十大方法", "如何在工作中保持积极心态", "职场中的谈判技巧", "网络安全与个人隐私保护", "成功的秘诀：从失败中学习", "如何提高工作效率", "有效的团队沟通技巧", "数字化工具如何改变工作方式", "如何进行有效的目标设定", "从零开始学项目管理", "职场中的心理学技巧", "如何写一封成功的求职信", "职场中的沟通障碍及应对方法", "克服工作中的焦虑情绪", "有效时间管理的五个技巧", "提升自我效率的五种方法", "如何保持工作动力", "团队协作中的常见误区", "如何在职场中脱颖而出", "如何有效地管理你的时间", "职场中的冲突解决策略", "如何提高自己的工作效率", "管理者的时间管理法则", "如何提升自己的职业技能", "高效工作法则", "时间管理与任务优先级", "高效办公的必备技巧", "如何提高决策能力", "工作中的自我管理技巧", "职场中的沟通误区", "成功的职业发展路径", "团队协作中的信任建立", "如何改善工作中的效率", "如何保持高效的工作状态", "提高工作产出的十个方法", "从失败中汲取的成功经验", "如何避免职场中的常见误区", "提升职场竞争力的五个技巧", "如何设定个人职业目标", "高效能工作模式的五大秘诀", "如何改善自己的工作流程", "高效领导力的七个要素", "有效的反馈技巧", "如何在职场中提升自己的存在感", "如何克服职场中的拖延症", "工作中的心理压力应对", "职场中常见的沟通误区", "如何做出更好的职业决策", "如何提升团队的执行力", "高效团队建设的技巧", "如何提高职场中的影响力", "如何制定职业规划", "如何提升自己的情商", "职场中的自我成长技巧", "团队协作中的常见问题及解决方法", "如何管理职场中的人际关系", "职场沟通中的关键技巧", "提高效率的思维方式", "如何提高工作动力", "职场进阶的五个技巧", "如何与同事有效沟通", "如何平衡工作与家庭", "如何规划自己的职场生涯", "如何增强职场中的自信心", "提高工作效率的科学方法", "职场中的情绪调节技巧", "如何应对职场中的不公平待遇", "工作中的情绪智能提升技巧", "如何提高自己的组织能力", "高效沟通的关键要素", "提高工作效率的心理学技巧", "从失败中汲取职场经验", "如何提升自己的职场能力", "如何突破职场中的瓶颈", "职场中的核心竞争力", "如何提升自我管理能力", "情商在职场中的应用", "职场中的时间管理法则", "如何实现工作目标", "如何在工作中保持专注", "职场中的个人定位", "如何提升职业素养", "如何在职场中获得成功", "提升职场效率的技巧", "如何管理工作中的压力", "如何避免工作中的错误", "职场人际关系的沟通技巧", "如何平衡工作和个人生活", "提升职场竞争力的八个秘诀", "如何处理职场中的压力", "如何管理好自己的时间", "职场中的团队建设技巧", "提升职业能力的五个关键", "如何有效制定个人目标", "职场沟通的语言技巧", "工作中的自我激励技巧", "提升职场影响力的方法", "如何培养高效的工作习惯", "职场中的领导技巧", "如何提升决策能力", "成功职场人必备的素质", "如何有效应对职场挑战", "工作中的高效思维方式", "如何提升自己的情商", "成功的职场生涯规划", "如何实现职业目标", "职场心理学的应用", "团队管理中的时间管理技巧", "职场中的自我提升之路", "如何提高工作效率的五个步骤", "如何平衡工作与学习", "如何有效进行职业规划"]}, "data_category": "text"}, "作者": {"generator_type": "faker_name", "params": {"locale": "zh_CN"}, "data_category": "text"}, "发布日期": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d"}, "data_category": "date"}, "最后更新": {"generator_type": "date_range", "params": {"start_year": 2023, "end_year": 2023, "format": "%Y-%m-%d"}, "data_category": "date"}, "分类": {"generator_type": "categorical", "params": {"values": ["职场", "技术", "生活", "健康", "教育", "创业", "艺术", "旅行", "美食", "心理", "财务", "环保", "科技", "社会", "文化", "个人成长", "产品评测", "时尚", "汽车", "育儿", "宠物", "摄影", "音乐", "电影", "游戏", "体育", "历史", "政治", "经济", "家居", "园艺", "手工", "数码", "法律", "医疗"]}, "data_category": "text"}, "标签": {"generator_type": "categorical", "params": {"values": ["效率, 工作, 技巧", "时间管理, 计划, 工具", "软技能, 职场, 新人", "远程工作, 居家办公, 协作", "AI, 科技, 应用", "数据, 分析, 入门", "创造力, 灵感, 创新", "沟通, 表达, 技巧", "阅读, 学习, 习惯", "写作, 内容, 技巧", "健康, 饮食, 营养", "冥想, 正念, 压力管理", "旅行, 摄影, 技巧", "财务, 理财, 预算", "职业, 规划, 发展", "编程, 学习, 资源", "思维, 批判, 逻辑", "环保, 可持续, 生活", "情商, <PERSON><PERSON>, 人际关系", "心理健康, 自我关爱, 压力", "区块链, 技术, 入门", "5G, 时代, 商业机会", "大数据, 分析, 案例", "元宇宙, 概念, 未来发展", "云计算, 服务, 选择", "家庭装修, 经验, 分享", "亲子教育, 常见问题, 解决", "传统文化, 现代价值, 传承", "跨境电商, 运营策略, 实施", "居家办公, 环境布置, 技巧", "减压瑜伽, 基础入门, 方法", "咖啡爱好者, 冲泡技巧, 分享", "城市摄影, 构图技巧, 实践", "数字营销, 趋势分析, 策略", "社交媒体, 运营指南, 实践", "投资理财, 风险控制, 方法", "科技产品, 评测方法论, 实践", "自媒体内容, 创作策略, 实践", "健身初学者, 完全指南, 方法", "素食烹饪, 营养平衡, 实践"]}, "data_category": "text"}, "字数": {"generator_type": "integer_range", "params": {"min": 500, "max": 5000}, "data_category": "numeric"}, "阅读时间": {"generator_type": "numerical_range_formatted", "params": {"min_value": 2, "max_value": 20, "decimals": 0, "format_string": "{:d}分钟"}, "data_category": "numeric"}, "浏览量": {"generator_type": "integer_range", "params": {"min": 100, "max": 100000}, "data_category": "numeric"}, "点赞数": {"generator_type": "integer_range", "params": {"min": 10, "max": 5000}, "data_category": "numeric"}, "评论数": {"generator_type": "integer_range", "params": {"min": 0, "max": 1000}, "data_category": "numeric"}, "分享数": {"generator_type": "integer_range", "params": {"min": 5, "max": 2000}, "data_category": "numeric"}, "状态": {"generator_type": "categorical", "params": {"values": ["已发布", "草稿", "待审核", "已归档", "置顶", "精选", "热门", "违规", "已撤回", "审核未通过", "定时发布", "私密", "仅好友可见", "外部转载"]}, "data_category": "text"}, "封面图": {"generator_type": "categorical", "params": {"values": ["有", "无"]}, "data_category": "text"}}}}