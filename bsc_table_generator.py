#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import json
import random
import time
import imgkit
import importlib
import multiprocessing as mp
from typing import List, Dict, Tuple, Any, Optional
from datetime import datetime
from functools import partial

# Configure imgkit to use wkhtmltopdf executable
# Adjust this path based on your wkhtmltopdf installation
WKHTMLTOPDF_PATH = r'C:\Program Files\wkhtmltopdf\bin\wkhtmltoimage.exe'
if os.path.exists(WKHTMLTOPDF_PATH):
    config = imgkit.config(wkhtmltoimage=WKHTMLTOPDF_PATH)
else:
    # Try to use system PATH
    config = None

# 输出目录常量
OUTPUT_FOLDER_BLOCKED = os.path.join("表格", "分块表")

# 用于跟踪已经使用过的主题组合，避免重复
_used_theme_combinations = set()

# 用于从config目录导入主题的函数
def load_theme_from_config(theme_name: str) -> Dict:
    """从config目录中加载指定主题的配置数据"""
    try:
        # 动态导入模块
        module = importlib.import_module(f"config.{theme_name}")
        # 返回与模块名称相同的变量
        return getattr(module, theme_name)
    except (ImportError, AttributeError) as e:
        print(f"无法加载主题 '{theme_name}': {e}")
        return {}

def get_available_themes() -> List[str]:
    """获取config目录下所有可用的主题名称"""
    config_dir = "config"
    themes = []
    
    # 检查config目录是否存在
    if not os.path.isdir(config_dir):
        print(f"警告: 找不到配置目录 '{config_dir}'")
        return themes
    
    # 遍历config目录中的所有.py文件
    for filename in os.listdir(config_dir):
        if filename.endswith(".py") and filename != "__init__.py" and not filename.startswith("__"):
            # 去掉.py后缀获取主题名称
            theme_name = filename[:-3]
            themes.append(theme_name)
    
    return themes

def select_random_themes(count: int = 4, avoid_recent: bool = True) -> List[str]:
    """随机选择指定数量的主题，避免最近使用过的组合
    
    参数:
    - count: 要选择的主题数量
    - avoid_recent: 是否避免最近使用过的组合
    
    返回:
    - 选择的主题列表
    """
    all_themes = get_available_themes()
    
    if not all_themes:
        print("错误: 未找到可用的主题文件")
        return []
    
    # 如果请求的主题数量大于可用主题数量，则返回所有主题
    if count >= len(all_themes):
        print(f"警告: 请求的主题数量({count})大于可用主题数量({len(all_themes)})。返回所有可用主题。")
        return all_themes
    
    # 避免重复组合的逻辑
    if avoid_recent and len(all_themes) >= count*2:  # 确保有足够的主题可选
        max_attempts = 10  # 最大尝试次数，避免无限循环
        for attempt in range(max_attempts):
            # 随机选择指定数量的主题
            selected_themes = random.sample(all_themes, count)
            
            # 对主题列表排序，以便相同组合的不同顺序被视为相同
            combo_key = tuple(sorted(selected_themes))
            
            # 如果这个组合不在最近使用的集合中，则使用它
            if combo_key not in _used_theme_combinations:
                # 添加到已使用集合
                _used_theme_combinations.add(combo_key)
                
                # 限制集合大小，避免内存无限增长
                if len(_used_theme_combinations) > 1000:  # 记住最近的1000个组合
                    try:
                        _used_theme_combinations.pop()  # 移除一个元素
                    except KeyError:
                        pass
                
                return selected_themes
        
        # 如果尝试多次后仍然找不到新组合，则返回最后一次选择的组合
        print("警告: 无法找到未使用过的主题组合，使用重复组合")
        return selected_themes
    
    # 如果不需要避免重复或者可选主题不足，直接随机选择
    selected_themes = random.sample(all_themes, count)
    return selected_themes

def generate_random_question(theme_display_name: str, category: str) -> str:
    """根据主题和类别生成随机引导性问题
    
    参数:
    - theme_display_name: 主题显示名称
    - category: 问题类别 (financial, customer, internal, learning)
    
    返回:
    - 生成的问题字符串
    """
    # 为每个类别准备多种问题模板
    question_templates = {
        "financial": [
            f"如何优化{theme_display_name}以获得可持续的财务表现？",
            f"我们应该如何改进{theme_display_name}来提高财务收益？",
            f"在{theme_display_name}方面，哪些策略能带来最大的财务效益？",
            f"为了实现财务目标，{theme_display_name}应该如何发展？",
            f"怎样调整{theme_display_name}来确保长期财务健康？"
        ],
        "customer": [
            f"如何改善{theme_display_name}以提升客户满意度和忠诚度？",
            f"在{theme_display_name}方面，客户最看重什么？",
            f"如何利用{theme_display_name}来吸引和留住更多客户？",
            f"我们应该如何调整{theme_display_name}来满足不同客户群体的需求？",
            f"什么样的{theme_display_name}创新能带来卓越的客户体验？"
        ],
        "internal": [
            f"如何完善{theme_display_name}以提高运营效率和质量？",
            f"在{theme_display_name}流程中，哪些环节需要优化？",
            f"如何重构{theme_display_name}以适应业务转型？",
            f"我们能通过哪些方式提高{theme_display_name}的内部协同效率？",
            f"在{theme_display_name}中，如何平衡速度与质量？"
        ],
        "learning": [
            f"如何加强{theme_display_name}以促进创新和持续成长？",
            f"{theme_display_name}领域中，我们应该发展哪些核心能力？",
            f"如何构建{theme_display_name}的知识管理和分享机制？",
            f"在{theme_display_name}方面，团队需要哪些新技能？",
            f"如何将{theme_display_name}的最佳实践转化为组织优势？"
        ]
    }
    
    # 获取相应类别的问题模板，如果类别不存在，使用通用模板
    templates = question_templates.get(category, [f"如何改进{theme_display_name}？"])
    
    # 随机选择一个问题模板
    return random.choice(templates)

def generate_bsc_content(themes: List[str]) -> Dict[str, Any]:
    """根据所选主题生成BSC(平衡计分卡)内容"""
    if len(themes) < 4:
        print(f"警告: 生成完整BSC需要4个主题，当前只有{len(themes)}个。将使用默认内容补充。")
        # 补充主题直到达到4个
        while len(themes) < 4:
            themes.append(f"default_theme_{len(themes)+1}")
    
    # 加载选定的主题数据
    theme_data = {}
    for theme in themes:
        theme_data[theme] = load_theme_from_config(theme)
    
    # 提取各主题的关键特性用于生成内容
    theme_keywords = {}
    for theme, data in theme_data.items():
        if not data:  # 如果主题数据为空，使用默认值
            theme_keywords[theme] = {"display_name": f"未知主题({theme})", "keywords": [theme]}
            continue
        
        # 提取显示名称和关键词
        display_name = data.get("display_name", theme)
        
        # 从columns中提取关键字
        columns = data.get("columns", {})
        keywords = list(columns.keys())
        
        # 如果有categorical字段，也提取它们的值作为关键词
        for col_name, col_config in columns.items():
            if isinstance(col_config, dict) and col_config.get("generator_type") == "categorical":
                values = col_config.get("params", {}).get("values", [])
                if values:
                    # 只添加前5个值作为关键词，避免过多
                    # 修复slice错误：确保values是可迭代的并且可以被切片
                    if isinstance(values, list) and len(values) > 0:
                        # 添加前5个元素或全部（如果少于5个）
                        keywords.extend(values[:min(5, len(values))])
        
        theme_keywords[theme] = {
            "display_name": display_name,
            "keywords": keywords[:10]  # 限制关键词数量
        }
    
    # 随机决定是否打乱主题顺序，增加变化性
    if random.random() > 0.5:
        random.shuffle(themes)
    
    # 分配主题到BSC的四个区块
    # 如果themes列表长度>=4，可以确保每个区块使用不同主题
    # 财务区块
    financial_theme = themes[0]
    financial_keywords = theme_keywords[financial_theme]["keywords"]
    financial_display = theme_keywords[financial_theme]["display_name"]
    
    # 客户区块
    customer_theme = themes[1]
    customer_keywords = theme_keywords[customer_theme]["keywords"]
    customer_display = theme_keywords[customer_theme]["display_name"]
    
    # 内部运营区块
    internal_theme = themes[2]
    internal_keywords = theme_keywords[internal_theme]["keywords"]
    internal_display = theme_keywords[internal_theme]["display_name"]
    
    # 学习成长区块
    learning_theme = themes[3]
    learning_keywords = theme_keywords[learning_theme]["keywords"]
    learning_display = theme_keywords[learning_theme]["display_name"]
    
    # 为每个象限生成自定义标题，基于主题
    # 财务象限的可能标题
    financial_titles = [
        f"{financial_display}效益", 
        f"{financial_display}绩效", 
        f"{financial_display}回报", 
        f"{financial_display}价值",
        f"{financial_display}收益"
    ]
    
    # 客户象限的可能标题
    customer_titles = [
        f"{customer_display}体验", 
        f"{customer_display}满意度", 
        f"{customer_display}服务", 
        f"{customer_display}关系",
        f"{customer_display}互动"
    ]
    
    # 内部运营象限的可能标题
    internal_titles = [
        f"{internal_display}流程", 
        f"{internal_display}运营", 
        f"{internal_display}管理", 
        f"{internal_display}效率",
        f"{internal_display}系统"
    ]
    
    # 学习成长象限的可能标题
    learning_titles = [
        f"{learning_display}能力", 
        f"{learning_display}发展", 
        f"{learning_display}创新", 
        f"{learning_display}提升",
        f"{learning_display}进步"
    ]
    
    # 随机选择每个象限的标题
    financial_title = random.choice(financial_titles)
    customer_title = random.choice(customer_titles)
    internal_title = random.choice(internal_titles)
    learning_title = random.choice(learning_titles)
    
    # 为各主题选取更具代表性的关键词（如果可能）
    def get_representative_keyword(keywords_list, count=2):
        """从关键词列表中选择更具代表性的关键词"""
        if not keywords_list or len(keywords_list) < count:
            return ["指标"] * count
        return random.sample(keywords_list, count)
    
    # 获取每个主题的代表性关键词
    fin_key1, fin_key2 = get_representative_keyword(financial_keywords)
    cust_key1, cust_key2 = get_representative_keyword(customer_keywords)
    int_key1, int_key2 = get_representative_keyword(internal_keywords)
    learn_key1, learn_key2 = get_representative_keyword(learning_keywords)
    
    # 创建各主题之间更强的逻辑连接
    def create_theme_connection(theme1, key1, theme2, key2):
        """创建两个主题之间的逻辑连接短语"""
        connections = [
            f"通过{theme1}的{key1}提升{theme2}的{key2}",
            f"利用{theme1}的{key1}强化{theme2}的{key2}",
            f"将{theme1}的{key1}与{theme2}的{key2}整合",
            f"从{theme1}的{key1}促进{theme2}的{key2}",
            f"优化{theme1}的{key1}以改善{theme2}的{key2}"
        ]
        return random.choice(connections)
    
    # 重新设计中心文本生成逻辑，确保所有四个主题紧密整合
    # 为中心文本生成多种不同的模板，每种模板都会使用所有四个主题的关键词和名称
    central_text_templates = [
        # 模板1: 循环链式关系
        lambda: f"集成战略: {create_theme_connection(learning_display, learn_key1, internal_display, int_key1)}，"
               f"从而{create_theme_connection(internal_display, int_key2, customer_display, cust_key1)}，"
               f"进而{create_theme_connection(customer_display, cust_key2, financial_display, fin_key1)}，"
               f"最终{create_theme_connection(financial_display, fin_key2, learning_display, learn_key2)}，形成良性循环。",
               
        # 模板2: 基于协同效应
        lambda: f"协同战略: {financial_display}与{customer_display}的融合使{financial_display}的{fin_key1}与{customer_display}的{cust_key1}相互促进；"
               f"同时，{internal_display}与{learning_display}的结合让{internal_display}的{int_key1}和{learning_display}的{learn_key1}相互增强。"
               f"四大领域协同发展，共同提升组织整体绩效。",
               
        # 模板3: 具体行动导向
        lambda: f"行动计划: 首先强化{learning_display}中的{learn_key1}和{learn_key2}；"
               f"其次优化{internal_display}的{int_key1}和{int_key2}流程；"
               f"然后提升{customer_display}的{cust_key1}和{cust_key2}；"
               f"最终改善{financial_display}的{fin_key1}和{fin_key2}，确保可持续发展。",
               
        # 模板4: 创新转型导向
        lambda: f"创新路径: 借助{learning_display}的{learn_key1}带动{internal_display}的{int_key1}创新，"
               f"通过{internal_display}的{int_key2}改革提升{customer_display}的{cust_key1}体验，"
               f"让{customer_display}的{cust_key2}成为{financial_display}的{fin_key1}增长点，"
               f"将{financial_display}的{fin_key2}反哺到{learning_display}的{learn_key2}建设。",
        
        # 模板5: 整合价值链
        lambda: f"价值链整合: 从{learning_display}的{learn_key1}构建核心竞争力，"
               f"将{internal_display}的{int_key1}和{int_key2}作为价值传递的关键环节，"
               f"为{customer_display}提供基于{cust_key1}和{cust_key2}的差异化价值，"
               f"最终通过{financial_display}的{fin_key1}和{fin_key2}实现商业模式的可持续性。",
               
        # 模板6: 战略平衡导向
        lambda: f"战略平衡: 在{financial_display}方面追求{fin_key1}和{fin_key2}的均衡发展，"
               f"在{customer_display}层面兼顾{cust_key1}与{cust_key2}的双重需求，"
               f"在{internal_display}中平衡{int_key1}与{int_key2}的资源配置，"
               f"在{learning_display}领域协调{learn_key1}与{learn_key2}的培养进度，确保整体战略稳健推进。",
               
        # 模板7: 目标导向模式
        lambda: f"目标导向: 我们的终极目标是实现{financial_display}的{fin_key1}最大化，"
               f"这需要通过提供卓越的{customer_display}{cust_key1}来赢得客户，"
               f"而卓越的客户服务依赖于高效的{internal_display}{int_key1}，"
               f"所有这些都建立在持续发展{learning_display}的{learn_key1}基础上。"
               f"四个维度各有侧重，又相互支撑，形成完整目标体系。",
               
        # 模板8: 能力构建模型
        lambda: f"能力构建: {learning_display}是基础层，负责构建{learn_key1}和培养{learn_key2}；"
               f"{internal_display}是支撑层，负责优化{int_key1}和完善{int_key2}；"
               f"{customer_display}是价值层，致力于增强{cust_key1}和改善{cust_key2}；"
               f"{financial_display}是成果层，体现为{fin_key1}提升和{fin_key2}增长。"
               f"四层能力协同构建，推动组织可持续发展。",
               
        # 模板9: 数字化转型框架
        lambda: f"数字化转型: 以数据为核心，通过{learning_display}的{learn_key1}积累知识资产，"
               f"基于{internal_display}的{int_key1}实现流程智能化，"
               f"借助{customer_display}的{cust_key1}创建个性化体验，"
               f"最终形成{financial_display}的{fin_key1}新增长点。"
               f"数据驱动四大板块联动发展，推动组织全面转型。",
               
        # 模板10: 全局优化视角
        lambda: f"全局优化: 我们将{financial_display}、{customer_display}、{internal_display}和{learning_display}视为一个整体系统，"
                f"其中{financial_display}的{fin_key1}是系统输出，"
                f"{customer_display}的{cust_key1}是价值传递，"
                f"{internal_display}的{int_key1}是内部运作，"
                f"{learning_display}的{learn_key1}是基础投入。"
                f"通过系统思维优化各环节，实现组织整体效益最大化。",
                
        # 模板11: 敏捷适应模式
        lambda: f"敏捷适应: {learning_display}通过{learn_key1}和{learn_key2}培养适应能力，"
                f"使{internal_display}能够快速调整{int_key1}和{int_key2}流程，"
                f"从而使{customer_display}体验到灵活的{cust_key1}服务，"
                f"最终实现{financial_display}的{fin_key1}和{fin_key2}韧性增长。"
                f"四个维度形成敏捷响应机制，增强组织应对变化的能力。",
                
        # 模板12: 生态系统视角
        lambda: f"生态系统: {financial_display}、{customer_display}、{internal_display}和{learning_display}构成完整生态，"
                f"其中{learning_display}的{learn_key1}是生态创新源，"
                f"{internal_display}的{int_key1}是资源转化器，"
                f"{customer_display}的{cust_key1}是价值接收端，"
                f"{financial_display}的{fin_key1}是可持续指标。"
                f"生态各要素互相滋养，形成自我优化循环。",
                
        # 模板13: 持续改进框架
        lambda: f"持续改进: 我们基于{learning_display}的{learn_key1}持续获取新知识，"
                f"不断优化{internal_display}中的{int_key1}和{int_key2}环节，"
                f"逐步提升{customer_display}的{cust_key1}和{cust_key2}满意度，"
                f"稳步增强{financial_display}的{fin_key1}和{fin_key2}水平。"
                f"通过PDCA循环推动四大领域协同进步。",
                
        # 模板14: 矩阵协同结构
        lambda: f"矩阵协同: {learning_display}与{internal_display}在横向形成能力矩阵，"
                f"通过{learn_key1}增强{int_key1}，通过{learn_key2}完善{int_key2}；"
                f"{customer_display}与{financial_display}在纵向构成成果矩阵，"
                f"以{cust_key1}促进{fin_key1}，以{cust_key2}带动{fin_key2}。"
                f"横纵协同，实现整体效能最优化。",
                
        # 模板15: 战略地图视角
        lambda: f"战略地图: 从底层的{learning_display}构建{learn_key1}和{learn_key2}基础，"
                f"向上支撑{internal_display}的{int_key1}和{int_key2}流程优化，"
                f"进而提升{customer_display}的{cust_key1}和{cust_key2}价值主张，"
                f"最终实现{financial_display}的{fin_key1}增长和{fin_key2}提升。"
                f"形成清晰的因果链路，指导战略落地。",
                
        # 模板16: 价值网络模型
        lambda: f"价值网络: {financial_display}、{customer_display}、{internal_display}和{learning_display}形成价值网络，"
                f"其中{learning_display}的{learn_key1}与{internal_display}的{int_key1}形成能力联结，"
                f"{internal_display}的{int_key2}与{customer_display}的{cust_key1}构成服务纽带，"
                f"{customer_display}的{cust_key2}与{financial_display}的{fin_key1}建立价值连接。"
                f"网络各节点协同运作，创造整体价值溢出。",
                
        # 模板17: 平台思维模式
        lambda: f"平台战略: 以{learning_display}的{learn_key1}和{internal_display}的{int_key1}构建核心平台能力，"
                f"通过平台连接{customer_display}的{cust_key1}需求与{financial_display}的{fin_key1}供给，"
                f"实现{customer_display}的{cust_key2}体验与{internal_display}的{int_key2}效率双提升，"
                f"促进{financial_display}的{fin_key2}与{learning_display}的{learn_key2}共同增长。"
                f"平台效应带动四维协同发展。",
                
        # 模板18: 资源优化配置
        lambda: f"资源优化: 将有限资源优先投入{learning_display}的{learn_key1}建设，"
                f"合理分配到{internal_display}的{int_key1}和{int_key2}改进，"
                f"科学配置于{customer_display}的{cust_key1}和{cust_key2}提升，"
                f"策略性投资于{financial_display}的{fin_key1}和{fin_key2}增长。"
                f"通过资源最优化配置，实现四大领域均衡发展。",
                
        # 模板19: 风险管理框架
        lambda: f"风险管控: 通过加强{learning_display}的{learn_key1}降低能力风险，"
                f"优化{internal_display}的{int_key1}减少运营风险，"
                f"提升{customer_display}的{cust_key1}控制市场风险，"
                f"改善{financial_display}的{fin_key1}管理财务风险。"
                f"四个维度全面防控风险，保障组织稳健运行。",
                
        # 模板20: 创新扩散机制
        lambda: f"创新扩散: 从{learning_display}的{learn_key1}激发创新源泉，"
                f"通过{internal_display}的{int_key1}和{int_key2}进行创新孵化，"
                f"由{customer_display}的{cust_key1}和{cust_key2}验证创新价值，"
                f"最终形成{financial_display}的{fin_key1}和{fin_key2}创新回报。"
                f"构建完整创新扩散链条，持续释放创新动能。",
                
        # 模板21: 质量管理体系
        lambda: f"质量驱动: 从{learning_display}的{learn_key1}建立质量意识和能力，"
                f"在{internal_display}中实施{int_key1}和{int_key2}的质量控制，"
                f"通过{customer_display}的{cust_key1}和{cust_key2}获取质量反馈，"
                f"最终体现为{financial_display}的{fin_key1}质量效益。"
                f"全流程质量管控，提升组织整体表现。",
                
        # 模板22: 知识管理框架
        lambda: f"知识管理: 以{learning_display}为核心构建{learn_key1}知识库，"
                f"通过{internal_display}的{int_key1}和{int_key2}实现知识应用，"
                f"从{customer_display}的{cust_key1}和{cust_key2}中获取市场知识，"
                f"形成{financial_display}的{fin_key1}和{fin_key2}知识资产。"
                f"知识在四个维度间流动创造价值。",
                
        # 模板23: 人本领导模式
        lambda: f"人本领导: 通过{learning_display}培养以{learn_key1}为核心的人才，"
                f"在{internal_display}中建立基于{int_key1}的赋能机制，"
                f"引导团队关注{customer_display}的{cust_key1}需求，"
                f"共同实现{financial_display}的{fin_key1}目标。"
                f"以人为本，激发组织活力与创造力。",
                
        # 模板24: 供应链整合视角
        lambda: f"供应链整合: 以{learning_display}的{learn_key1}提升供应链智能，"
                f"通过{internal_display}的{int_key1}和{int_key2}优化供应链流程，"
                f"满足{customer_display}的{cust_key1}和{cust_key2}交付需求，"
                f"提高{financial_display}的{fin_key1}和{fin_key2}供应链效益。"
                f"端到端供应链协同，创造综合竞争优势。",
                
        # 模板25: 场景化战略模型
        lambda: f"场景战略: 基于{learning_display}的{learn_key1}洞察用户场景，"
                f"优化{internal_display}的{int_key1}以支持场景解决方案，"
                f"围绕{customer_display}的{cust_key1}打造极致场景体验，"
                f"实现{financial_display}的{fin_key1}场景商业价值。"
                f"以场景为中心，重构业务增长模式。",
                
        # 模板26: 系统动力学视角
        lambda: f"系统动力: {learning_display}、{internal_display}、{customer_display}和{financial_display}形成动态系统，"
                f"其中{learn_key1}和{int_key1}是系统的驱动因子，"
                f"{cust_key1}和{fin_key1}是系统的状态变量，"
                f"{learn_key2}、{int_key2}、{cust_key2}和{fin_key2}构成反馈环路。"
                f"理解系统结构和行为，把握变革时机和杠杆点。",
                
        # 模板27: 共创价值框架
        lambda: f"共创价值: 汇聚{learning_display}的{learn_key1}和{learn_key2}智慧，"
                f"协同{internal_display}的{int_key1}和{int_key2}资源，"
                f"与{customer_display}共同定义{cust_key1}和{cust_key2}价值，"
                f"创造{financial_display}的{fin_key1}和{fin_key2}共享成果。"
                f"多方参与价值共创，实现合作共赢。",
                
        # 模板28: 精益管理思维
        lambda: f"精益管理: 应用{learning_display}的{learn_key1}持续识别浪费，"
                f"在{internal_display}中精简{int_key1}和{int_key2}流程，"
                f"聚焦{customer_display}真正重视的{cust_key1}和{cust_key2}，"
                f"提升{financial_display}的{fin_key1}和{fin_key2}投入产出比。"
                f"精益思想贯穿四大领域，持续优化价值流。",
                
        # 模板29: 全球化战略框架
        lambda: f"全球化战略: 加强{learning_display}的{learn_key1}跨文化能力，"
                f"优化{internal_display}的{int_key1}全球协同体系，"
                f"满足{customer_display}的{cust_key1}区域差异化需求，"
                f"实现{financial_display}的{fin_key1}全球化增长。"
                f"本地智慧与全球整合，打造国际竞争新优势。",
                
        # 模板30: 可持续发展模式
        lambda: f"可持续发展: 通过{learning_display}的{learn_key1}构建绿色能力，"
                f"在{internal_display}中推行{int_key1}和{int_key2}低碳转型，"
                f"满足{customer_display}对{cust_key1}和{cust_key2}的环保期待，"
                f"平衡{financial_display}的{fin_key1}与环境社会责任。"
                f"经济、社会、环境三重底线，引领可持续未来。"
    ]
    
    # 随机选择一个模板
    central_text = random.choice(central_text_templates)()
    
    # 生成区块表头 (每个区块使用4个相关的关键词作为表头)
    def get_header_keywords(keywords_list, style=None):
        """生成表头关键词，可以使用不同风格
        
        参数:
        - keywords_list: 可用关键词列表
        - style: 表头生成风格 (None为默认，'goal'为目标导向，'balanced'为平衡视角)
        
        返回:
        - 4个表头关键词列表
        """
        # 确保有足够的关键词
        if len(keywords_list) < 4:
            return keywords_list + ["指标"] * (4 - len(keywords_list))
            
        # 根据风格生成不同的表头
        if style == 'goal':
            # 目标导向: 目标-指标-目标值-行动
            if len(keywords_list) >= 4:
                selected = random.sample(keywords_list, 4)
                return [f"{selected[0]}目标", f"{selected[1]}指标", f"{selected[2]}目标值", f"{selected[3]}行动"]
        elif style == 'balanced':
            # 平衡视角: 提取不同词性或含义的关键词
            # 这里简化处理，仅随机选择关键词
            return random.sample(keywords_list, 4)
        
        # 默认随机选择
        return random.sample(keywords_list, 4)
    
    # 随机选择不同的表头生成风格
    header_styles = ['default', 'goal', 'balanced']
    financial_style = random.choice(header_styles)
    customer_style = random.choice(header_styles)
    internal_style = random.choice(header_styles)
    learning_style = random.choice(header_styles)
    
    # 生成引导性问题 - 使用新的随机问题生成函数
    financial_question = generate_random_question(financial_display, "financial")
    customer_question = generate_random_question(customer_display, "customer") 
    internal_question = generate_random_question(internal_display, "internal")
    learning_question = generate_random_question(learning_display, "learning")
    
    # 整合所有生成的内容
    bsc_content = {
        "central_text": central_text,
        "financial": {
            "theme": financial_theme,
            "display_name": financial_display,
            "title": financial_title,
            "headers": get_header_keywords(financial_keywords, financial_style),
            "question": financial_question
        },
        "customer": {
            "theme": customer_theme,
            "display_name": customer_display,
            "title": customer_title,
            "headers": get_header_keywords(customer_keywords, customer_style),
            "question": customer_question
        },
        "internal": {
            "theme": internal_theme,
            "display_name": internal_display,
            "title": internal_title,
            "headers": get_header_keywords(internal_keywords, internal_style),
            "question": internal_question
        },
        "learning": {
            "theme": learning_theme,
            "display_name": learning_display,
            "title": learning_title,
            "headers": get_header_keywords(learning_keywords, learning_style),
            "question": learning_question
        }
    }
    
    return bsc_content

def generate_blocked_table(
    central_text: str,
    financial_headers: list[str],
    financial_question: str,
    customer_headers: list[str],
    customer_question: str,
    internal_headers: list[str],
    internal_question: str,
    learning_headers: list[str],
    learning_question: str,
    output_filename: str,
    image_width: int = 1500,
    financial_title: str = "财务",
    customer_title: str = "客户体验",
    internal_title: str = "内部运营",
    learning_title: str = "学习成长"
) -> bool:
    """生成平衡计分卡(BSC)风格的分块表图片"""
    
    if not os.path.exists(OUTPUT_FOLDER_BLOCKED):
        os.makedirs(OUTPUT_FOLDER_BLOCKED)
        print(f"创建目录: {OUTPUT_FOLDER_BLOCKED}")

    # 创建透视表HTML的辅助函数
    def create_perspective_table_html(headers: list[str]) -> str:
        """创建每个区块内部的表格HTML"""
        if len(headers) != 4:
            # 如果提供的表头数量不正确，使用默认值
            headers = ["Header 1", "Header 2", "Header 3", "Header 4"]
        
        # 创建表格HTML结构
        table_html = "<table class='perspective-table'><thead><tr>"
        
        # 添加表头
        for header in headers:
            table_html += f"<th>{header}</th>"
        table_html += "</tr></thead><tbody><tr>"
        
        # 添加一行空单元格，作为示例数据行
        for _ in range(4): 
            table_html += "<td>&nbsp;</td>" # 使用&nbsp;保持单元格高度
        table_html += "</tr></tbody></table>"
        
        return table_html

    # 准备HTML内容
    # 中心文本处理：将关键词替换为高亮样式
    highlight_keywords = ["学习成长", "内部运作", "客户满意", "财务收益"]
    processed_central_text = central_text
    
    # 遍历关键词列表，将每个关键词替换为带高亮样式的HTML
    for keyword in highlight_keywords:
        if keyword in central_text: # 确保关键词在文本中存在再替换
            processed_central_text = processed_central_text.replace(
                keyword, 
                f"<span class='highlight'>{keyword}</span>"
            )

    html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>分块表</title>
    <style>
        body {{ font-family: 'SimHei', 'Microsoft YaHei', Arial, sans-serif; margin: 0; padding: 0; background-color: #ffffff; display: flex; justify-content: center; align-items: center; min-height: 100vh; }}
        
        /* 所有块的容器 - 相对定位作为绝对定位子元素的参考 */
        .bsc-container {{ 
            position: relative;
            width: {image_width}px; /* 容器总宽度，通过参数控制 */
            height: 900px; /* 容器总高度，可根据需要调整 */
            margin: 20px;
            background-color: transparent;
        }}
        
        /* 所有区块的通用样式 */
        .bsc-block {{ 
            position: absolute; /* 所有区块都使用绝对定位以便精确放置 */
            border: 1px solid #aaaaaa; /* 区块边框样式 */
            background-color: #f5f5f5; /* 区块背景色 */
            color: #000000;
            display: flex;
            flex-direction: column;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12); /* 轻微阴影效果 */
        }}
        
        /* 上块 - 财务区块定位 */
        .bsc-financial {{
            top: 8%; /* 统一向下移动8% */
            left: 30%; /* 水平居中(相对于容器) */
            width: 40%; /* 区块宽度，可调整 */
            height: auto; /* 高度自适应内容 */
        }}

        /* 左块 - 客户区块定位 */
        .bsc-customer {{
            top: 33%; /* 统一向下移动8%，从25%调整到33% */
            left: 0; /* 左对齐容器 */
            width: 22%; /* 左侧区块宽度 */
            height: auto;
        }}

        /* 右块 - 内部运营区块定位 */
        .bsc-internal {{
            top: 33%; /* 统一向下移动8%，从25%调整到33% */
            right: 0; /* 右对齐容器 */
            width: 22%; /* 右侧区块宽度 */
            height: auto;
        }}

        /* 下块 - 学习成长区块定位 */
        .bsc-learning {{
            bottom: 27%; /* 统一向下移动8%，从35%调整到27% */
            left: 30%; /* 水平居中(与上块对齐) */
            width: 40%; /* 区块宽度，与上块一致 */
            height: auto;
        }}

        /* 中央区块 - 企业愿景与战略 */
        .bsc-center {{
            position: absolute;
            top: 33%; /* 统一向下移动8%，从25%调整到33% */
            left: 29%; /* 左侧起始位置 */
            width: 42%; /* 中心区块宽度，可调整 */
            height: auto;
            min-height: 120px; /* 最小高度确保内容显示 */
            padding: 15px 20px; /* 内边距 */
            text-align: justify;
            line-height: 1.6;
            font-size: 0.95em;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #ffeeee; /* 浅红色背景，突出显示 */
            color: #000000;
            z-index: 5; /* 确保在箭头之上显示 */
            box-sizing: border-box;
            border: none;
        }}
        
        /* 区块标题样式 */
        .bsc-title {{ 
            background-color: #e6e6e6; /* 标题背景色 */
            color: #000000;
            padding: 10px 15px; /* 标题内边距 */
            text-align: center;
            font-weight: bold;
            font-size: 1.0em;
            border-bottom: 1px solid #cccccc; /* 标题下边框，与内容分隔 */
        }}
        
        /* 区块内表格样式 */
        .perspective-table {{ 
            width: 100%; /* 表格宽度充满区块 */
            border-collapse: collapse; /* 边框合并模式 */
            table-layout: fixed; /* 固定表格布局，提高渲染性能 */
            margin: 0; 
        }}
        
        /* 表格单元格通用样式 */
        .perspective-table th, .perspective-table td {{ 
            border: 1px solid #cccccc; /* 单元格边框 */
            text-align: center;
            padding: 7px; /* 单元格内边距 */
            font-size: 0.9em;
            word-wrap: break-word; /* 长文本自动换行 */
            color: #000000; 
        }}
        
        /* 表头样式 */
        .perspective-table th {{ 
            background-color: #f0f0f0; /* 表头背景色 */
            font-weight: normal; /* 表头字体粗细 */
        }}
        
        /* 数据单元格样式 */
        .perspective-table td {{ 
            background-color: #ffffff; /* 数据单元格背景色 */
        }}

        /* 引导性问题样式 */
        .bsc-question {{ 
            background-color: #e6e6e6; /* 问题背景色 */
            color: #000000;
            padding: 8px 15px; /* 内边距 */
            text-align: center;
            font-size: 0.85em; /* 问题字体大小 */
            border-bottom: 1px solid #cccccc; /* 下边框，与表格分隔 */
        }}
        
        /* 高亮关键词样式 */
        .highlight {{ 
            color: #d43f3a; /* 高亮文本颜色(红色) */
            font-weight: bold; /* 高亮文本加粗 */
        }} 

        /* SVG箭头容器样式 */
        .bsc-arrows {{ 
            position: absolute; 
            top: 0; 
            left: 0; 
            width: 100%; 
            height: 100%; 
            pointer-events: none; /* 防止箭头捕获鼠标事件 */
            z-index: 1; /* 箭头层级，保证在区块下方 */
        }}
        
        /* 箭头线条样式 */
        .bsc-arrows line, .bsc-arrows path {{
            stroke: #333333; /* 默认箭头颜色(灰色) */
            stroke-width: 2.5; /* 默认线条粗细 */
        }}
        
        /* 箭头头部样式 */
        .bsc-arrows polygon {{
            fill: #333333; /* 箭头头部填充颜色，与线条颜色一致 */
        }}
    </style>
</head>
<body>
    <div class="bsc-container" id="capture">
        <!-- Financial block (top) -->
        <div class="bsc-block bsc-financial">
            <div class="bsc-title">{financial_title}</div>
            <div class="bsc-question">{financial_question}</div>
            {create_perspective_table_html(financial_headers)}
        </div>

        <!-- Customer block (left) -->
        <div class="bsc-block bsc-customer">
            <div class="bsc-title">{customer_title}</div>
            <div class="bsc-question">{customer_question}</div>
            {create_perspective_table_html(customer_headers)}
        </div>

        <!-- Internal block (right) -->
        <div class="bsc-block bsc-internal">
            <div class="bsc-title">{internal_title}</div>
            <div class="bsc-question">{internal_question}</div>
            {create_perspective_table_html(internal_headers)}
        </div>

        <!-- Learning block (bottom) -->
        <div class="bsc-block bsc-learning">
            <div class="bsc-title">{learning_title}</div>
            <div class="bsc-question">{learning_question}</div>
            {create_perspective_table_html(learning_headers)}
        </div>
        
        <!-- Center text block - moved after the other blocks so it appears on top -->
        <div class="bsc-center">
            <div class="bsc-center-content">{processed_central_text}</div>
        </div>
        
        <!-- 箭头SVG - 定义区块间的因果关系连接 -->
        <svg class="bsc-arrows">
            <defs>
                <!-- 箭头标记定义 - 右侧箭头 -->
                <marker id="arrowhead-right" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto" markerUnits="strokeWidth">
                    <polygon points="0 0, 10 3.5, 0 7" fill="#333333" />
                </marker>
                <!-- 箭头标记定义 - 左侧箭头 -->
                <marker id="arrowhead-left" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto" markerUnits="strokeWidth">
                    <polygon points="10 0, 0 3.5, 10 7" fill="#333333" />
                </marker>
            </defs>
            
            <!-- 客户到财务的双向箭头（对角线连接）-->
            <line x1="15%" y1="33%" x2="30%" y2="22%" stroke="#333333" stroke-width="2.5"
                marker-start="url(#arrowhead-left)" marker-end="url(#arrowhead-right)" />

            <!-- 内部运营到财务的双向箭头（对角线连接）-->
            <line x1="85%" y1="33%" x2="70%" y2="22%" stroke="#333333" stroke-width="2.5"
                marker-start="url(#arrowhead-left)" marker-end="url(#arrowhead-right)" />

            <!-- 财务到中心的垂直连接箭头（上到中 - 橙色高亮）-->
            <line x1="50%" y1="23%" x2="50%" y2="33%" stroke="#FF6600" stroke-width="3.5"
                marker-start="url(#arrowhead-left)" marker-end="url(#arrowhead-right)" />

            <!-- 客户到中心的双向箭头（左到中 - 橙色高亮）-->
            <line x1="22%" y1="40%" x2="29%" y2="40%" stroke="#FF6600" stroke-width="3.5"
                marker-start="url(#arrowhead-left)" marker-end="url(#arrowhead-right)" />

            <!-- 内部运营到中心的双向箭头（右到中 - 橙色高亮）-->
            <line x1="78%" y1="40%" x2="71%" y2="40%" stroke="#FF6600" stroke-width="3.5"
                marker-start="url(#arrowhead-left)" marker-end="url(#arrowhead-right)" />

            <!-- 中心到学习成长的双向箭头（中到下 - 橙色高亮）-->
            <line x1="50%" y1="46%" x2="50%" y2="58%" stroke="#FF6600" stroke-width="3.5"
                marker-start="url(#arrowhead-left)" marker-end="url(#arrowhead-right)" />

            <!-- 客户到学习成长的双向箭头（左下对角线 - 橙色高亮）-->
            <line x1="18%" y1="49%" x2="38%" y2="58%" stroke="#FF6600" stroke-width="3.5"
                marker-start="url(#arrowhead-left)" marker-end="url(#arrowhead-right)" />

            <!-- 内部运营到学习成长的双向箭头（右下对角线 - 橙色高亮）-->
            <line x1="82%" y1="49%" x2="62%" y2="58%" stroke="#FF6600" stroke-width="3.5"
                marker-start="url(#arrowhead-left)" marker-end="url(#arrowhead-right)" />
        </svg>
    </div>
</body>
</html>
"""

    # 生成图片
    output_path = os.path.join(OUTPUT_FOLDER_BLOCKED, output_filename)
    try:
        # 使用imgkit将HTML字符串转换为JPG图片
        options = {'width': image_width + 40, 'encoding': "UTF-8", 'quiet': ''}
        if config:
            imgkit.from_string(html_content, output_path, options=options, config=config)
        else:
            imgkit.from_string(html_content, output_path, options=options)
        print(f"分块表已保存到: {output_path}")
        return True
    except Exception as e:
        # 错误处理和日志记录
        print(f"生成分块表图片时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def worker_task_bsc(task_id: int, start_index: int, count: int) -> Dict:
    """工作进程任务 - 生成一组分块表图片"""
    worker_start_time = time.time()
    results = {
        "task_id": task_id,
        "success_count": 0,
        "error_count": 0,
        "start_index": start_index
    }
    
    # 当前工作进程已使用的主题组合，用于本地跟踪，确保单个工作进程内部不重复
    local_used_combinations = set()
    
    try:
        for i in range(count):
            table_index = start_index + i
            
            # 为每个表格随机选择4个主题，并确保本工作进程内部不重复
            max_attempts = 5
            for attempt in range(max_attempts):
                themes = select_random_themes(4, avoid_recent=True)
                if not themes or len(themes) < 4:
                    print(f"工作进程 {task_id}: 无法选择足够的主题，跳过表格 {table_index}")
                    results["error_count"] += 1
                    break
                
                # 本地检查组合是否已用过
                combo_key = tuple(sorted(themes))
                if combo_key not in local_used_combinations:
                    local_used_combinations.add(combo_key)
                    break
                
                # 最后一次尝试，仍然没有找到新组合，使用重复的组合
                if attempt == max_attempts - 1:
                    print(f"工作进程 {task_id}: 尝试 {max_attempts} 次后仍未找到未使用的主题组合，使用重复组合")
            
            if not themes or len(themes) < 4:
                continue
            
            # 根据主题生成BSC内容
            bsc_content = generate_bsc_content(themes)
            
            # 使用简单的序号命名：分块表_1, 分块表_2, ...
            file_name = f"分块表_{table_index}.jpg"
            
            # 生成分块表，使用自定义标题
            success = generate_blocked_table(
                central_text=bsc_content["central_text"],
                financial_headers=bsc_content["financial"]["headers"],
                financial_question=bsc_content["financial"]["question"],
                customer_headers=bsc_content["customer"]["headers"],
                customer_question=bsc_content["customer"]["question"],
                internal_headers=bsc_content["internal"]["headers"],
                internal_question=bsc_content["internal"]["question"],
                learning_headers=bsc_content["learning"]["headers"],
                learning_question=bsc_content["learning"]["question"],
                financial_title=bsc_content["financial"]["title"],
                customer_title=bsc_content["customer"]["title"],
                internal_title=bsc_content["internal"]["title"],
                learning_title=bsc_content["learning"]["title"],
                output_filename=file_name,
                image_width=1800  # 调整到更宽的尺寸以显示更多内容
            )
            
            if success:
                results["success_count"] += 1
            else:
                results["error_count"] += 1
            
            if results["success_count"] % 5 == 0:
                print(f"工作进程 {task_id} 进度: {results['success_count']}/{count} 已生成")
            
    except Exception as e:
        print(f"工作进程 {task_id} 严重失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    worker_elapsed = time.time() - worker_start_time
    print(f"工作进程 {task_id} 完成: 成功 {results['success_count']}, 失败 {results['error_count']}, 耗时 {worker_elapsed:.2f}秒")
    return results

def generate_bsc_tables_multiprocess(desired_table_count: int, process_count: Optional[int] = None) -> int:
    """使用多进程并行生成指定数量的分块表格图片到 OUTPUT_FOLDER_BLOCKED."""
    
    if process_count is None:
        process_count = max(1, mp.cpu_count() - 1)  # 默认使用CPU核心数-1的进程数
    
    print(f"开始多进程生成 {desired_table_count} 张分块表到 '{OUTPUT_FOLDER_BLOCKED}'...")
    start_time = time.time()

    if not os.path.exists(OUTPUT_FOLDER_BLOCKED):
        os.makedirs(OUTPUT_FOLDER_BLOCKED)
        print(f"创建目录: {OUTPUT_FOLDER_BLOCKED}")

    # 计算每个进程需要生成的表格数量
    tables_per_process = desired_table_count // process_count
    remainder = desired_table_count % process_count
    
    # 创建进程池和任务
    pool = mp.Pool(processes=process_count)
    print(f"创建了 {process_count} 个工作进程")
    
    tasks = []
    current_start_index = 1
    
    for i in range(process_count):
        # 为最后一个进程分配余数
        process_table_count = tables_per_process + (1 if i < remainder else 0)
        if process_table_count > 0:
            task = pool.apply_async(worker_task_bsc, (i + 1, current_start_index, process_table_count))
            tasks.append(task)
            current_start_index += process_table_count

    # 收集结果
    total_success_generated = 0
    total_errors_in_workers = 0

    print("所有进程已启动，正在生成分块表...")
    for task in tasks:
        try:
            result = task.get()  # 等待任务完成
            if result:
                total_success_generated += result.get("success_count", 0)
                total_errors_in_workers += result.get("error_count", 0)
            else:
                print(f"任务 {i+1} 返回了空结果")
        except Exception as e:
            print(f"获取任务结果时出错: {e}")
            import traceback
            traceback.print_exc()

    pool.close()
    pool.join()

    # 最终汇总
    total_elapsed_seconds = time.time() - start_time
    hours, remainder = divmod(total_elapsed_seconds, 3600)
    minutes, seconds = divmod(remainder, 60)

    print("=" * 60)
    print(f"分块表多进程生成完成!")
    print(f"目标生成数量: {desired_table_count}")
    print(f"实际成功生成: {total_success_generated} 张分块表")
    print(f"生成过程中出现错误: {total_errors_in_workers} 次")
    print(f"总耗时: {int(hours)}小时 {int(minutes)}分钟 {seconds:.2f}秒")
    if total_success_generated > 0:
        print(f"平均每张成功表格耗时: {total_elapsed_seconds / total_success_generated:.4f} 秒")
    print("=" * 60)

    return total_success_generated

def clean_old_files():
    """清理旧的复杂命名文件，保留简单命名的文件"""
    if not os.path.exists(OUTPUT_FOLDER_BLOCKED):
        return

    print("清理旧的复杂命名文件...")
    cleaned_count = 0

    for filename in os.listdir(OUTPUT_FOLDER_BLOCKED):
        if filename.endswith('.jpg'):
            # 保留简单命名格式：分块表_数字.jpg 和测试文件
            if (filename.startswith('分块表_') and
                not ('_' in filename[4:-4] and len(filename[4:-4].split('_')) > 1) and
                filename[4:-4].isdigit()) or filename.startswith('测试_'):
                continue  # 保留这些文件
            else:
                # 删除复杂命名的文件
                file_path = os.path.join(OUTPUT_FOLDER_BLOCKED, filename)
                try:
                    os.remove(file_path)
                    cleaned_count += 1
                    print(f"删除旧文件: {filename}")
                except Exception as e:
                    print(f"删除文件失败 {filename}: {e}")

    print(f"清理完成，删除了 {cleaned_count} 个旧文件")

def test_single_bsc_table():
    """生成单个BSC表格用于测试统一位置调整效果"""
    print("生成测试BSC表格...")

    # 选择4个主题
    themes = select_random_themes(4, avoid_recent=False)
    if not themes or len(themes) < 4:
        print("错误: 无法选择足够的主题")
        return False

    print(f"使用主题: {themes}")

    # 生成BSC内容
    bsc_content = generate_bsc_content(themes)

    # 生成测试文件
    test_filename = "测试_BSC表格_简单命名.jpg"

    success = generate_blocked_table(
        central_text=bsc_content["central_text"],
        financial_headers=bsc_content["financial"]["headers"],
        financial_question=bsc_content["financial"]["question"],
        customer_headers=bsc_content["customer"]["headers"],
        customer_question=bsc_content["customer"]["question"],
        internal_headers=bsc_content["internal"]["headers"],
        internal_question=bsc_content["internal"]["question"],
        learning_headers=bsc_content["learning"]["headers"],
        learning_question=bsc_content["learning"]["question"],
        financial_title=bsc_content["financial"]["title"],
        customer_title=bsc_content["customer"]["title"],
        internal_title=bsc_content["internal"]["title"],
        learning_title=bsc_content["learning"]["title"],
        output_filename=test_filename,
        image_width=1800
    )

    if success:
        print(f"测试BSC表格已生成: {os.path.join(OUTPUT_FOLDER_BLOCKED, test_filename)}")
        return True
    else:
        print("测试BSC表格生成失败")
        return False

if __name__ == "__main__":
    # 首先清理旧的复杂命名文件
    print("=== 清理旧文件 ===")
    clean_old_files()

    print("\n" + "="*50 + "\n")

    # 生成测试表格验证简单命名格式
    print("=== 测试简单命名格式 ===")
    test_single_bsc_table()

    print("\n" + "="*50 + "\n")

    # 然后生成批量表格
    desired_bsc_table_count = 1100  # 生成10张表格验证新命名格式
    process_count = max(1, mp.cpu_count() - 1)

    print(f"使用 {process_count} 个进程生成 {desired_bsc_table_count} 张分块表...")
    print("文件命名格式: 分块表_1.jpg, 分块表_2.jpg, ...")
    generate_bsc_tables_multiprocess(desired_bsc_table_count, process_count)