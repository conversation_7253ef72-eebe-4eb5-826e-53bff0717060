#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
卡线表生成器 (Card-Line Table Generator)
用于生成带有对角线分隔的卡线表格式图像

特点：
- 表头单元格包含对角线分隔行列属性
- 完整的网格线结构
- 支持大规模批量生成（30,000张图像）
- 内容唯一性保证（重复率<0.01%）
- 中文本地化数据生成
"""

import os
import random
import time
import gc
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import matplotlib
from datetime import datetime
import numpy as np
from faker import Faker

# 导入现有的数据生成组件
from json_table_generator import (
    TableSchema, EnhancedDataGenerators, ColumnDefinition
)

# ====================== 配置常量 ======================

# 输出配置
OUTPUT_FOLDER = "表格"
OUTPUT_CARD_LINE_FOLDER = os.path.join(OUTPUT_FOLDER, "卡线表")

# 表格样式配置
TITLE_FONTSIZE = 14
CELL_FONTSIZE = 10
HEADER_FONTSIZE = 9
ROW_HEIGHT = 0.6
DPI = 300
BATCH_SIZE = 100  # 批处理大小，优化内存使用

# 中文字体配置
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

# 表格尺寸配置
MIN_ROWS = 3
MAX_ROWS = 8
MIN_COLS = 2  # 卡线表至少需要2列才有意义
MAX_COLS = 6

# ====================== 卡线表生成器类 ======================

class CardLineTableGenerator:
    """卡线表生成器，专门用于生成带对角线的表格"""

    def __init__(self, output_folder=OUTPUT_CARD_LINE_FOLDER):
        """初始化生成器"""
        self.output_folder = output_folder
        self.data_generator = EnhancedDataGenerators()
        self.row_height = ROW_HEIGHT
        self.dpi = DPI
        self.title_fontsize = TITLE_FONTSIZE
        self.cell_fontsize = CELL_FONTSIZE
        self.header_fontsize = HEADER_FONTSIZE

        # 确保输出文件夹存在
        if not os.path.exists(self.output_folder):
            os.makedirs(self.output_folder)

    def generate_card_line_data(self, num_rows, num_cols):
        """生成卡线表数据结构 - 新逻辑：直接选择最佳列确保语义匹配"""
        # 确保卡线表至少有2列
        min_cols = max(2, num_cols)

        # 使用现有的主题配置生成数据
        schema = TableSchema()

        # 生成基础数据
        df = self.generate_table_data(schema, num_rows)

        # 选择合适的列，新逻辑：直接选择最佳第一列
        available_cols = list(df.columns)

        # 移除序号列，我们要用有意义的分类列
        if "序号" in available_cols:
            available_cols.remove("序号")

        # 确保有足够的列数
        if len(available_cols) >= min_cols:
            # 使用新的列选择逻辑
            selected_cols = self.select_meaningful_columns(schema, available_cols, min_cols)
            df = df[selected_cols]
        else:
            # 如果可用列不够，使用所有可用列，但至少保证2列
            if len(available_cols) >= 2:
                df = df[available_cols[:min_cols]]
            else:
                # 如果连2列都没有，重新生成数据
                print(f"Warning: 可用列数不足({len(available_cols)})，重新生成数据...")
                df = self.generate_table_data(schema, num_rows)
                available_cols = [col for col in df.columns if col != "序号"]
                if len(available_cols) >= 2:
                    selected_cols = self.select_meaningful_columns(schema, available_cols, min_cols)
                    df = df[selected_cols]

        # 最终验证：确保至少有2列
        if len(df.columns) < 2:
            print(f"Error: 生成的表格只有{len(df.columns)}列，卡线表至少需要2列")
            # 强制添加一个默认列
            if len(df.columns) == 1:
                df['备注'] = ['项目' + str(i+1) for i in range(len(df))]

        return df, schema

    def select_meaningful_columns(self, schema, available_cols, num_cols, target_row_label=None):
        """选择有意义的列，新逻辑：直接选择最佳第一列，确保语义匹配"""
        column_defs = schema.get_column_definitions()
        col_def_map = {col_def.name: col_def for col_def in column_defs}

        # 新逻辑：直接选择最佳的第一列，确保语义匹配

        # 第一步：按优先级分类所有可用列
        name_cols = []          # 姓名/名称类列（最高优先级）
        identifier_cols = []    # 标识类列（高优先级）
        text_cols = []          # 其他文本列（中优先级）
        other_cols = []         # 其他列（低优先级）

        # 通用的名称/姓名关键词
        name_keywords = ["姓名", "名称", "用户名", "客户名", "患者姓名", "学生姓名", "员工姓名", "公司名称", "产品名称", "设备名称", "项目名称"]

        # 标识类关键词
        identifier_keywords = ["类型", "分类", "状态", "等级", "级别", "品牌", "部门", "职位", "科室"]

        for col in available_cols:
            # 跳过数字序号列
            if self._is_numeric_sequence_column(col):
                continue

            # 检查是否是名称类列
            if any(keyword in col for keyword in name_keywords):
                name_cols.append(col)
            # 检查是否是标识类列
            elif any(keyword in col for keyword in identifier_keywords):
                identifier_cols.append(col)
            # 检查是否是文本列
            elif col in col_def_map:
                col_def = col_def_map[col]
                if col_def.data_type in ["categorical", "categorical_with_pattern", "faker_name", "faker_company"] or col_def.data_category == "text":
                    text_cols.append(col)
                else:
                    other_cols.append(col)
            else:
                # 默认归类为其他列
                other_cols.append(col)

        # 第二步：选择最佳的第一列
        selected_cols = []

        if name_cols:
            # 优先选择名称类列
            selected_cols.append(name_cols[0])
            print(f"✅ 选择名称类列作为第一列: '{name_cols[0]}'")
        elif identifier_cols:
            # 其次选择标识类列
            selected_cols.append(identifier_cols[0])
            print(f"✅ 选择标识类列作为第一列: '{identifier_cols[0]}'")
        elif text_cols:
            # 再次选择文本列
            selected_cols.append(text_cols[0])
            print(f"✅ 选择文本列作为第一列: '{text_cols[0]}'")
        elif other_cols:
            # 最后选择其他列
            selected_cols.append(other_cols[0])
            print(f"⚠️ 选择其他列作为第一列: '{other_cols[0]}'")
        else:
            # 如果没有可用列，使用第一个可用列
            if available_cols:
                selected_cols.append(available_cols[0])
                print(f"⚠️ 使用第一个可用列: '{available_cols[0]}'")

        # 第三步：选择其余列
        if len(selected_cols) > 0:
            first_col = selected_cols[0]

            # 构建剩余列列表（排除已选择的第一列）
            remaining_cols = []
            for col_list in [name_cols, identifier_cols, text_cols, other_cols]:
                for col in col_list:
                    if col != first_col and col not in selected_cols:
                        remaining_cols.append(col)

            # 添加剩余需要的列
            remaining_needed = num_cols - len(selected_cols)
            if remaining_needed > 0 and remaining_cols:
                selected_cols.extend(remaining_cols[:remaining_needed])

        # 确保至少有指定数量的列
        if len(selected_cols) < num_cols and available_cols:
            # 如果还需要更多列，从所有可用列中补充
            for col in available_cols:
                if col not in selected_cols:
                    selected_cols.append(col)
                    if len(selected_cols) >= num_cols:
                        break

        print(f"📋 最终选择的列: {selected_cols}")
        return selected_cols

    def _is_numeric_sequence_column(self, column_name):
        """检查列是否是数字序号列"""
        # 检查列名是否暗示数字序号
        numeric_indicators = ["序号", "编号", "ID", "id", "No", "no", "Number", "number"]

        # 如果列名包含这些关键词，可能是序号列
        for indicator in numeric_indicators:
            if indicator in column_name:
                return True

        # 如果列名本身就是数字或简单的字母，也认为是序号列
        if column_name.isdigit() or (len(column_name) <= 2 and column_name.isalpha()):
            return True

        return False

    def generate_table_data(self, schema, num_rows):
        """根据表格模式生成数据"""
        data = {}
        theme = schema.get_theme()
        column_defs = schema.get_column_definitions()

        # 为每列生成数据
        for col_def in column_defs:
            data[col_def.name] = self.data_generator.generate(col_def, num_rows, theme)

        df = pd.DataFrame(data)

        # 处理特殊的计算列
        for col_def in column_defs:
            if col_def.data_type == "calculated_sum":
                source_columns = col_def.generator_params.get("columns", [])
                if all(col in df.columns for col in source_columns):
                    df[col_def.name] = df[source_columns].sum(axis=1)
            elif col_def.data_type == "rank":
                based_on = col_def.generator_params.get("based_on", "")
                if based_on in df.columns:
                    df[col_def.name] = df[based_on].rank(method='min', ascending=False).astype(int)

        return df

    def calculate_column_widths(self, columns, df):
        """计算列宽度"""
        widths = []
        for col in columns:
            # 计算列名和数据的最大长度
            max_len = max(
                len(str(col)),
                df[col].astype(str).str.len().max() if col in df.columns else 0
            )
            # 基于内容长度计算宽度，设置最小和最大值
            width = max(1.5, min(4.0, max_len * 0.15 + 0.8))
            widths.append(width)
        return widths

    def determine_row_label_for_theme(self, schema):
        """根据主题确定最合适的行标识文本"""
        actual_theme = schema.get_theme()

        # 基于实际主题的行标识映射 - 完整覆盖所有可用主题
        theme_row_labels = {
            # 医疗健康类
            "medical_records": "患者",
            "hospital_registration": "患者",
            "healthcare": "医院",
            "health": "记录",
            "insurance_claims": "理赔",

            # 用户信息类
            "user_info": "用户",
            "customer_feedback": "客户",
            "personnel": "员工",

            # 新闻媒体类
            "news_release": "新闻",
            "blog_posts": "文章",
            "email_data": "邮件",

            # 娱乐文化类
            "music": "歌手",
            "sports": "赛事",
            "sports_equipment": "器材",

            # 旅游出行类
            "travel": "景点",
            "hotel_reservation": "预订",
            "flight_itinerary": "航班",
            "transportation": "车辆",
            "vehicle_repair": "车辆",

            # 图书教育类
            "library_lending": "图书",
            "education": "学生",
            "education_institution": "机构",
            "job_recruitment": "职位",

            # 房产城市类
            "real_estate": "房产",
            "cities": "城市",
            "major_cities": "城市",
            "residence_data": "住户",

            # 科技项目类
            "technology": "项目",
            "project": "项目",
            "iot_sensor_data": "设备",
            "api_request_logs": "接口",
            "network_logs": "访问",

            # 环境监测类
            "environment": "环境",
            "weather": "天气",

            # 餐饮食品类
            "food": "餐厅",
            "food_delivery": "订单",

            # 金融财务类
            "finance": "交易",
            "financial": "账户",
            "payment_records": "交易",
            "bank_account": "账户",
            "order_records": "订单",

            # 商业企业类
            "company": "公司",
            "product": "产品",
            "inventory": "商品",

            # 会议活动类
            "meeting_attendance": "参会"
        }

        # 如果主题不在映射中，尝试根据主题名称智能推断
        if actual_theme not in theme_row_labels:
            # 根据主题名称的关键词进行智能匹配
            theme_lower = actual_theme.lower()

            if any(keyword in theme_lower for keyword in ['user', 'customer', 'client']):
                return "用户"
            elif any(keyword in theme_lower for keyword in ['company', 'business', 'enterprise']):
                return "公司"
            elif any(keyword in theme_lower for keyword in ['product', 'item', 'goods']):
                return "产品"
            elif any(keyword in theme_lower for keyword in ['order', 'purchase', 'transaction']):
                return "订单"
            elif any(keyword in theme_lower for keyword in ['employee', 'staff', 'personnel']):
                return "员工"
            elif any(keyword in theme_lower for keyword in ['student', 'education', 'school']):
                return "学生"
            elif any(keyword in theme_lower for keyword in ['medical', 'health', 'patient']):
                return "患者"
            elif any(keyword in theme_lower for keyword in ['vehicle', 'car', 'transport']):
                return "车辆"
            elif any(keyword in theme_lower for keyword in ['book', 'library', 'reading']):
                return "图书"
            elif any(keyword in theme_lower for keyword in ['hotel', 'reservation', 'booking']):
                return "预订"
            elif any(keyword in theme_lower for keyword in ['news', 'article', 'media']):
                return "新闻"
            elif any(keyword in theme_lower for keyword in ['music', 'song', 'artist']):
                return "歌手"
            elif any(keyword in theme_lower for keyword in ['sport', 'game', 'match']):
                return "赛事"
            elif any(keyword in theme_lower for keyword in ['travel', 'tourism', 'scenic']):
                return "景点"
            elif any(keyword in theme_lower for keyword in ['environment', 'ecology', 'green']):
                return "环境"
            elif any(keyword in theme_lower for keyword in ['technology', 'tech', 'innovation']):
                return "项目"
            elif any(keyword in theme_lower for keyword in ['finance', 'financial', 'money']):
                return "交易"
            elif any(keyword in theme_lower for keyword in ['city', 'urban', 'municipal']):
                return "城市"
            elif any(keyword in theme_lower for keyword in ['food', 'restaurant', 'dining']):
                return "餐厅"
            elif any(keyword in theme_lower for keyword in ['equipment', 'device', 'tool']):
                return "设备"
            elif any(keyword in theme_lower for keyword in ['meeting', 'conference', 'attendance']):
                return "参会"
            elif any(keyword in theme_lower for keyword in ['log', 'record', 'data']):
                return "记录"
            else:
                # 如果都不匹配，使用通用标识
                return "记录"

        return theme_row_labels.get(actual_theme, "记录")

    def wrap_text(self, text, max_width=15):
        """文本换行处理"""
        text = str(text)
        if len(text) <= max_width:
            return text

        # 简单的换行逻辑
        lines = []
        for i in range(0, len(text), max_width):
            lines.append(text[i:i+max_width])
        return '\n'.join(lines)

    def generate_dynamic_header_texts(self, schema, columns):
        """根据表格主题和第一列内容动态生成表头文本，确保语义一致性

        新逻辑：直接使用第一列的列名作为左上角标识，确保完全匹配
        """
        # 第一步：直接使用第一列的列名作为左上角标识
        first_column_name = columns[0] if columns else "项目"

        # 对列名进行适当的处理，使其适合作为表头标识
        row_text = self._process_column_name_for_header(first_column_name)

        # 生成列标识（右下角文本）- 使用实际主题名称
        actual_theme = schema.get_theme()
        col_text_mappings = {
            "food_delivery": "详情",
            "restaurant": "菜品",
            "user_info": "属性",
            "customer_feedback": "反馈",
            "employee": "信息",
            "medical_records": "指标",
            "hospital_registration": "信息",
            "news_release": "内容",
            "blog_posts": "内容",
            "music": "属性",
            "travel": "信息",
            "hotel_reservation": "详情",
            "library_lending": "状态",
            "real_estate": "信息",
            "cities": "信息",
            "major_cities": "信息",
            "technology": "指标",
            "environment": "数据",
            "finance": "属性",
            "payment_records": "属性",
            "order_records": "属性",
            "bank_account": "属性",
            "company": "属性",
            "product": "属性",
            "education": "属性",
            "transportation": "属性",
            "meeting_attendance": "属性"
        }

        col_text = col_text_mappings.get(actual_theme, "属性")

        return row_text, col_text

    def _process_column_name_for_header(self, column_name):
        """处理列名，使其适合作为表头标识"""
        # 移除常见的后缀，保留核心含义
        processed_name = column_name

        # 移除常见后缀
        suffixes_to_remove = ["名称", "编号", "ID", "id", "号码", "代码"]
        for suffix in suffixes_to_remove:
            if processed_name.endswith(suffix):
                processed_name = processed_name[:-len(suffix)]
                break

        # 如果处理后为空或太短，使用原名称
        if len(processed_name) < 2:
            processed_name = column_name

        # 特殊处理：如果是一些通用词汇，保持原样
        if processed_name in ["用户", "客户", "患者", "学生", "员工", "公司", "产品", "订单", "交易", "设备", "项目"]:
            return processed_name

        return processed_name

    def generate_triple_header_texts(self, schema, columns):
        """为双斜线表头格式生成三个区域的文本

        基于红色数字标注的正确语义对应关系：
        - 红色"1" = 列标题组 → text2 (右上大三角)
        - 红色"2" = 行标识组 → text1 (左上小三角)
        - 红色"3" = 数据内容组 → text3 (下方矩形)

        Args:
            schema: 表格模式对象
            columns: 列名列表

        Returns:
            tuple: (text1, text2, text3) 分别对应左上小三角、右上大三角、下方矩形的文本
        """
        # 获取基础的行列标识
        row_text, col_text = self.generate_dynamic_header_texts(schema, columns)
        actual_theme = schema.get_theme()

        # 根据正确的语义对应关系重新分配文本：
        # text1 (左上小三角) = 行标识类型 (对应第一列的分类)
        # text2 (右上大三角) = 列标题类型 (对应其他列的字段)
        # text3 (下方矩形) = 数据内容类型 (对应表格数据单元格)

        # 为双斜线格式生成数据内容类型标识 (下方矩形区域)
        # 使用通用性文本，避免特定性过强导致的语义不匹配问题

        # 通用性文本库：适用于所有数据类型的文本选项
        universal_texts = [
            "数据",    # 最通用的数据表示
            "内容",    # 适用于各种内容类型
            "信息",    # 通用信息表示
            "记录",    # 适用于各种记录类型
            "详情",    # 适用于详细信息
            "分类",    # 适用于分类数据
            "条目",    # 适用于列表项目
            "项目",    # 适用于各种项目
            "要素",    # 适用于组成要素
            "明细",    # 适用于详细明细
            "统计",    # 适用于统计数据
            "指标",    # 适用于各种指标
            "属性",    # 适用于属性数据
            "状态",    # 适用于状态信息
            "结果"     # 适用于结果数据
        ]

        # 使用通用性文本映射，避免特定性过强的文本
        # 所有主题都使用通用文本，确保语义适配性
        import random

        # 为不同主题类别分配合适的通用文本
        # 根据主题特征选择最合适的通用文本，但都保持通用性
        theme_category_mappings = {
            # 数据密集型主题 → 使用"数据"、"统计"、"指标"
            "data_intensive": ["数据", "统计", "指标", "明细"],
            # 内容型主题 → 使用"内容"、"信息"、"详情"
            "content_based": ["内容", "信息", "详情", "要素"],
            # 记录型主题 → 使用"记录"、"条目"、"明细"
            "record_based": ["记录", "条目", "明细", "项目"],
            # 分类型主题 → 使用"分类"、"属性"、"状态"
            "category_based": ["分类", "属性", "状态", "结果"],
            # 通用型主题 → 使用最通用的文本
            "general": ["数据", "信息", "内容", "记录"]
        }

        # 主题分类映射：将具体主题归类到通用类别
        theme_to_category = {
            # 数据密集型主题
            "medical_records": "data_intensive",
            "sales_data": "data_intensive",
            "finance": "data_intensive",
            "iot_sensor_data": "data_intensive",
            "weather": "data_intensive",
            "sports": "data_intensive",

            # 内容型主题
            "news_release": "content_based",
            "blog_posts": "content_based",
            "customer_feedback": "content_based",
            "email_data": "content_based",

            # 记录型主题
            "order_records": "record_based",
            "payment_records": "record_based",
            "library_lending": "record_based",
            "meeting_attendance": "record_based",
            "network_logs": "record_based",
            "vehicle_repair": "record_based",
            "flight_itinerary": "record_based",

            # 分类型主题
            "user_info": "category_based",
            "employee": "category_based",
            "product": "category_based",
            "inventory": "category_based",
            "transportation": "category_based",
            "company": "category_based",
            "personnel": "category_based",

            # 其他主题使用通用类别
            "hospital_registration": "general",
            "music": "general",
            "travel": "general",
            "hotel_reservation": "general",
            "real_estate": "general",
            "cities": "general",
            "major_cities": "general",
            "technology": "general",
            "environment": "general",
            "food": "general",
            "food_delivery": "general",
            "healthcare": "general",
            "project": "general",
            "residence_data": "general"
        }

        # 根据主题选择合适的通用文本
        theme_category = theme_to_category.get(actual_theme, "general")
        available_texts = theme_category_mappings[theme_category]

        # 随机选择一个通用文本，确保多样性
        data_content_text = random.choice(available_texts)

        # 返回三个文本，遵循正确的语义对应关系：
        # text1: 左上小三角 = 行标识类型 (对应红色"2")
        # text2: 右上大三角 = 列标题类型 (对应红色"1")
        # text3: 下方矩形 = 数据内容类型 (对应红色"3")
        return row_text, col_text, data_content_text

    def draw_precise_diagonal_line(self, ax, table, text1, text2):
        """精确绘制对角线在第一个单元格内"""
        try:
            # 注释：使用基于表格整体位置的计算方法，确保对角线精确定位在第一个单元格内

            # 注释：获取表格在画布上的实际边界框位置
            table_bbox = table.get_window_extent(ax.figure.canvas.get_renderer())

            # 注释：将表格边界框坐标转换到axes坐标系（0-1范围的相对坐标）
            table_bbox_axes = table_bbox.transformed(ax.transAxes.inverted())

            # 注释：计算表格的总行数和列数，用于确定单个单元格的尺寸
            num_rows = len(set(pos[0] for pos in table._cells.keys()))
            num_cols = len(set(pos[1] for pos in table._cells.keys()))

            # 注释：计算单个单元格的宽度和高度（在axes坐标系中的相对尺寸）
            cell_width = table_bbox_axes.width / num_cols   # 单元格宽度 = 表格总宽度 / 列数
            cell_height = table_bbox_axes.height / num_rows # 单元格高度 = 表格总高度 / 行数

            # 注释：确定第一个单元格（左上角单元格）的四个边界坐标
            cell_left = table_bbox_axes.x0                  # 左边界：表格左边界
            cell_right = cell_left + cell_width             # 右边界：左边界 + 单元格宽度
            cell_top = table_bbox_axes.y1                   # 上边界：表格上边界（y1是上边界）
            cell_bottom = cell_top - cell_height            # 下边界：上边界 - 单元格高度

            # 可调参数：对角线边距，控制对角线与单元格边框的距离
            # 增大这些值会使对角线更短，远离边框；减小会使对角线更长，接近边框
            margin_x = cell_width * 0.02# 水平边距：单元格宽度的8%
            margin_y = cell_height * 0.06  # 垂直边距：单元格高度的8%

            # 注释：计算对角线的起止坐标（从左上角到右下角）
            line_start_x = cell_left + margin_x    # 起点X：左边界 + 水平边距
            line_start_y = cell_top - margin_y     # 起点Y：上边界 - 垂直边距
            line_end_x = cell_right - margin_x     # 终点X：右边界 - 水平边距
            line_end_y = cell_bottom + margin_y    # 终点Y：下边界 + 垂直边距

            # 可调参数：对角线样式设置
            ax.plot([line_start_x, line_end_x], [line_start_y, line_end_y],
                   'k-',                           # 线条颜色：黑色
                   linewidth=1.2,                  # 可调参数：线条粗细，增大值使线条更粗
                   transform=ax.transAxes,         # 使用axes坐标系
                   zorder=10)                      # 图层顺序：确保对角线在表格上方

            # 注释：添加对角线两侧的文本标识

            # 注释：左上角文本（行标识）- 定位在单元格左上角区域
            # 可调参数：文本水平位置，控制文本距离左边界的距离
            text1_x = cell_left + cell_width * 0.05    # 水平位置：左边界 + 宽度的5%（靠近左边）
            # 可调参数：文本垂直位置，控制文本距离上边界的距离
            text1_y = cell_top - cell_height * 0.75    # 垂直位置：上边界 - 高度的15%（靠近上边）
            ax.text(text1_x, text1_y, text1,
                   fontsize=self.header_fontsize-2,       # 可调参数：字体大小，减2确保文本不会太大
                   ha='left',                             # 水平对齐：左对齐
                   va='top',                              # 垂直对齐：顶部对齐
                   weight='bold',                         # 字体粗细：加粗
                   transform=ax.transAxes,                # 使用axes坐标系
                   zorder=11)                             # 图层顺序：确保文本在对角线上方

            # 注释：右下角文本（列标识）- 定位在单元格右下角区域
            # 可调参数：文本水平位置，控制文本距离右边界的距离
            text2_x = cell_right - cell_width * 0.05   # 水平位置：右边界 - 宽度的5%（靠近右边）
            # 可调参数：文本垂直位置，控制文本距离下边界的距离
            text2_y = cell_bottom + cell_height * 0.75 # 垂直位置：下边界 + 高度的15%（靠近下边）
            ax.text(text2_x, text2_y, text2,
                   fontsize=self.header_fontsize-2,       # 可调参数：字体大小，与左上角文本保持一致
                   ha='right',                            # 水平对齐：右对齐
                   va='bottom',                           # 垂直对齐：底部对齐
                   weight='bold',                         # 字体粗细：加粗
                   transform=ax.transAxes,                # 使用axes坐标系
                   zorder=11)                             # 图层顺序：确保文本在对角线上方

            # 注释：参数调整建议
            # - margin_x/margin_y: 0.05-0.15 范围，控制对角线长度
            # - text位置的0.05: 0.02-0.10 范围，控制文本距离边角的距离
            # - text位置的0.15: 0.10-0.25 范围，控制文本距离边界的距离
            # - linewidth: 0.8-2.0 范围，控制对角线粗细
            # - fontsize调整: -3到+1 范围，控制文本大小

        except Exception as e:
            print(f"Warning: 精确对角线绘制失败: {e}")
            # 备用方案：使用固定位置
            self.draw_fallback_diagonal(ax, text1, text2)

    def draw_fallback_diagonal(self, ax, text1, text2):
        """备用对角线绘制方案 - 当精确计算失败时使用固定位置"""

        # 注释：使用固定的相对位置作为备用方案，确保在任何情况下都能绘制对角线
        # 可调参数：单元格位置和尺寸的固定值（在axes坐标系中，范围0-1）
        cell_left = 0.1      # 可调参数：左边界位置，0.05-0.15范围，控制整体水平位置
        cell_width = 0.12    # 可调参数：单元格宽度，0.08-0.20范围，控制对角线长度
        cell_top = 0.85      # 可调参数：上边界位置，0.80-0.90范围，控制整体垂直位置
        cell_height = 0.06   # 可调参数：单元格高度，0.04-0.10范围，控制对角线高度

        # 注释：绘制备用对角线（从左上到右下）
        ax.plot([cell_left, cell_left + cell_width],           # X坐标：从左边界到右边界
               [cell_top, cell_top - cell_height],             # Y坐标：从上边界到下边界
               'k-',                                           # 线条样式：黑色实线
               linewidth=1.5,                                  # 可调参数：线条粗细，1.0-2.5范围
               transform=ax.transAxes,                         # 使用axes坐标系
               zorder=10)                                      # 图层顺序

        # 注释：添加备用文本位置
        # 左上角文本（行标识）
        ax.text(cell_left + cell_width * 0.1,                  # 可调参数：水平偏移，0.05-0.20范围
               cell_top - cell_height * 0.5,                   # 可调参数：垂直偏移，0.10-0.30范围
               text1,
               fontsize=self.header_fontsize-1,                # 可调参数：字体大小调整，-3到+1范围
               ha='left', va='top',                            # 对齐方式：左上对齐
               weight='bold',                                  # 字体粗细：加粗
               transform=ax.transAxes, zorder=11)              # 坐标系和图层

        # 右下角文本（列标识）
        ax.text(cell_left + cell_width * 0.9,                  # 可调参数：水平偏移，0.80-0.95范围
               cell_top - cell_height * 0.5,                   # 可调参数：垂直偏移，0.70-0.90范围
               text2,
               fontsize=self.header_fontsize-1,                # 可调参数：字体大小调整，与左上角保持一致
               ha='right', va='bottom',                        # 对齐方式：右下对齐
               weight='bold',                                  # 字体粗细：加粗
               transform=ax.transAxes, zorder=11)              # 坐标系和图层

        # 注释：备用方案参数调整建议
        # - cell_left/cell_top: 调整整体位置，确保不与表格重叠
        # - cell_width/cell_height: 调整对角线区域大小，适应不同表格尺寸
        # - 文本偏移百分比: 微调文本位置，避免与对角线重叠
        # - linewidth: 根据图像分辨率调整线条粗细

    def draw_double_diagonal_lines(self, ax, table, text1, text2, text3):
        """绘制精确的双对角线表头格式 - 完全匹配红色参考图像

        Args:
            ax: matplotlib axes对象
            table: 表格对象
            text1: 左上角小三角形区域文本（对应"属性"）
            text2: 右上角大三角形区域文本（对应"项目"）
            text3: 下方矩形区域文本（对应"分类"）
        """
        try:
            # 注释：精确双对角线表头设计说明


            # 注释：获取表格在画布上的实际边界框位置
            table_bbox = table.get_window_extent(ax.figure.canvas.get_renderer())
            table_bbox_axes = table_bbox.transformed(ax.transAxes.inverted())

            # 注释：计算表格的总行数和列数
            num_rows = len(set(pos[0] for pos in table._cells.keys()))
            num_cols = len(set(pos[1] for pos in table._cells.keys()))

            # 注释：计算第一个单元格的边界坐标
            cell_width = table_bbox_axes.width / num_cols
            cell_height = table_bbox_axes.height / num_rows
            cell_left = table_bbox_axes.x0
            cell_right = cell_left + cell_width
            cell_top = table_bbox_axes.y1
            cell_bottom = cell_top - cell_height

            # 可调参数：精确匹配新红色参考图像的样式配置
            line_margin_x = cell_width * 0.02    # 可调参数：水平边距，进一步减小以匹配红色线条
            line_margin_y = cell_height * 0.02   # 可调参数：垂直边距，进一步减小以匹配红色线条
            line_width = 1.0                     # 可调参数：线条粗细，匹配图像中的线条
            line_color = 'black'                 # 可调参数：线条颜色
            line_style = '-'                     # 可调参数：线条样式

            # 可调参数：垂直分割线位置（根据新红色图像调整为更靠左的位置）
            vertical_position = 0.18             # 可调参数：垂直线位置，匹配新红色图像中的精确比例

            # 注释：绘制主对角线（从左上到右下）
            # 精确匹配新红色参考图像中的对角线位置和角度
            # 根据红色线条调整起始点和终点，使其更贴近边缘
            diagonal_start_x = cell_left + line_margin_x
            diagonal_start_y = cell_top - line_margin_y
            diagonal_end_x = cell_right - line_margin_x
            diagonal_end_y = cell_bottom + line_margin_y

            ax.plot([diagonal_start_x, diagonal_end_x], [diagonal_start_y, diagonal_end_y],
                   color=line_color, linestyle=line_style, linewidth=line_width,
                   transform=ax.transAxes, zorder=10)

            # 注释：绘制斜线分割线 - 详细端点位置控制和计算逻辑
            # 将垂直线改为斜线，以更精确匹配红色参考图像中的第二条分割线

            # ========== 斜线端点位置控制参数 ==========
            # 可调参数：斜线起始点和终点的独立位置控制
            diagonal2_start_x_ratio = 0.01     # 可调参数：斜线起始点X位置比例 (0.1-0.3范围)
            diagonal2_start_y_ratio = 0.01     # 可调参数：斜线起始点Y位置比例 (0.0-0.1范围)
            diagonal2_end_x_ratio = 0.5     # 可调参数：斜线终点X位置比例 (0.1-0.3范围)
            diagonal2_end_y_ratio = 0.98        # 可调参数：斜线终点Y位置比例 (0.9-1.0范围)

            # ========== 斜线起始点（上端点）坐标计算 ==========
            # diagonal2_start_x: 斜线起始点的X坐标（水平位置）
            # 计算公式: diagonal2_start_x = cell_left + cell_width * diagonal2_start_x_ratio
            # 参数说明:
            #   - cell_left: 单元格左边界的绝对坐标
            #   - cell_width: 单元格的总宽度
            #   - diagonal2_start_x_ratio: 起始点在单元格内的水平位置比例 (当前值: 0.18)
            # 计算逻辑:
            #   起始点X = 单元格左边界 + 单元格宽度 × 18%
            #   例如: 如果单元格宽度为100像素，起始点位于距离左边界18像素处
            # 调整建议:
            #   - 增大 diagonal2_start_x_ratio (如0.20) → 起始点右移
            #   - 减小 diagonal2_start_x_ratio (如0.15) → 起始点左移
            #   - 推荐范围: 0.10-0.30 (10%-30%位置)
            diagonal2_start_x = cell_left + cell_width * diagonal2_start_x_ratio

            # diagonal2_start_y: 斜线起始点的Y坐标（垂直位置）
            # 计算公式: diagonal2_start_y = cell_top - cell_height * diagonal2_start_y_ratio
            # 参数说明:
            #   - cell_top: 单元格顶部边界的绝对坐标
            #   - cell_height: 单元格的总高度
            #   - diagonal2_start_y_ratio: 起始点在单元格内的垂直位置比例 (当前值: 0.02)
            # 计算逻辑:
            #   起始点Y = 单元格顶部 - 单元格高度 × 2%
            #   负号表示从顶部向下偏移，2%表示接近顶部边界
            # 调整建议:
            #   - 增大 diagonal2_start_y_ratio (如0.05) → 起始点下移
            #   - 减小 diagonal2_start_y_ratio (如0.00) → 起始点贴近顶部边界
            #   - 推荐范围: 0.00-0.10 (0%-10%位置)
            diagonal2_start_y = cell_top - cell_height * diagonal2_start_y_ratio

            # ========== 斜线终点（下端点）坐标计算 ==========
            # diagonal2_end_x: 斜线终点的X坐标（水平位置）
            # 计算公式: diagonal2_end_x = cell_left + cell_width * diagonal2_end_x_ratio
            # 参数说明:
            #   - diagonal2_end_x_ratio: 终点在单元格内的水平位置比例 (当前值: 0.18)
            # 计算逻辑:
            #   终点X = 单元格左边界 + 单元格宽度 × 18%
            #   当前设置为与起始点相同的X位置，形成垂直线效果
            # 斜线角度控制:
            #   - 设置 diagonal2_end_x_ratio = diagonal2_start_x_ratio → 垂直线 (90度)
            #   - 设置 diagonal2_end_x_ratio > diagonal2_start_x_ratio → 向右倾斜 (锐角)
            #   - 设置 diagonal2_end_x_ratio < diagonal2_start_x_ratio → 向左倾斜 (钝角)
            # 调整建议:
            #   - 垂直线: diagonal2_end_x_ratio = 0.18 (与起始点相同)
            #   - 右倾斜: diagonal2_end_x_ratio = 0.22 (终点右移4%)
            #   - 左倾斜: diagonal2_end_x_ratio = 0.14 (终点左移4%)
            diagonal2_end_x = cell_left + cell_width * diagonal2_end_x_ratio

            # diagonal2_end_y: 斜线终点的Y坐标（垂直位置）
            # 计算公式: diagonal2_end_y = cell_top - cell_height * diagonal2_end_y_ratio
            # 参数说明:
            #   - diagonal2_end_y_ratio: 终点在单元格内的垂直位置比例 (当前值: 0.98)
            # 计算逻辑:
            #   终点Y = 单元格顶部 - 单元格高度 × 98%
            #   98%表示接近底部边界（距离底部2%）
            # 调整建议:
            #   - 增大 diagonal2_end_y_ratio (如1.00) → 终点贴近底部边界
            #   - 减小 diagonal2_end_y_ratio (如0.95) → 终点上移
            #   - 推荐范围: 0.90-1.00 (90%-100%位置)
            diagonal2_end_y = cell_top - cell_height * diagonal2_end_y_ratio

            # ========== 斜线角度和倾斜度控制详解 ==========
            # 斜线角度由两个端点的相对位置决定:
            # 角度计算公式: angle = arctan((end_y - start_y) / (end_x - start_x))
            # 当前设置分析:
            #   - X方向差值: diagonal2_end_x - diagonal2_start_x = 0 (相同X坐标)
            #   - Y方向差值: diagonal2_end_y - diagonal2_start_y = 96%单元格高度
            #   - 结果: 形成垂直线（角度90度）
            #
            # 角度调整实例:
            #   1. 垂直线 (90度):
            #      start_x=0.18, end_x=0.18 → 完全垂直
            #   2. 轻微右倾斜 (约85度):
            #      start_x=0.18, end_x=0.22 → 向右倾斜4%
            #   3. 明显右倾斜 (约80度):
            #      start_x=0.18, end_x=0.26 → 向右倾斜8%
            #   4. 轻微左倾斜 (约95度):
            #      start_x=0.18, end_x=0.14 → 向左倾斜4%

            # ========== 与红色参考图像的匹配建议 ==========
            # 根据红色参考图像中的斜线特征，提供多种参数设置选项:
            #
            # 选项1 - 垂直线（当前默认设置）:
            #   diagonal2_start_x_ratio = 0.18, diagonal2_end_x_ratio = 0.18
            #   效果: 完全垂直的分割线，符合传统双对角线表头标准
            #
            # 选项2 - 轻微右倾斜（匹配某些传统格式）:
            #   diagonal2_start_x_ratio = 0.18, diagonal2_end_x_ratio = 0.22
            #   效果: 向右倾斜约5度，形成更动态的视觉效果
            #
            # 选项3 - 轻微左倾斜（特殊格式需求）:
            #   diagonal2_start_x_ratio = 0.18, diagonal2_end_x_ratio = 0.14
            #   效果: 向左倾斜约5度，与主对角线形成对称效果

            # ========== 与主对角线的协调性检查 ==========
            # 主对角线特征: 从左上角(margin, margin)到右下角(1-margin, 1-margin)
            # 斜线分割特征: 从(0.18, 0.02)到(0.18, 0.98)
            #
            # 协调性分析:
            #   - 两线交点: 约在(0.18, 0.18)位置
            #   - 形成三个区域: 左上小三角、右上大三角、下方矩形
            #   - 角度差异: 主对角线约45度，斜线90度，差异45度
            #   - 无重叠风险: 角度差异明显，视觉层次清晰
            #
            # 避免冲突的建议:
            #   1. 保持斜线X位置在0.15-0.25范围内，避免过于靠近边缘
            #   2. 确保斜线不与主对角线平行（避免角度相同）
            #   3. 如果调整斜线角度，建议保持与主对角线至少30度的角度差
            #   4. 测试不同角度时，确保三个文本区域仍有足够空间

            # ========== 当前参数值及实际效果总结 ==========
            # diagonal2_start_x_ratio = 0.18 → 起始点位于左侧18%位置
            # diagonal2_start_y_ratio = 0.02 → 起始点距离顶部2%（接近顶部边界）
            # diagonal2_end_x_ratio = 0.18 → 终点位于左侧18%位置（与起始点同列）
            # diagonal2_end_y_ratio = 0.98 → 终点距离顶部98%（接近底部边界）
            #
            # 实际效果:
            #   - 形成垂直分割线，将单元格分为左侧18%和右侧82%区域
            #   - 左侧区域较小，适合放置简短标识文本
            #   - 右侧区域较大，适合放置主要分类文本
            #   - 下方区域为完整矩形，适合放置详细分类说明

            ax.plot([diagonal2_start_x, diagonal2_end_x], [diagonal2_start_y, diagonal2_end_y],
                   color=line_color, linestyle=line_style, linewidth=line_width,
                   transform=ax.transAxes, zorder=10)

            # 注释：添加三个区域的文本标识
            # 精确匹配红色参考图像中的文本位置布局

            # 可调参数：文本样式配置（匹配红色图像的文本效果）
            text_fontsize = self.header_fontsize - 2    # 可调参数：字体大小，稍小以适应小区域
            text_color = 'black'                        # 可调参数：文本颜色
            text_weight = 'normal'                      # 可调参数：字体粗细，使用正常粗细
            text_alpha = 1.0                            # 可调参数：文本透明度，0.0-1.0范围

            # 注释：精确计算三个区域的文本位置（基于新红色参考图像）
            # 区域1：左上角小三角形（对应新红色图像中的"项目"）
            # 位置：在垂直线左侧、对角线上方的小三角形中心（调整为更小的区域）
            text1_x = cell_left + cell_width * (vertical_position * 0.8)
            text1_y = cell_top - cell_height * 0.8
            ax.text(text1_x, text1_y, text1,
                   fontsize=text_fontsize, color=text_color, weight=text_weight, alpha=text_alpha,
                   ha='center', va='center', transform=ax.transAxes, zorder=11)

            # 区域2：右上角大三角形（对应新红色图像中的"属性"）
            # 位置：在垂直线右侧、对角线上方的大三角形中心（调整为更大的区域）
            text2_x = cell_left + cell_width * (vertical_position + (1 - vertical_position) * 0.5)
            text2_y = cell_top - cell_height * 0.25
            ax.text(text2_x, text2_y, text2,
                   fontsize=text_fontsize, color=text_color, weight=text_weight, alpha=text_alpha,
                   ha='center', va='center', transform=ax.transAxes, zorder=11)

            # 区域3：下方矩形区域（对应新红色图像中的"分类"）
            # 位置：在对角线下方的整个矩形区域中心
            text3_x = cell_left + cell_width * 0.5
            text3_y = cell_top - cell_height * 0.75
            ax.text(text3_x, text3_y, text3,
                   fontsize=text_fontsize, color=text_color, weight=text_weight, alpha=text_alpha,
                   ha='center', va='center', transform=ax.transAxes, zorder=11)

            # 注释：精确双对角线参数调整建议（基于新红色参考图像）
            # 线条参数：
            # - line_margin_x/y: 0.02，进一步减小边距以匹配红色线条位置
            # - line_width: 1.0，匹配图像线条粗细
            # - vertical_position: 0.18，匹配新红色图像中更靠左的垂直线位置
            # 文本参数：
            # - text_fontsize: header_fontsize-2，适应小区域文本
            # - 文本位置: 基于新红色图像精确计算的几何中心
            # - text_weight: normal，匹配图像文本样式
            # 布局说明（完全匹配新红色参考图像）：
            # - 左上小三角：显示"项目"类标识（更小的区域）
            # - 右上大三角：显示"属性"类标识（更大的区域）
            # - 下方矩形：显示"分类"类标识

        except Exception as e:
            print(f"Warning: 精确双对角线绘制失败: {e}")
            # 备用方案：使用单斜线格式
            self.draw_precise_diagonal_line(ax, table, text1, text3)

    def render_card_line_table(self, df, img_index, schema, header_format='single'):
        """渲染卡线表格图像

        Args:
            df: 数据框
            img_index: 图像索引
            schema: 表格模式
            header_format: 表头格式，'single'=单斜线，'double'=双斜线
        """
        # 线程安全的matplotlib配置
        import matplotlib
        matplotlib.use('Agg')  # 确保使用非交互式后端

        # 在每个线程中重新设置字体配置，避免线程间冲突
        import matplotlib.pyplot as plt
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        # 验证输入数据
        if df is None or df.empty:
            raise ValueError("输入数据框为空")

        if len(df.columns) < 2:
            raise ValueError(f"卡线表至少需要2列，当前只有{len(df.columns)}列")

        # 处理数据：转换长文本为多行文本
        df_processed = df.copy()
        max_text_width = 12  # 卡线表文本宽度稍小

        for col in df_processed.columns:
            df_processed[col] = df_processed[col].astype(str).apply(
                lambda x: self.wrap_text(x, max_text_width)
            )

        # 准备数据
        columns = df_processed.columns.tolist()
        data_rows = df_processed.values.tolist()

        # 计算表格尺寸
        cell_widths = self.calculate_column_widths(columns, df_processed)
        total_width = sum(cell_widths) + 1
        total_height = self.row_height * (len(data_rows) + 2)

        # 创建图像 - 添加错误处理
        try:
            fig = plt.figure(figsize=(total_width, total_height))
            ax = plt.gca()
            ax.axis('off')

            # 创建表格 - 验证数据完整性
            if not data_rows or not columns:
                raise ValueError("表格数据或列名为空")

            table = ax.table(
                cellText=data_rows,
                colLabels=columns,
                cellLoc='center',
                loc='center',
                bbox=[0.0, 0.0, 1.0, 1.0]
            )

            if table is None:
                raise ValueError("表格创建失败")

        except Exception as e:
            plt.close('all')  # 清理资源
            raise ValueError(f"图像创建失败: {e}")

        # 设置表格样式
        table.auto_set_font_size(False)
        table.set_fontsize(self.cell_fontsize)
        table.scale(1, 2)

        # 卡线表特有样式：完整网格线 + 对角线表头
        for pos, cell in table._cells.items():
            row, col = pos
            cell.set_height(self.row_height / (len(data_rows) + 1))
            cell.set_edgecolor('black')
            cell.set_linewidth(0.8)

            # 设置所有边框可见
            cell.visible_edges = 'TLBR'

            if row == 0:  # 表头行
                cell.set_text_props(weight='bold')
                cell.set_facecolor('#f8f9fa')

                # 为第一个单元格添加对角线（序号列）
                if col == 0:
                    # 清空原有文本，我们将手动添加对角线文本
                    cell.get_text().set_text('')
            else:  # 数据行
                cell.set_facecolor('white')
                if col == 0:  # 第一列加粗
                    cell.set_text_props(weight='bold')

        # 移除表格标题 - 只保留纯表格内容
        # format_suffix = "双斜线式" if header_format == 'double' else "卡线表式"
        # table_title = f"{schema.get_table_title_template()}（{format_suffix}） {img_index+1}"
        # ax.set_title(table_title, fontsize=self.title_fontsize, pad=20, weight='bold')

        # 根据表头格式选择绘制方法
        if header_format == 'double':
            # 双斜线格式：生成三个文本并绘制双斜线
            text1, text2, text3 = self.generate_triple_header_texts(schema, columns)
            self.draw_double_diagonal_lines(ax, table, text1, text2, text3)
        else:
            # 单斜线格式：生成两个文本并绘制单斜线
            row_text, col_text = self.generate_dynamic_header_texts(schema, columns)
            self.draw_precise_diagonal_line(ax, table, row_text, col_text)

        # 创建输出文件路径（根据表头格式区分文件名）
        try:
            display_name = schema.get_theme_display_name()
            format_suffix = "双斜线式" if header_format == 'double' else "卡线表"
            output_path = os.path.join(self.output_folder, f"{display_name}_{format_suffix}_{img_index+1}.jpg")

            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 保存图片 - 添加错误处理
            plt.tight_layout()
            plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight',
                       facecolor='white', edgecolor='none', format='jpeg')

            # 验证文件是否成功保存
            if not os.path.exists(output_path):
                raise IOError(f"文件保存失败: {output_path}")

            # 验证文件大小
            file_size = os.path.getsize(output_path)
            if file_size < 1024:  # 小于1KB可能是空白或损坏文件
                raise IOError(f"生成的文件过小 ({file_size} bytes)，可能是空白图像")

        except Exception as e:
            # 清理资源
            plt.close('all')
            gc.collect()
            raise IOError(f"图像保存失败: {e}")
        finally:
            # 确保资源被释放
            plt.close('all')
            # 强制垃圾回收以释放内存
            gc.collect()

        return output_path

    def generate_single_card_line_table(self, img_index, header_format='single'):
        """生成单个卡线表

        Args:
            img_index: 图像索引
            header_format: 表头格式，'single'=单斜线，'double'=双斜线
        """
        try:
            # 随机确定表格尺寸
            num_rows = random.randint(MIN_ROWS, MAX_ROWS)
            num_cols = random.randint(MIN_COLS, MAX_COLS)

            # 生成数据
            df, schema = self.generate_card_line_data(num_rows, num_cols)

            # 渲染表格
            output_path = self.render_card_line_table(df, img_index, schema, header_format)

            return {
                'success': True,
                'output_path': output_path,
                'theme': schema.get_theme(),
                'rows': len(df),
                'cols': len(df.columns)
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'img_index': img_index
            }

    def generate_batch_card_line_tables(self, num_images, batch_size=BATCH_SIZE, header_format='single'):
        """批量生成卡线表，优化内存使用

        Args:
            num_images: 生成图像数量
            batch_size: 批处理大小
            header_format: 表头格式，'single'=单斜线，'double'=双斜线
        """
        format_name = "双斜线表头" if header_format == 'double' else "单斜线表头"
        print(f"开始生成 {num_images} 张{format_name}卡线表图像...")
        print(f"批处理大小: {batch_size}")

        generated_images = []
        total_batches = (num_images + batch_size - 1) // batch_size

        start_time = time.time()

        for batch_idx in range(total_batches):
            batch_start = batch_idx * batch_size
            batch_end = min(batch_start + batch_size, num_images)
            batch_size_actual = batch_end - batch_start

            print(f"\n处理批次 {batch_idx + 1}/{total_batches} "
                  f"(图像 {batch_start + 1}-{batch_end})")

            batch_start_time = time.time()
            batch_results = []

            # 生成当前批次的图像
            for i in range(batch_start, batch_end):
                result = self.generate_single_card_line_table(i, header_format)
                batch_results.append(result)

                if result['success']:
                    print(f"  ✓ 图像 {i + 1}: {result['theme']} "
                          f"({result['rows']}行×{result['cols']}列)")
                else:
                    print(f"  ✗ 图像 {i + 1}: 生成失败 - {result['error']}")

            # 统计批次结果
            successful = [r for r in batch_results if r['success']]
            failed = [r for r in batch_results if not r['success']]

            batch_time = time.time() - batch_start_time
            print(f"  批次完成: {len(successful)}/{batch_size_actual} 成功, "
                  f"耗时 {batch_time:.2f}秒")

            generated_images.extend(successful)

            # 强制垃圾回收释放内存
            gc.collect()

        total_time = time.time() - start_time
        success_rate = len(generated_images) / num_images * 100

        print(f"\n=== 生成完成 ===")
        print(f"总计: {len(generated_images)}/{num_images} 张图像生成成功")
        print(f"成功率: {success_rate:.1f}%")
        print(f"总耗时: {total_time:.2f}秒")
        print(f"平均速度: {len(generated_images)/total_time:.2f} 张/秒")
        print(f"输出目录: {self.output_folder}")

        return generated_images

# ====================== 内容唯一性保证 ======================

class ContentUniquenessTracker:
    """内容唯一性跟踪器，确保重复率<0.01%"""

    def __init__(self):
        self.content_hashes = set()
        self.duplicate_count = 0
        self.total_count = 0

    def generate_content_hash(self, df, schema):
        """生成内容哈希值用于唯一性检查"""
        # 组合多个因素生成哈希
        content_parts = [
            schema.get_theme(),
            str(df.shape),
            str(df.columns.tolist()),
            str(df.iloc[0].tolist() if len(df) > 0 else []),  # 第一行数据
            str(df.iloc[-1].tolist() if len(df) > 0 else [])  # 最后一行数据
        ]
        content_str = '|'.join(content_parts)
        return hash(content_str)

    def is_unique(self, df, schema):
        """检查内容是否唯一"""
        content_hash = self.generate_content_hash(df, schema)
        self.total_count += 1

        if content_hash in self.content_hashes:
            self.duplicate_count += 1
            return False

        self.content_hashes.add(content_hash)
        return True

    def get_duplicate_rate(self):
        """获取重复率"""
        if self.total_count == 0:
            return 0.0
        return self.duplicate_count / self.total_count

# ====================== 主执行函数 ======================

def generate_card_line_tables(num_images=10, batch_size=BATCH_SIZE, ensure_uniqueness=True, header_format='single'):
    """生成指定数量的卡线表图像

    Args:
        num_images: 要生成的图像数量
        batch_size: 批处理大小
        ensure_uniqueness: 是否确保内容唯一性
        header_format: 表头格式，'single'=单斜线，'double'=双斜线
    """
    print("=" * 60)
    print("卡线表生成器 - Card-Line Table Generator")
    print("=" * 60)

    generator = CardLineTableGenerator()
    uniqueness_tracker = ContentUniquenessTracker() if ensure_uniqueness else None

    if ensure_uniqueness:
        print(f"启用内容唯一性检查，目标重复率 < 0.01%")

    # 如果需要确保唯一性，可能需要生成更多图像
    target_images = num_images
    max_attempts = int(num_images * 1.2) if ensure_uniqueness else num_images

    generated_images = []
    attempt_count = 0

    while len(generated_images) < target_images and attempt_count < max_attempts:
        remaining = target_images - len(generated_images)
        current_batch_size = min(batch_size, remaining, max_attempts - attempt_count)

        batch_results = generator.generate_batch_card_line_tables(
            current_batch_size, batch_size, header_format
        )

        if ensure_uniqueness:
            # 过滤重复内容
            unique_results = []
            for result in batch_results:
                if result['success']:
                    # 重新生成数据进行唯一性检查
                    try:
                        schema = TableSchema(result['theme'])
                        df, _ = generator.generate_card_line_data(
                            result['rows'], result['cols']
                        )

                        if uniqueness_tracker.is_unique(df, schema):
                            unique_results.append(result)
                        else:
                            print(f"  跳过重复内容: {result['theme']}")
                    except:
                        pass  # 跳过有问题的结果

            generated_images.extend(unique_results)
            duplicate_rate = uniqueness_tracker.get_duplicate_rate()
            print(f"当前重复率: {duplicate_rate:.4f} ({duplicate_rate*100:.2f}%)")
        else:
            generated_images.extend(batch_results)

        attempt_count += current_batch_size

    # 最终统计
    final_count = len(generated_images)
    print(f"\n=== 最终结果 ===")
    print(f"成功生成: {final_count}/{target_images} 张卡线表图像")

    if ensure_uniqueness and uniqueness_tracker:
        final_duplicate_rate = uniqueness_tracker.get_duplicate_rate()
        print(f"内容重复率: {final_duplicate_rate:.4f} ({final_duplicate_rate*100:.2f}%)")
        if final_duplicate_rate < 0.0001:
            print("✓ 达到唯一性要求 (< 0.01%)")
        else:
            print("⚠ 未完全达到唯一性要求")

    return generated_images

def generate_30k_card_line_tables():
    """生成30,000张卡线表图像的高性能函数"""
    print("开始生成30,000张卡线表图像...")
    print("这是一个大规模生成任务，请确保有足够的磁盘空间和时间")

    # 使用较大的批处理大小以提高效率
    large_batch_size = 200

    return generate_card_line_tables(
        num_images=30000,
        batch_size=large_batch_size,
        ensure_uniqueness=True
    )

# ====================== 双斜线测试专用函数 ======================

def test_precise_double_diagonal():
    """测试精确的双斜线表头格式 - 匹配红色参考图像"""
    print("🎯 精确双斜线表头测试")
    print("=" * 60)
    print("📋 测试目标:")
    print("  - 完全匹配红色参考图像的双斜线布局")
    print("  - 验证三个区域的文本定位精确性")
    print("  - 确保线条角度和位置符合传统标准")
    print("  - 生成10个测试样本进行验证")
    print()

    try:
        # 生成10个精确双斜线格式的测试图像
        results = generate_card_line_tables(
            num_images=10,
            batch_size=10,
            ensure_uniqueness=False,
            header_format='double'
        )

        success_count = 0
        test_files = []

        for i, result in enumerate(results):
            if result['success']:
                success_count += 1
                filename = os.path.basename(result['output_path'])
                test_files.append(filename)

                print(f"  ✓ 测试图像 {i+1}: {filename}")
                print(f"    主题: {result['theme']} ({result['rows']}行×{result['cols']}列)")
                print(f"    路径: {result['output_path']}")

                # 验证文件名格式
                if "双斜线式" in filename:
                    print(f"    ✅ 文件名格式正确")
                else:
                    print(f"    ⚠️ 文件名格式需要检查")
                print()
            else:
                print(f"  ✗ 测试图像 {i+1}: 生成失败")

        print(f"📊 精确双斜线测试结果:")
        print(f"  成功生成: {success_count}/10 张图像")
        print(f"  成功率: {success_count/10*100:.1f}%")

        if success_count >= 8:
            print(f"\n🎉 精确双斜线测试通过！")
            print(f"\n📸 生成的测试文件:")
            for filename in test_files:
                print(f"  - {filename}")

            print(f"\n🔍 验证要点:")
            print(f"  1. 检查主对角线是否从左上到右下")
            print(f"  2. 检查垂直线是否在1/4位置")
            print(f"  3. 检查左上小三角形文本位置")
            print(f"  4. 检查右上大三角形文本位置")
            print(f"  5. 检查下方矩形区域文本位置")
            print(f"  6. 确认整体布局匹配红色参考图像")

            return True
        else:
            print(f"\n❌ 精确双斜线测试失败！")
            return False

    except Exception as e:
        print(f"❌ 精确双斜线测试异常: {e}")
        return False

def quick_double_diagonal_test():
    """快速双斜线测试 - 生成3个样本快速验证"""
    print("⚡ 快速双斜线验证")
    print("=" * 40)

    try:
        results = generate_card_line_tables(
            num_images=3,
            batch_size=3,
            ensure_uniqueness=False,
            header_format='double'
        )

        success_count = sum(1 for r in results if r['success'])

        print(f"快速测试结果: {success_count}/3 成功")

        if success_count >= 2:
            print("✅ 快速测试通过")
            return True
        else:
            print("❌ 快速测试失败")
            return False

    except Exception as e:
        print(f"❌ 快速测试异常: {e}")
        return False

def compare_single_vs_double_diagonal():
    """对比单斜线和双斜线格式的差异"""
    print("🔄 单斜线 vs 双斜线格式对比")
    print("=" * 50)

    try:
        print("生成对比样本...")

        # 生成单斜线格式
        print("\n1. 生成单斜线格式样本:")
        single_results = generate_card_line_tables(
            num_images=2,
            batch_size=2,
            ensure_uniqueness=False,
            header_format='single'
        )

        # 生成双斜线格式
        print("\n2. 生成双斜线格式样本:")
        double_results = generate_card_line_tables(
            num_images=2,
            batch_size=2,
            ensure_uniqueness=False,
            header_format='double'
        )

        print(f"\n📋 格式对比结果:")

        # 显示单斜线文件
        print(f"\n单斜线格式文件:")
        for result in single_results:
            if result['success']:
                filename = os.path.basename(result['output_path'])
                print(f"  📄 {filename}")

        # 显示双斜线文件
        print(f"\n双斜线格式文件:")
        for result in double_results:
            if result['success']:
                filename = os.path.basename(result['output_path'])
                print(f"  📄 {filename}")

        single_success = sum(1 for r in single_results if r['success'])
        double_success = sum(1 for r in double_results if r['success'])

        if single_success >= 1 and double_success >= 1:
            print(f"\n✅ 格式对比测试成功！")
            print(f"\n🔍 对比要点:")
            print(f"  - 单斜线: 一条对角线，两个区域")
            print(f"  - 双斜线: 主对角线+垂直线，三个区域")
            print(f"  - 文件名: '卡线表' vs '双斜线式'")
            print(f"  - 布局: 简单分割 vs 精确三角形分割")
            return True
        else:
            print(f"\n❌ 格式对比测试失败！")
            return False

    except Exception as e:
        print(f"❌ 格式对比测试异常: {e}")
        return False

# ====================== 脚本执行入口 ======================

if __name__ == "__main__":
    print("卡线表生成器启动...")

    # 测试生成少量图像
    test_images = generate_card_line_tables(num_images=5, batch_size=5)

    print(f"\n测试完成，生成了 {len(test_images)} 张测试图像")
    print("如需生成30,000张图像，请调用 generate_30k_card_line_tables() 函数")
