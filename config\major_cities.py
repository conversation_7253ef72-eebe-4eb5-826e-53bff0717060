major_cities = {
    "display_name": "主要城市",
    "table_title_template": "中国主要城市表",
    "columns": {
        "序号": {
            "generator_type": "index",
            "data_category": "text"
        },
        "城市名称": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "北京",
                    "上海",
                    "广州",
                    "深圳",
                    "成都",
                    "重庆",
                    "武汉",
                    "杭州",
                    "南京",
                    "西安"
                ]
            },
            "data_category": "text"
        },
        "城市等级": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "一线城市",
                    "新一线城市",
                    "二线城市"
                ]
            },
            "data_category": "text"
        },
        "备注": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "无",
                    "重要",
                    "紧急",
                    "待跟进",
                    "已完成",
                    "已取消"
                ]
            },
            "data_category": "text"
        }
    },
    "text_columns_count": 2,
    "text_columns_names": [
        "城市名称",
        "备注"
    ],
    "numeric_columns_count": 1,
    "numeric_columns_names": [
        "城市等级"
    ],
    "date_columns_count": 0,
    "date_columns_names": [],
    "other_columns_count": 0,
    "other_columns_names": [],
    "all_columns": [
        "城市名称",
        "备注",
        "城市等级"
    ]
}

