real_estate = {
    "display_name": "房产信息",
    "table_title_template": "房产信息登记表",
    "columns": {
        "房产编号": {
            "generator_type": "alphanumeric_pattern",
            "data_category": "text",
            "params": {
                "patterns": [
                    "RE-####-##",
                    "H######",
                    "PROP####"
                ]
            }
        },
        "房产类型": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "住宅",
                    "商铺",
                    "写字楼",
                    "厂房",
                    "公寓",
                    "别墅",
                    "复式",
                    "平房",
                    "商住两用",
                    "酒店式公寓"
                ]
            }
        },
        "地址": {
            "generator_type": "faker_address",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "所在城市": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "北京",
                    "上海",
                    "广州",
                    "深圳",
                    "成都",
                    "武汉",
                    "杭州",
                    "南京",
                    "西安",
                    "重庆"
                ]
            }
        },
        "面积": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 20,
                "max_value": 500,
                "decimals": 2,
                "format_string": "{:.2f}㎡"
            }
        },
        "价格": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 500000,
                "max_value": 20000000,
                "format_string": "{:,}"
            }
        },
        "单价": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 5000,
                "max_value": 100000,
                "format_string": "{:,}/㎡"
            }
        },
        "卧室数量": {
            "generator_type": "integer_range",
            "data_category": "numeric",
            "params": {
                "min": 1,
                "max": 6
            }
        },
        "卫生间数量": {
            "generator_type": "integer_range",
            "data_category": "numeric",
            "params": {
                "min": 1,
                "max": 4
            }
        },
        "楼层": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "1层",
                    "2层",
                    "3层",
                    "4层",
                    "5层",
                    "6层",
                    "7层",
                    "8层",
                    "9层",
                    "10层",
                    "11-20层",
                    "20层以上"
                ]
            }
        },
        "总楼层": {
            "generator_type": "integer_range",
            "data_category": "numeric",
            "params": {
                "min": 1,
                "max": 60
            }
        },
        "建成年份": {
            "generator_type": "integer_range",
            "data_category": "numeric",
            "params": {
                "min": 1990,
                "max": 2024
            }
        },
        "装修情况": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "精装修",
                    "简装",
                    "毛坯",
                    "豪华装修",
                    "中等装修",
                    "清水房",
                    "老旧房",
                    "翻新装修"
                ]
            }
        },
        "朝向": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "南北通透",
                    "东西朝向",
                    "南向",
                    "北向",
                    "东向",
                    "西向",
                    "东南",
                    "西南",
                    "东北",
                    "西北"
                ]
            }
        },
        "小区名称": {
            "generator_type": "categorical_with_pattern",
            "data_category": "text",
            "params": {
                "prefixes": [
                    "阳光",
                    "金色",
                    "绿地",
                    "万科",
                    "保利",
                    "恒大",
                    "碧桂园",
                    "龙湖",
                    "华润",
                    "富力"
                ],
                "suffixes": [
                    "花园",
                    "小区",
                    "公馆",
                    "华府",
                    "苑",
                    "城",
                    "景苑",
                    "名苑",
                    "佳园",
                    "豪庭"
                ]
            }
        },
        "交通情况": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "近地铁",
                    "公交便利",
                    "地铁口500米",
                    "临主干道",
                    "公交站步行5分钟",
                    "交通便利",
                    "有轻轨",
                    "临高速路口",
                    "有机场大巴",
                    "靠近火车站"
                ]
            }
        },
        "配套设施": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "学校,医院,商场",
                    "公园,超市,健身房",
                    "商业街,幼儿园,游泳池",
                    "图书馆,影院,餐厅",
                    "会所,老年活动中心,运动场",
                    "社区医疗,停车场,儿童乐园"
                ]
            }
        },
        "房本情况": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "满二",
                    "满五",
                    "未满二",
                    "未满五",
                    "一手房",
                    "二手房",
                    "公房",
                    "商品房",
                    "军产房"
                ]
            }
        },
        "土地性质": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "住宅用地",
                    "商业用地",
                    "工业用地",
                    "综合用地",
                    "教育用地",
                    "医疗用地",
                    "农用地"
                ]
            }
        },
        "产权年限": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "70年",
                    "50年",
                    "40年",
                    "永久",
                    "40年以下",
                    "50-70年"
                ]
            }
        },
        "挂牌时间": {
            "generator_type": "date_range",
            "data_category": "date",
            "params": {
                "start_year": 2023,
                "end_year": 2024,
                "format": "%Y-%m-%d"
            }
        },
        "联系人": {
            "generator_type": "faker_name",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "联系电话": {
            "generator_type": "faker_phone_number",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "物业费": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 1,
                "max_value": 10,
                "decimals": 2,
                "format_string": "{:.2f}/㎡/月"
            }
        },
        "供暖方式": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "集中供暖",
                    "自采暖",
                    "煤气暖气",
                    "电暖气",
                    "空调制热",
                    "地暖",
                    "暖气片",
                    "太阳能",
                    "无供暖",
                    "燃气壁挂炉"
                ]
            }
        },
        "绿化率": {
            "generator_type": "percentage",
            "data_category": "numeric",
            "params": {
                "min_value": 10,
                "max_value": 70,
                "decimals": 0
            }
        },
        "车位情况": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "地下车位充足",
                    "地面车位",
                    "无固定车位",
                    "一户一车位",
                    "车位紧张",
                    "收费车位",
                    "专属车库",
                    "充电桩车位",
                    "机械车位",
                    "租赁车位"
                ]
            }
        },
        "开发商": {
            "generator_type": "faker_company",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "看房时间": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "随时可看",
                    "预约看房",
                    "周末可看",
                    "工作日可看",
                    "电话预约",
                    "每日9点-18点",
                    "每日全天",
                    "联系中介",
                    "业主陪同看房",
                    "暂不可看"
                ]
            }
        },
        "税费情况": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "买卖双方各付",
                    "买方承担",
                    "卖方承担",
                    "免税房",
                    "契税已缴",
                    "增值税需缴",
                    "个税需缴",
                    "税费待定",
                    "首套优惠",
                    "满五唯一"
                ]
            }
        },
        "户型结构": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "平层",
                    "错层",
                    "跃层",
                    "复式",
                    "开间",
                    "普通住宅",
                    "LOFT",
                    "花园洋房",
                    "联排别墅",
                    "独栋别墅"
                ]
            }
        },
        "电梯情况": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "有电梯",
                    "无电梯",
                    "双梯",
                    "一梯两户",
                    "一梯三户",
                    "一梯四户",
                    "电梯房",
                    "步梯房",
                    "货梯",
                    "观光电梯"
                ]
            }
        },
        "楼龄": {
            "generator_type": "integer_range_with_unit",
            "data_category": "numeric",
            "params": {
                "min": 1,
                "max": 50,
                "unit": "年"
            }
        },
        "装修费用": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 50000,
                "max_value": 500000,
                "format_string": "{:,}"
            }
        },
        "满意度评分": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 3.0,
                "max_value": 5.0,
                "decimals": 1,
                "format_string": "{:.1f}分"
            }
        }
    },
    "text_columns_count": 15,
    "text_columns_names": [
        "房产类型",
        "地址",
        "所在城市",
        "装修情况",
        "朝向",
        "配套设施",
        "房本情况",
        "土地性质",
        "联系人",
        "供暖方式",
        "车位情况",
        "开发商",
        "税费情况",
        "户型结构",
        "电梯情况"
    ],
    "numeric_columns_count": 14,
    "numeric_columns_names": [
        "房产编号",
        "面积",
        "价格",
        "单价",
        "卧室数量",
        "卫生间数量",
        "总楼层",
        "小区名称",
        "联系电话",
        "物业费",
        "绿化率",
        "楼龄",
        "装修费用",
        "满意度评分"
    ],
    "date_columns_count": 4,
    "date_columns_names": [
        "建成年份",
        "产权年限",
        "挂牌时间",
        "看房时间"
    ],
    "other_columns_count": 2,
    "other_columns_names": [
        "楼层",
        "交通情况"
    ],
    "all_columns": [
        "房产类型",
        "地址",
        "所在城市",
        "装修情况",
        "朝向",
        "配套设施",
        "房本情况",
        "土地性质",
        "联系人",
        "供暖方式",
        "车位情况",
        "开发商",
        "税费情况",
        "户型结构",
        "电梯情况",
        "房产编号",
        "面积",
        "价格",
        "单价",
        "卧室数量",
        "卫生间数量",
        "总楼层",
        "小区名称",
        "联系电话",
        "物业费",
        "绿化率",
        "楼龄",
        "装修费用",
        "满意度评分",
        "建成年份",
        "产权年限",
        "挂牌时间",
        "看房时间",
        "楼层",
        "交通情况"
    ]
}

