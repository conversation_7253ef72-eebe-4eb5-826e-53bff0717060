sports = {
    "display_name": "体育赛事",
    "table_title_template": "体育赛事统计表",
    "columns": {
        "比赛项目": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "足球",
                    "篮球",
                    "排球",
                    "网球",
                    "乒乓球",
                    "羽毛球",
                    "游泳",
                    "田径",
                    "体操",
                    "举重",
                    "射击",
                    "击剑",
                    "拳击",
                    "柔道",
                    "跆拳道",
                    "马拉松",
                    "棒球",
                    "高尔夫",
                    "冰球",
                    "滑雪",
                    "花样滑冰",
                    "短道速滑",
                    "橄榄球",
                    "美式足球",
                    "手球",
                    "曲棍球",
                    "水球",
                    "沙滩排球",
                    "保龄球",
                    "壁球",
                    "台球",
                    "斯诺克",
                    "藤球",
                    "垒球",
                    "板球",
                    "壁球",
                    "飞盘",
                    "极限运动",
                    "攀岩",
                    "滑板",
                    "冲浪",
                    "帆船",
                    "赛艇",
                    "皮划艇",
                    "独木舟",
                    "潜水",
                    "跳水",
                    "蹦床",
                    "艺术体操",
                    "健美操",
                    "舞蹈",
                    "武术",
                    "空手道",
                    "摔跤",
                    "散打",
                    "太极拳",
                    "自行车",
                    "公路自行车",
                    "山地自行车",
                    "BMX",
                    "铁人三项",
                    "现代五项",
                    "马术",
                    "赛马",
                    "滑翔",
                    "热气球",
                    "跳伞",
                    "蹦极",
                    "高山滑雪",
                    "越野滑雪",
                    "单板滑雪",
                    "速度滑冰",
                    "冰壶",
                    "钓鱼",
                    "飞镖",
                    "台球",
                    "象棋",
                    "国际象棋",
                    "围棋",
                    "桥牌",
                    "电子竞技",
                    "赛车",
                    "方程式赛车",
                    "拉力赛车",
                    "摩托车赛",
                    "卡丁车",
                    "水上摩托",
                    "滑水",
                    "水上飞机",
                    "动力伞",
                    "热气球",
                    "三项全能",
                    "铁人五项",
                    "室内划船",
                    "健美健身",
                    "野外定向",
                    "定向越野",
                    "户外探险",
                    "攀冰",
                    "山地车",
                    "障碍赛",
                    "越野跑",
                    "超级马拉松",
                    "障碍跑"
                ]
            },
            "data_category": "text"
        },
        "比赛日期": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2022,
                "end_year": 2024,
                "format": "%Y-%m-%d"
            },
            "data_category": "date"
        },
        "比赛地点": {
            "generator_type": "categorical_with_pattern",
            "params": {
                "prefixes": {
                    "type": "common_data",
                    "key": "major_cities"
                },
                "suffixes": [
                    "体育场",
                    "体育馆",
                    "奥体中心",
                    "奥林匹克体育中心",
                    "综合体育馆",
                    "体育中心"
                ]
            },
            "data_category": "text"
        },
        "参赛队伍": {
            "generator_type": "categorical_with_pattern",
            "params": {
                "prefixes": {
                    "type": "common_data",
                    "key": "major_cities"
                },
                "suffixes": [
                    "队",
                    "俱乐部",
                    "体育队",
                    "联队",
                    "勇士",
                    "猛虎",
                    "雄鹰",
                    "飞龙",
                    "火箭",
                    "骑士"
                ]
            },
            "data_category": "text"
        },
        "比分": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "1:0",
                    "2:1",
                    "3:2",
                    "2:0",
                    "3:0",
                    "1:1",
                    "2:2",
                    "3:3",
                    "4:3",
                    "5:4",
                    "0:0",
                    "1:2",
                    "0:3",
                    "2:3",
                    "3:4",
                    "4:5"
                ]
            },
            "data_category": "text"
        },
        "冠军": {
            "generator_type": "categorical_with_pattern",
            "params": {
                "prefixes": {
                    "type": "common_data",
                    "key": "major_cities"
                },
                "suffixes": [
                    "队",
                    "俱乐部",
                    "体育队",
                    "联队",
                    "勇士"
                ]
            },
            "data_category": "text"
        },
        "观众人数": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 1,
                "max_value": 90,
                "format_string": "{:d}万"
            },
            "data_category": "numeric"
        },
        "赛事级别": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "世界杯",
                    "奥运会",
                    "世锦赛",
                    "全国锦标赛",
                    "联赛",
                    "邀请赛",
                    "友谊赛",
                    "洲际杯",
                    "大满贯",
                    "大师赛",
                    "公开赛"
                ]
            },
            "data_category": "text"
        },
        "学历": {
            "generator_type": "categorical",
            "data_category": "text",
            "values": [
                "高中",
                "中专",
                "大专",
                "本科",
                "硕士",
                "博士",
                "MBA"
            ]
        },
        "备注": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "无",
                    "重要",
                    "紧急",
                    "待跟进",
                    "已完成",
                    "已取消"
                ]
            },
            "data_category": "text"
        },
        "状态": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "活跃",
                    "挂起",
                    "完成",
                    "取消",
                    "延期",
                    "进行中"
                ]
            },
            "data_category": "text"
        },
    "转播平台": {
        "generator_type": "categorical",
        "params": {
            "values": [
                "CCTV-5",
                "CCTV-5+",
                "北京卫视",
                "上海卫视",
                "广东体育",
                "五星体育",
                "腾讯体育",
                "爱奇艺体育",
                "咪咕视频",
                "优酷体育",
                "哔哩哔哩"
            ]
        },
        "data_category": "text"
    },
    "奖金": {
        "generator_type": "numerical_range_formatted",
        "params": {
            "min_value": 50,
            "max_value": 5000,
            "format_string": "{:,}万"
        },
        "data_category": "numeric"
    },
    },
    "text_columns_count": 7,
    "text_columns_names": [
        "学历",
        "参赛队伍",
        "状态",
        "转播平台",
        "比赛地点",
        "冠军",
        "赛事级别"
    ],
    "numeric_columns_count": 5,
    "numeric_columns_names": [
        "比赛地点",
        "参赛队伍",
        "冠军",
        "观众人数",
        "赛事级别"
    ],
    "date_columns_count": 2,
    "date_columns_names": [
        "比赛日期",
        "比分"
    ],
    "other_columns_count": 1,
    "other_columns_names": [
        "比赛项目"
    ],
    "all_columns": [
        "学历",
        "备注",
        "状态",
        "比赛地点",
        "参赛队伍",
        "冠军",
        "观众人数",
        "赛事级别",
        "比赛日期",
        "比分",
        "比赛项目"
    ]
}

