#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import json
import random
import copy
import time
import imgkit
import multiprocessing
from datetime import datetime
from functools import partial
import multiprocessing as mp
import re

# 导入现有功能
from cross_table_generator import (
    extract_categories_from_config,
    extract_metrics_from_config,
    create_random_cross_table_image,
    load_table_config,
    generate_dynamic_cross_table_config,
    create_variations,
    # generate_batch_cross_tables # This was the old single-process batch generator
)

# 导入分块表生成模块
from bsc_table_generator import (
    generate_bsc_tables_multiprocess,
    generate_blocked_table,
    select_random_themes,
    generate_bsc_content
)

# 导入json表格生成模块
from json_table_generator import (
    TABLE_TYPES,
    OUTPUT_FOLDER as JSON_OUTPUT_FOLDER,
    OUTPUT_THREE_LINE_FOLDER,
    OUTPUT_TWO_DIM_FOLDER,
    OUTPUT_NO_LINE_FOLDER,
    OUTPUT_ONE_DIM_FOLDER,
    OUTPUT_TEXT_TABLE_FOLDER,
    OUTPUT_DATA_TABLE_FOLDER,
    TableGenerator,
    generate_all_tables,
    generate_three_line_tables,
    generate_text_tables,
    generate_no_line_tables,
    generate_one_dim_tables,
    generate_two_dim_tables,
    generate_data_tables
)

# 常量
MAX_ROWS_LEVELS = 2  # 行维度层级数量
MAX_COLS_LEVELS = 3  # 列维度层级数量
MAX_ROWS_VALUES = 5  # 每个行维度最多的取值数量
MAX_COLS_VALUES = 4  # 每个列维度最多的取值数量
OUTPUT_FOLDER = os.path.join("表格", "交叉表")  # 输出目录
BATCH_SIZE = 1000  # 每批次生成的表格数量 - Note: This might be large for a single call to the new func
PROCESS_COUNT = max(1, mp.cpu_count() - 1)  # 使用CPU核心数-1的进程数
OUTPUT_FOLDER_BLOCKED = os.path.join("表格", "分块表") # 分块表输出目录

# def setup_logging(): # Logging was removed
#     pass

def worker_task(task_id, config, base_path, start_index):
    """工作进程任务 - 生成一组交叉表图片"""
    worker_start_time = time.time()
    results = {
        "task_id": task_id,
        "success_count": 0,
        "error_count": 0,
        "start_index": start_index
    }
    
    try:
        # No worker-specific file logging
        
        for i, (table_key, table_config) in enumerate(config["cross_tables"].items()):
            table_index = start_index + i
            
            # 创建文件名和路径 - 使用工作进程ID确保不同进程生成的文件名不会冲突
            # Ensure the main OUTPUT_FOLDER for cross-tables is used by worker_task
            # The base_path passed to worker_task should be OUTPUT_FOLDER for this function.
            file_name = f"交叉表_{task_id}_{table_index}_{table_key.split('_')[0]}.jpg"
            output_path = os.path.join(base_path, file_name) # base_path is OUTPUT_FOLDER here
            
            try:
                success = create_random_cross_table_image(table_config, output_path)
                if success:
                    results["success_count"] += 1
                else:
                    results["error_count"] += 1
                
                if results["success_count"] % 10 == 0: # Simple console print for worker progress
                    # print(f"Worker {task_id} progress: {results['success_count']}/{len(config['cross_tables'])} generated.")
                    pass # Keeping worker console quiet for now
            except Exception as e:
                results["error_count"] += 1
                print(f"Worker {task_id} error generating table {table_index}: {str(e)}")
            
    except Exception as e:
        print(f"Worker {task_id} critically failed: {str(e)}")
    
    return results

def split_configs_for_workers(all_configs_dict, worker_count):
    """将配置字典拆分为多个子集分配给不同工作进程"""
    tables_list = list(all_configs_dict.items())
    chunks = []
    total_tables_to_process = len(tables_list)
    
    if total_tables_to_process == 0:
        return chunks
        
    chunk_size = total_tables_to_process // worker_count
    if total_tables_to_process % worker_count > 0: # if there's a remainder
        chunk_size +=1

    if chunk_size == 0 and total_tables_to_process > 0 : # Ensure chunk_size is at least 1 if there are tables
        chunk_size = 1

    for i in range(0, total_tables_to_process, chunk_size):
        chunk_tables = tables_list[i:i + chunk_size]
        if not chunk_tables: # Should not happen with correct loop condition
            continue
        chunk_dict = {"cross_tables": dict(chunk_tables)}
        chunks.append(chunk_dict)
    
    return chunks

# New function to encapsulate multiprocessing cross-table generation
def generate_cross_tables_multiprocess(desired_table_count: int):
    """使用多进程并行生成指定数量的交叉表图片到 OUTPUT_FOLDER."""
    
    print(f"开始多进程生成 {desired_table_count} 张交叉表到 '{OUTPUT_FOLDER}'...")
    start_time = time.time()

    if not os.path.exists(OUTPUT_FOLDER):
        os.makedirs(OUTPUT_FOLDER)
        print(f"创建目录: {OUTPUT_FOLDER}")

    # 1. Load main table config
    main_config_data = load_table_config()
    if not main_config_data:
        print("错误: 无法加载主表格配置文件 (table_config.json)，无法生成交叉表。")
        return 0

    # 2. Extract categories and metrics
    categories = extract_categories_from_config(main_config_data)
    metrics = extract_metrics_from_config(main_config_data)
    if not categories:
        print("错误: 无法从主配置中提取足够的分类变量，无法生成交叉表。")
        return 0
    print(f"从主配置加载了 {len(categories)} 个分类变量和 {len(metrics)} 个度量变量。")

    # 3. Prepare configurations for cross-tables
    # Aim for slightly more configurations than desired_table_count to account for potential individual generation failures.
    # Using a fixed internal redundancy factor, e.g., 20% more.
    internal_redundancy_factor = 1.2 
    num_configs_to_prepare = int(desired_table_count * internal_redundancy_factor)
    if num_configs_to_prepare <= 0: num_configs_to_prepare = 1 # ensure at least one

    all_generated_configs_dict = {}
    attempts = 0
    max_attempts = 5 # Max attempts to gather enough unique configs

    while len(all_generated_configs_dict) < num_configs_to_prepare and attempts < max_attempts:
        # Generate a batch of base configurations
        base_configs_batch = generate_dynamic_cross_table_config(categories, metrics)
        if base_configs_batch and "cross_tables" in base_configs_batch:
            all_generated_configs_dict.update(base_configs_batch["cross_tables"])
            
            # Add variations for this batch
            variations_batch = create_variations(base_configs_batch) # create_variations expects a dict like {"cross_tables": {...}}
            if variations_batch and "cross_tables" in variations_batch:
                all_generated_configs_dict.update(variations_batch["cross_tables"])
        attempts += 1
        if attempts >= max_attempts and len(all_generated_configs_dict) < num_configs_to_prepare:
            print(f"警告: 尝试 {max_attempts} 次后，只生成了 {len(all_generated_configs_dict)}/{num_configs_to_prepare} 个配置。可能配置多样性不足。")
            if not all_generated_configs_dict: # if absolutely no configs generated
                 print("错误: 无法生成任何交叉表配置。请检查 `generate_dynamic_cross_table_config` 和 `create_variations`。")
                 return 0

    # Trim to the number of configs we actually want to process if we overshot significantly due to batching
    # Or if we have fewer than desired_table_count, we process what we have.
    actual_configs_to_process_list = list(all_generated_configs_dict.items())[:desired_table_count]
    final_configs_dict_for_processing = dict(actual_configs_to_process_list)
    
    if not final_configs_dict_for_processing:
        print("错误: 最终没有可处理的交叉表配置。")
        return 0
        
    num_final_configs = len(final_configs_dict_for_processing)
    print(f"准备了 {num_final_configs} 种唯一的交叉表配置进行处理。")

    # Save the specific configurations that will be processed (optional, good for debugging)
    # config_file_for_run = os.path.join(OUTPUT_FOLDER, f"cross_table_configs_run_{int(time.time())}.json")
    # with open(config_file_for_run, 'w', encoding='utf-8') as f:
    #     json.dump({"cross_tables": final_configs_dict_for_processing}, f, ensure_ascii=False, indent=2)
    # print(f"本次运行的配置已保存到 {config_file_for_run}")

    # 4. Setup multiprocessing pool and distribute tasks
    pool = mp.Pool(processes=PROCESS_COUNT)
    print(f"创建了 {PROCESS_COUNT} 个工作进程。")

    config_chunks = split_configs_for_workers(final_configs_dict_for_processing, PROCESS_COUNT)
    if not config_chunks:
        print("错误: 无法将配置拆分为工作块。")
        pool.close()
        pool.join()
        return 0
    print(f"将 {num_final_configs} 个配置拆分为 {len(config_chunks)} 个子任务。")

    tasks = []
    current_table_start_index = 1 # For unique naming
    for i, chunk_data in enumerate(config_chunks):
        # Pass OUTPUT_FOLDER as base_path for workers generating cross-tables
        task = pool.apply_async(worker_task, (i + 1, chunk_data, OUTPUT_FOLDER, current_table_start_index))
        tasks.append(task)
        current_table_start_index += len(chunk_data["cross_tables"])

    # 5. Collect results
    total_success_generated = 0
    total_errors_in_workers = 0

    print("所有进程已启动，正在生成交叉表...")
    for i, task in enumerate(tasks):
        try:
            result = task.get() # Wait for each task to complete
            if result:
                total_success_generated += result.get("success_count", 0)
                total_errors_in_workers += result.get("error_count", 0)
                # print(f"任务 {result.get('task_id', 'N/A')} 完成: 成功 {result.get('success_count',0)}, 失败 {result.get('error_count',0)}")
            else:
                print(f"任务 {i+1} 返回了空结果。")
                total_errors_in_workers += len(config_chunks[i]["cross_tables"]) # Assume all failed in this chunk
        except Exception as e:
            print(f"获取任务 {i+1} 结果时出错: {e}")
            # Assume all tables in this chunk failed if task.get() raises an error
            if i < len(config_chunks) and "cross_tables" in config_chunks[i]:
                 total_errors_in_workers += len(config_chunks[i]["cross_tables"]) 

    pool.close()
    pool.join()

    # 6. Final summary
    total_elapsed_seconds = time.time() - start_time
    hours, remainder = divmod(total_elapsed_seconds, 3600)
    minutes, seconds = divmod(remainder, 60)

    print("=" * 60)
    print(f"交叉表多进程生成完成!")
    print(f"目标生成数量: {desired_table_count}")
    print(f"实际成功生成: {total_success_generated} 张交叉表")
    print(f"生成过程中出现错误: {total_errors_in_workers} 次")
    print(f"总耗时: {int(hours)}小时 {int(minutes)}分钟 {seconds:.2f}秒")
    if total_success_generated > 0:
        print(f"平均每张成功表格耗时: {total_elapsed_seconds / total_success_generated:.4f} 秒")
    print("=" * 60)

    return total_success_generated

# 修改重命名函数，移除前导零
def rename_generated_files(source_folder, table_type, start_count, count):
    """
    重命名指定文件夹中的表格文件，使用全局连续编号
    
    Args:
        source_folder: 源文件夹路径
        table_type: 表格类型名称
        start_count: 起始计数
        count: 需要重命名的文件数量
        
    Returns:
        成功重命名的文件数量
    """
    print(f"开始重命名{table_type}文件...")
    
    # 获取文件夹中所有图片文件
    files = [f for f in os.listdir(source_folder) if f.endswith('.jpg') or f.endswith('.png')]
    
    # 根据文件创建时间排序
    files.sort(key=lambda x: os.path.getctime(os.path.join(source_folder, x)))
    
    # 只处理最新的count个文件
    files = files[-count:] if count < len(files) else files
    
    renamed_count = 0
    for i, file in enumerate(files):
        global_index = start_count + i
        # 构建新文件名: 仅使用序号，不包含表格类型
        new_filename = f"{global_index}.jpg"
        old_path = os.path.join(source_folder, file)
        new_path = os.path.join(source_folder, new_filename)
        
        try:
            os.rename(old_path, new_path)
            renamed_count += 1
        except Exception as e:
            print(f"重命名文件 {file} 时出错: {str(e)}")
    
    print(f"已重命名 {renamed_count} 个{table_type}文件")
    return renamed_count

# 修改基本表格生成函数，添加起始计数参数
def generate_basic_tables_multiprocess(table_type, desired_table_count, process_count=None):
    """
    使用多进程生成指定类型的基本表格
    
    Args:
        table_type: 表格类型，必须是TABLE_TYPES中的一种
        desired_table_count: 需要生成的表格数量
        process_count: 进程数量，默认为None时使用PROCESS_COUNT
        
    Returns:
        成功生成的表格数量
    """
    if process_count is None:
        process_count = PROCESS_COUNT
        
    # 确保表格类型有效
    if table_type not in TABLE_TYPES:
        print(f"错误: 表格类型 '{table_type}' 无效。有效类型为: {', '.join(TABLE_TYPES)}")
        return 0
    
    # 获取对应表格类型的输出目录
    output_folders = {
        "三线表": OUTPUT_THREE_LINE_FOLDER,
        "二维表": OUTPUT_TWO_DIM_FOLDER,
        "无线表": OUTPUT_NO_LINE_FOLDER,
        "一维表": OUTPUT_ONE_DIM_FOLDER,
        "文本表": OUTPUT_TEXT_TABLE_FOLDER,
        "数据表": OUTPUT_DATA_TABLE_FOLDER
    }
    output_folder = output_folders.get(table_type)
    
    # 确保输出目录存在
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        print(f"创建目录: {output_folder}")
    
    print(f"开始多进程生成 {desired_table_count} 张{table_type}到 '{output_folder}'...")
    start_time = time.time()
    
    # 每个进程生成的表格数量
    tables_per_process = desired_table_count // process_count
    remainder = desired_table_count % process_count
    
    # 创建进程池
    pool = mp.Pool(processes=process_count)
    print(f"创建了 {process_count} 个工作进程。")
    
    # 准备任务参数
    tasks = []
    for i in range(process_count):
        # 最后一个进程处理余数
        count = tables_per_process + (1 if i < remainder else 0)
        if count > 0:
            if table_type == "三线表":
                task = pool.apply_async(generate_three_line_tables, (count, min(count, 100)))
            elif table_type == "文本表":
                task = pool.apply_async(generate_text_tables, (count, min(count, 100)))
            elif table_type == "无线表":
                task = pool.apply_async(generate_no_line_tables, (count, min(count, 100)))
            elif table_type == "一维表":
                task = pool.apply_async(generate_one_dim_tables, (count, min(count, 100)))
            elif table_type == "二维表":
                task = pool.apply_async(generate_two_dim_tables, (count, min(count, 100)))
            elif table_type == "数据表":
                task = pool.apply_async(generate_data_tables, (count, min(count, 100)))
            tasks.append(task)
    
    # 收集结果
    total_generated = 0
    for task in tasks:
        try:
            result = task.get()
            if isinstance(result, list):
                total_generated += len(result)
            elif isinstance(result, int):
                total_generated += result
        except Exception as e:
            print(f"任务执行出错: {e}")
    
    # 关闭进程池
    pool.close()
    pool.join()
    
    # 输出统计信息
    total_elapsed_seconds = time.time() - start_time
    hours, remainder = divmod(total_elapsed_seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    print("=" * 60)
    print(f"{table_type}多进程生成完成!")
    print(f"目标生成数量: {desired_table_count}")
    print(f"实际成功生成: {total_generated} 张{table_type}")
    print(f"总耗时: {int(hours)}小时 {int(minutes)}分钟 {seconds:.2f}秒")
    if total_generated > 0:
        print(f"平均每张表格耗时: {total_elapsed_seconds / total_generated:.4f} 秒")
    print("=" * 60)
    
    return total_generated

# 主执行函数
if __name__ == "__main__":
    # 主执行块
    
    print("======= 批量生成所有八种表格多进程脚本 =======")
    start_total_time = time.time()
    
    # 1. 设置每种表格类型的生成数量（可以根据需要分别设置）
    tables_counts = {
        "文本表": 10,   # 生成50张文本表
        "三线表": 10,  # 生成100张三线表
        "二维表": 10,   # 生成80张二维表
        "一维表": 10,   # 生成70张一维表
        "数据表": 10,   # 生成90张数据表
        "无线表": 10,   # 生成60张无线表
        "交叉表": 10,  # 生成120张交叉表
        "分块表": 10   # 生成150张分块表
    }
    
    # 存储生成结果
    generation_results = {}
    global_counter = 1  # 全局文件编号计数器
    
    # 2. 生成6种基本表格类型（分别生成）
    for table_type in TABLE_TYPES:
        print(f"\n开始生成 {table_type}...")
        desired_count = tables_counts.get(table_type, 0)
        if desired_count <= 0:
            print(f"跳过生成 {table_type}，设置数量为 {desired_count}")
            generation_results[table_type] = 0
            continue
            
        try:
            # 生成表格
            count = generate_basic_tables_multiprocess(table_type, desired_count)
            generation_results[table_type] = count
            
            # 确定输出文件夹
            output_folders = {
                "三线表": OUTPUT_THREE_LINE_FOLDER,
                "二维表": OUTPUT_TWO_DIM_FOLDER,
                "无线表": OUTPUT_NO_LINE_FOLDER,
                "一维表": OUTPUT_ONE_DIM_FOLDER,
                "文本表": OUTPUT_TEXT_TABLE_FOLDER,
                "数据表": OUTPUT_DATA_TABLE_FOLDER
            }
            
            # 重命名文件
            if count > 0:
                rename_generated_files(output_folders[table_type], table_type, global_counter, count)
                global_counter += count
                
            print(f"已生成并重命名 {count} 张{table_type}")
        except Exception as e:
            print(f"生成{table_type}时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            generation_results[table_type] = 0
    
    # 3. 生成交叉表
    print("\n开始生成交叉表...")
    
    # 确保OUTPUT_FOLDER目录存在
    if not os.path.exists(OUTPUT_FOLDER):
        os.makedirs(OUTPUT_FOLDER)
        print(f"创建交叉表输出目录: {OUTPUT_FOLDER}")
    
    desired_cross_table_count = tables_counts.get("交叉表", 0)
    if desired_cross_table_count > 0:
        try:
            cross_tables_count = generate_cross_tables_multiprocess(desired_cross_table_count)
            generation_results["交叉表"] = cross_tables_count
            
            # 重命名交叉表文件
            if cross_tables_count > 0:
                rename_generated_files(OUTPUT_FOLDER, "交叉表", global_counter, cross_tables_count)
                global_counter += cross_tables_count
                
            print(f"已生成并重命名 {cross_tables_count} 张交叉表")
        except Exception as e:
            print(f"生成交叉表时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            generation_results["交叉表"] = 0
    else:
        print(f"跳过生成交叉表，设置数量为 {desired_cross_table_count}")
        generation_results["交叉表"] = 0
    
    # 4. 生成分块表
    print("\n开始生成分块表（平衡计分卡样式）...")
    
    # 确保分块表输出目录存在
    if not os.path.exists(OUTPUT_FOLDER_BLOCKED):
        os.makedirs(OUTPUT_FOLDER_BLOCKED)
        print(f"创建分块表输出目录: {OUTPUT_FOLDER_BLOCKED}")
    
    desired_bsc_table_count = tables_counts.get("分块表", 0)
    if desired_bsc_table_count > 0:
        # 设置进程数
        process_count = max(1, min(mp.cpu_count() - 1, 6))  # 使用合适的进程数
        
        print(f"准备使用 {process_count} 个进程生成 {desired_bsc_table_count} 张分块表...")
        
        try:
            start_time = time.time()
            bsc_tables_count = generate_bsc_tables_multiprocess(desired_bsc_table_count, process_count)
            bsc_total_time = time.time() - start_time
            generation_results["分块表"] = bsc_tables_count
            
            # 重命名分块表文件
            if bsc_tables_count > 0:
                rename_generated_files(OUTPUT_FOLDER_BLOCKED, "分块表", global_counter, bsc_tables_count)
                global_counter += bsc_tables_count
                
            print(f"已生成并重命名 {bsc_tables_count} 张分块表，总耗时: {bsc_total_time:.2f}秒")
        except Exception as e:
            print(f"生成分块表时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            generation_results["分块表"] = 0
    else:
        print(f"跳过生成分块表，设置数量为 {desired_bsc_table_count}")
        generation_results["分块表"] = 0
    
    # 总结
    total_tables = sum(generation_results.values())
    total_time = time.time() - start_total_time
    hours, remainder = divmod(total_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    print("\n" + "=" * 60)
    print("所有八种表格生成完成!")
    print("详细统计:")
    for table_type, count in generation_results.items():
        print(f"- {table_type}: {count} 张")
    print(f"总计: {total_tables} 张表格")
    print(f"总耗时: {int(hours)}小时 {int(minutes)}分钟 {seconds:.2f}秒")
    if total_tables > 0:
        print(f"平均每张表格耗时: {total_time/total_tables:.4f}秒")
    print("=" * 60)
    
    print("\n脚本执行完毕。")

