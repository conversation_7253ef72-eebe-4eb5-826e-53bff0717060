sports_equipment = {
    "display_name": "体育器材",
    "table_title_template": "体育器材数据表",
    "columns": {
        "器材名称": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "篮球",
                    "足球",
                    "排球",
                    "网球拍",
                    "羽毛球拍",
                    "乒乓球拍",
                    "哑铃",
                    "杠铃",
                    "跑步机",
                    "动感单车",
                    "椭圆机",
                    "划船机",
                    "健身球",
                    "瑜伽垫",
                    "拉力器",
                    "仰卧板",
                    "引体向上器",
                    "健腹轮",
                    "深蹲架",
                    "卧推凳",
                    "踏步机",
                    "跳绳",
                    "握力器",
                    "拳击手套",
                    "护具套装"
                ]
            },
            "data_category": "text"
        },
        "品牌": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "耐克",
                    "阿迪达斯",
                    "李宁",
                    "安踏",
                    "特步",
                    "匹克",
                    "361°",
                    "迪卡侬",
                    "斯伯丁",
                    "威尔胜",
                    "亚瑟士",
                    "美津浓",
                    "FILA",
                    "彪马",
                    "锐步",
                    "双鱼",
                    "红双喜",
                    "尤尼克斯",
                    "川崎",
                    "VICTOR",
                    "汤尤杯",
                    "乔丹",
                    "铁人三项"
                ]
            },
            "data_category": "text"
        },
        "型号": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "X###",
                    "Pro-##",
                    "MAX###",
                    "Air-#",
                    "Plus##",
                    "Neo-##",
                    "Y##X",
                    "Z##",
                    "TY-###"
                ]
            },
            "data_category": "text"
        },
        "价格": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 50,
                "max_value": 10000,
                "format_string": "{:,}"
            },
            "data_category": "numeric"
        },
        "材质": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "PU",
                    "PVC",
                    "橡胶",
                    "合成革",
                    "真皮",
                    "尼龙",
                    "碳纤维",
                    "铝合金",
                    "钢铁",
                    "木质",
                    "复合材料",
                    "ABS塑料",
                    "EVA",
                    "聚酯纤维",
                    "棉质",
                    "海绵",
                    "硅胶"
                ]
            },
            "data_category": "text"
        },
        "适用人群": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "专业运动员",
                    "业余爱好者",
                    "健身爱好者",
                    "初学者",
                    "青少年",
                    "成人",
                    "儿童",
                    "男性",
                    "女性",
                    "老年人",
                    "全民健身",
                    "家庭使用",
                    "商业场所",
                    "训练场地"
                ]
            },
            "data_category": "text"
        },
        "用途": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "比赛用",
                    "训练用",
                    "娱乐用",
                    "健身用",
                    "家庭健身",
                    "户外运动",
                    "室内运动",
                    "力量训练",
                    "有氧训练",
                    "柔韧性训练",
                    "平衡训练",
                    "协调性训练",
                    "速度训练",
                    "爆发力训练"
                ]
            },
            "data_category": "text"
        },
        "重量": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 0.1,
                "max_value": 200,
                "decimals": 1,
                "format_string": "{:.1f}kg"
            },
            "data_category": "numeric"
        },
        "尺寸": {
            "generator_type": "dimension_format",
            "params": {
                "min_length": 10,
                "max_length": 300,
                "min_width": 10,
                "max_width": 200,
                "min_height": 0,
                "max_height": 200,
                "format_string": "{}×{}×{}cm"
            },
            "data_category": "text"
        },
        "颜色": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "黑色",
                    "白色",
                    "红色",
                    "蓝色",
                    "绿色",
                    "黄色",
                    "橙色",
                    "紫色",
                    "粉色",
                    "灰色",
                    "银色",
                    "金色",
                    "渐变色",
                    "迷彩",
                    "多彩",
                    "荧光色",
                    "透明"
                ]
            },
            "data_category": "text"
        },
        "评分": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 3.0,
                "max_value": 5.0,
                "decimals": 1,
                "format_string": "{:.1f}"
            },
            "data_category": "numeric"
        }
    },
    "text_columns_count": 6,
    "text_columns_names": [
        "器材名称",
        "材质",
        "适用人群",
        "颜色",
        "品牌",
        "用途"
    ],
    "numeric_columns_count": 5,
    "numeric_columns_names": [
        "型号",
        "价格",
        "重量",
        "尺寸",
        "评分"
    ],
    "date_columns_count": 0,
    "date_columns_names": [],
    "other_columns_count": 2,
    "other_columns_names": [
        "品牌",
        "用途"
    ],
    "all_columns": [
        "器材名称",
        "材质",
        "适用人群",
        "颜色",
        "型号",
        "价格",
        "重量",
        "尺寸",
        "评分",
        "品牌",
        "用途"
    ]
}

