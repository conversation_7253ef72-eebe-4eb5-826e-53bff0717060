medical_records = {
    "display_name": "医疗病历",
    "table_title_template": "患者病历记录表",
    "columns": {
        "病历编号": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "MR########",
                    "EMR######",
                    "CASE######",
                    "HC########"
                ]
            },
            "data_category": "text"
        },
        "患者姓名": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "性别": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "男",
                    "女"
                ]
            },
            "data_category": "text"
        },
        "年龄": {
            "generator_type": "integer_range",
            "params": {
                "min": 0,
                "max": 100
            },
            "data_category": "numeric"
        },
        "就诊日期": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2022,
                "end_year": 2023,
                "format": "%Y-%m-%d"
            },
            "data_category": "date"
        },
        "科室": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "内科",
                    "外科",
                    "妇产科",
                    "儿科",
                    "眼科",
                    "耳鼻喉科",
                    "口腔科",
                    "皮肤科",
                    "神经内科",
                    "神经外科",
                    "心血管内科",
                    "呼吸内科",
                    "消化内科",
                    "泌尿外科",
                    "骨科",
                    "肿瘤科",
                    "精神科",
                    "急诊科",
                    "重症医学科",
                    "康复科"
                ]
            },
            "data_category": "text"
        },
        "主诊医师": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "主诉": {
            "generator_type": "categorical_with_pattern",
            "params": {
                "prefixes": [
                    "反复",
                    "持续",
                    "突发",
                    "间断",
                    "偶发",
                    "慢性",
                    "急性",
                    "逐渐加重的"
                ],
                "suffixes": [
                    "头痛",
                    "胸痛",
                    "腹痛",
                    "咳嗽",
                    "发热",
                    "恶心呕吐",
                    "乏力",
                    "头晕",
                    "腹泻",
                    "便秘",
                    "背痛",
                    "关节痛",
                    "视力模糊",
                    "听力下降",
                    "皮疹",
                    "失眠"
                ]
            },
            "data_category": "text"
        },
        "现病史": {
            "generator_type": "categorical_with_pattern",
            "params": {
                "prefixes": [
                    "患者",
                    "病人"
                ],
                "suffixes": [
                    "近3日出现症状，无明显诱因",
                    "1周前开始出现不适，逐渐加重",
                    "半月前出现症状，曾自行服药未见好转",
                    "2天前突然发病，伴有明显症状",
                    "反复出现类似症状已有3个月",
                    "长期患有基础疾病，症状时轻时重",
                    "近期工作压力大，症状明显加重",
                    "无明显诱因突然发病，伴有全身不适"
                ]
            },
            "data_category": "text"
        },
        "既往史": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "否认特殊疾病史",
                    "高血压病史5年",
                    "糖尿病病史3年",
                    "冠心病病史",
                    "肝炎病史",
                    "肺结核病史",
                    "手术史：阑尾切除",
                    "外伤史：车祸致骨折",
                    "过敏史：青霉素",
                    "精神疾病史",
                    "家族性疾病史",
                    "无特殊",
                    "否认传染病史",
                    "慢性胃炎病史"
                ]
            },
            "data_category": "text"
        },
        "体格检查": {
            "generator_type": "categorical_with_pattern",
            "params": {
                "prefixes": [
                    "BP: ",
                    "T: ",
                    "P: ",
                    "R: ",
                    "身高: ",
                    "体重: "
                ],
                "suffixes": [
                    "120/80mmHg",
                    "36.5℃",
                    "75次/分",
                    "18次/分",
                    "170cm",
                    "65kg",
                    "神志清楚",
                    "精神可",
                    "面色苍白",
                    "呼吸平稳",
                    "心律齐",
                    "腹软",
                    "无殊"
                ]
            },
            "data_category": "text"
        },
        "辅助检查": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "血常规：正常",
                    "血常规：WBC升高",
                    "尿常规：正常",
                    "肝功能：正常",
                    "肾功能：正常",
                    "心电图：窦性心律",
                    "心电图：ST-T改变",
                    "X线胸片：正常",
                    "CT：未见明显异常",
                    "MRI：正常",
                    "超声：未见异常",
                    "超声：肝脏轻度脂肪浸润",
                    "血糖：正常",
                    "血脂：TC升高",
                    "电解质：正常",
                    "凝血功能：正常"
                ]
            },
            "data_category": "text"
        },
        "初步诊断": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "上呼吸道感染",
                    "肺炎",
                    "急性胃肠炎",
                    "冠心病",
                    "高血压",
                    "2型糖尿病",
                    "慢性胃炎",
                    "消化性溃疡",
                    "胆囊炎",
                    "尿路感染",
                    "肾结石",
                    "颈椎病",
                    "腰椎间盘突出",
                    "骨质疏松",
                    "关节炎",
                    "偏头痛",
                    "焦虑障碍",
                    "抑郁症",
                    "失眠症",
                    "过敏性皮炎",
                    "荨麻疹",
                    "结膜炎",
                    "扁桃体炎"
                ]
            },
            "data_category": "text"
        },
        "治疗方案": {
            "generator_type": "categorical_with_pattern",
            "params": {
                "prefixes": [
                    "口服",
                    "静脉滴注",
                    "肌肉注射",
                    "皮下注射",
                    "局部用药",
                    "物理治疗",
                    "手术治疗",
                    "综合治疗"
                ],
                "suffixes": [
                    "抗生素",
                    "解热镇痛药",
                    "抗病毒药",
                    "胃肠道药物",
                    "心血管药物",
                    "降压药",
                    "降糖药",
                    "镇静催眠药",
                    "止咳祛痰药",
                    "抗过敏药",
                    "激素类药物",
                    "维生素补充",
                    "中药治疗"
                ]
            },
            "data_category": "text"
        },
        "医嘱": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "规律服药，注意休息",
                    "清淡饮食，多饮水",
                    "定期复诊，观察病情变化",
                    "遵医嘱用药，禁忌辛辣刺激食物",
                    "加强锻炼，控制体重",
                    "戒烟限酒，规律作息",
                    "避免剧烈运动，保持心情舒畅",
                    "遵医嘱用药，必要时复查",
                    "注意保暖，避免着凉",
                    "患病期间避免接触过敏原",
                    "严格控制饮食，监测血糖",
                    "控制血压，低盐饮食",
                    "做好个人防护，避免交叉感染"
                ]
            },
            "data_category": "text"
        },
        "下次复诊日期": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2023,
                "end_year": 2024,
                "format": "%Y-%m-%d"
            },
            "data_category": "date"
        }
    },
    "text_columns_count": 11,
    "text_columns_names": [
        "患者姓名",
        "性别",
        "科室",
        "主诊医师",
        "医嘱",
        "主诉",
        "现病史",
        "既往史",
        "体格检查",
        "辅助检查",
        "初步诊断"
    ],
    "numeric_columns_count": 5,
    "numeric_columns_names": [
        "病历编号",
        "主诉",
        "现病史",
        "体格检查",
        "治疗方案"
    ],
    "date_columns_count": 3,
    "date_columns_names": [
        "年龄",
        "就诊日期",
        "下次复诊日期"
    ],
    "other_columns_count": 3,
    "other_columns_names": [
        "既往史",
        "辅助检查",
        "初步诊断"
    ],
    "all_columns": [
        "患者姓名",
        "性别",
        "科室",
        "主诊医师",
        "医嘱",
        "病历编号",
        "主诉",
        "现病史",
        "体格检查",
        "治疗方案",
        "年龄",
        "就诊日期",
        "下次复诊日期",
        "既往史",
        "辅助检查",
        "初步诊断"
    ]
}

