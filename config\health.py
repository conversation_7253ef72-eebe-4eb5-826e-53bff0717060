health = {
    "display_name": "健康档案",
    "table_title_template": "医疗健康记录表",
    "columns": {
        "患者姓名": {
            "generator_type": "faker_name",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "患者ID": {
            "generator_type": "alphanumeric_pattern",
            "data_category": "text",
            "params": {
                "patterns": [
                    "P######",
                    "MR-####-##",
                    "HID######"
                ]
            }
        },
        "性别": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "男",
                    "女"
                ]
            }
        },
        "年龄": {
            "generator_type": "integer_range",
            "data_category": "numeric",
            "params": {
                "min": 1,
                "max": 100
            }
        },
        "出生日期": {
            "generator_type": "date_range",
            "data_category": "date",
            "params": {
                "start_year": 1950,
                "end_year": 2023,
                "format": "%Y-%m-%d"
            }
        },
        "血型": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "A型",
                    "B型",
                    "AB型",
                    "O型",
                    "A型Rh阴性",
                    "B型Rh阴性",
                    "AB型Rh阴性",
                    "O型Rh阴性"
                ]
            }
        },
        "身高": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 50,
                "max_value": 200,
                "decimals": 1,
                "format_string": "{:.1f}cm"
            }
        },
        "体重": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 5,
                "max_value": 150,
                "decimals": 1,
                "format_string": "{:.1f}kg"
            }
        },
        "BMI指数": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 15,
                "max_value": 40,
                "decimals": 1,
                "format_string": "{:.1f}"
            }
        },
        "血压": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "120/80",
                    "110/70",
                    "130/85",
                    "140/90",
                    "150/95",
                    "160/100",
                    "115/75",
                    "125/82"
                ]
            }
        },
        "脉搏": {
            "generator_type": "integer_range_with_unit",
            "data_category": "numeric",
            "params": {
                "min": 50,
                "max": 120,
                "unit": "次/分"
            }
        },
        "体温": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 35.5,
                "max_value": 39.5,
                "decimals": 1,
                "format_string": "{:.1f}°C"
            }
        },
        "就诊日期": {
            "generator_type": "date_range",
            "data_category": "date",
            "params": {
                "start_year": 2023,
                "end_year": 2024,
                "format": "%Y-%m-%d"
            }
        },
        "科室": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "内科",
                    "外科",
                    "妇产科",
                    "儿科",
                    "骨科",
                    "眼科",
                    "耳鼻喉科",
                    "皮肤科",
                    "神经科",
                    "心脏科",
                    "呼吸科",
                    "泌尿科"
                ]
            }
        },
        "主诊医师": {
            "generator_type": "faker_name",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "主诉": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "发热",
                    "头痛",
                    "咳嗽",
                    "腹痛",
                    "胸闷",
                    "乏力",
                    "恶心",
                    "呕吐",
                    "关节疼痛",
                    "腰痛",
                    "皮疹",
                    "眩晕",
                    "视力模糊"
                ]
            }
        },
        "诊断结果": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "普通感冒",
                    "流感",
                    "胃炎",
                    "结膜炎",
                    "高血压",
                    "糖尿病",
                    "贫血",
                    "支气管炎",
                    "肠胃炎",
                    "腰肌劳损",
                    "荨麻疹",
                    "过敏性鼻炎"
                ]
            }
        },
        "用药建议": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "抗生素",
                    "消炎药",
                    "退烧药",
                    "止痛药",
                    "抗过敏药",
                    "胃药",
                    "降压药",
                    "降糖药",
                    "维生素",
                    "营养补充剂",
                    "口服液",
                    "外用药膏"
                ]
            }
        },
        "治疗方案": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "口服药物治疗",
                    "物理治疗",
                    "心理治疗",
                    "手术治疗",
                    "针灸治疗",
                    "推拿按摩",
                    "饮食调理",
                    "日常保健",
                    "中医治疗",
                    "观察随访"
                ]
            }
        },
        "过敏史": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "无",
                    "青霉素过敏",
                    "海鲜过敏",
                    "花粉过敏",
                    "尘螨过敏",
                    "对某些食物过敏",
                    "药物过敏",
                    "多种过敏"
                ]
            }
        },
        "家族病史": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "无",
                    "高血压",
                    "糖尿病",
                    "心脏病",
                    "癌症",
                    "肝病",
                    "肾病",
                    "哮喘",
                    "精神疾病",
                    "自身免疫疾病"
                ]
            }
        },
        "复诊时间": {
            "generator_type": "date_range",
            "data_category": "date",
            "params": {
                "start_year": 2023,
                "end_year": 2024,
                "format": "%Y-%m-%d"
            }
        },
        "医疗保险类型": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "社会医疗保险",
                    "商业医疗保险",
                    "城镇职工医保",
                    "城乡居民医保",
                    "公费医疗",
                    "新农合",
                    "无保险",
                    "国际医疗保险",
                    "企业补充医疗保险",
                    "医疗救助"
                ]
            }
        },
        "就诊医院": {
            "generator_type": "categorical_with_pattern",
            "data_category": "text",
            "params": {
                "prefixes": [
                    "北京",
                    "上海",
                    "广州",
                    "深圳",
                    "武汉",
                    "成都",
                    "南京",
                    "西安",
                    "杭州",
                    "重庆"
                ],
                "suffixes": [
                    "协和医院",
                    "人民医院",
                    "第一医院",
                    "中医院",
                    "妇幼保健院",
                    "儿童医院",
                    "肿瘤医院",
                    "口腔医院",
                    "中西医结合医院",
                    "三甲医院"
                ]
            }
        },
        "住院状态": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "未住院",
                    "待住院",
                    "住院中",
                    "已出院",
                    "转院",
                    "日间住院",
                    "ICU",
                    "康复中心",
                    "留观",
                    "家庭病床"
                ]
            }
        },
        "住院天数": {
            "generator_type": "integer_range_with_unit",
            "data_category": "numeric",
            "params": {
                "min": 1,
                "max": 30,
                "unit": "天"
            }
        },
        "医疗费用": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 100,
                "max_value": 50000,
                "decimals": 2,
                "format_string": "{:.2f}"
            }
        },
        "检查项目": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "血常规",
                    "尿常规",
                    "CT扫描",
                    "X光",
                    "B超",
                    "心电图",
                    "磁共振",
                    "骨密度检查",
                    "肝功能",
                    "肾功能",
                    "血脂检查",
                    "血糖检查",
                    "内窥镜检查"
                ]
            }
        },
        "检查结果": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "正常",
                    "轻度异常",
                    "中度异常",
                    "重度异常",
                    "需进一步检查",
                    "疑似阳性",
                    "阳性",
                    "阴性",
                    "边缘性异常",
                    "待复查"
                ]
            }
        },
        "营养状况": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "良好",
                    "一般",
                    "较差",
                    "营养不良",
                    "营养过剩",
                    "蛋白质缺乏",
                    "维生素缺乏",
                    "贫血",
                    "肥胖",
                    "消瘦"
                ]
            }
        },
        "心理状态": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "正常",
                    "焦虑",
                    "抑郁",
                    "紧张",
                    "恐惧",
                    "情绪波动",
                    "适应良好",
                    "应激状态",
                    "心理压力大",
                    "情绪稳定"
                ]
            }
        },
        "康复计划": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "物理治疗",
                    "职业治疗",
                    "言语治疗",
                    "心理康复",
                    "营养干预",
                    "运动康复",
                    "家庭康复",
                    "社区康复",
                    "综合康复",
                    "长期随访"
                ]
            }
        },
        "随访日期": {
            "generator_type": "date_range",
            "data_category": "date",
            "params": {
                "start_year": 2024,
                "end_year": 2025,
                "format": "%Y-%m-%d"
            }
        },
        "检查周期": {
            "generator_type": "integer_range_with_unit",
            "data_category": "numeric",
            "params": {
                "min": 1,
                "max": 12,
                "unit": "月"
            }
        }
    },
    "text_columns_count": 16,
    "text_columns_names": [
        "患者姓名",
        "性别",
        "血型",
        "科室",
        "主诊医师",
        "主诉",
        "诊断结果",
        "用药建议",
        "治疗方案",
        "过敏史",
        "家族病史",
        "医疗保险类型",
        "住院状态",
        "营养状况",
        "心理状态",
        "康复计划"
    ],
    "numeric_columns_count": 9,
    "numeric_columns_names": [
        "患者ID",
        "身高",
        "体重",
        "BMI指数",
        "脉搏",
        "体温",
        "就诊医院",
        "医疗费用",
        "检查周期"
    ],
    "date_columns_count": 6,
    "date_columns_names": [
        "年龄",
        "出生日期",
        "就诊日期",
        "复诊时间",
        "住院天数",
        "随访日期"
    ],
    "other_columns_count": 3,
    "other_columns_names": [
        "血压",
        "检查项目",
        "检查结果"
    ],
    "all_columns": [
        "患者姓名",
        "性别",
        "血型",
        "科室",
        "主诊医师",
        "主诉",
        "诊断结果",
        "用药建议",
        "治疗方案",
        "过敏史",
        "家族病史",
        "医疗保险类型",
        "住院状态",
        "营养状况",
        "心理状态",
        "康复计划",
        "患者ID",
        "身高",
        "体重",
        "BMI指数",
        "脉搏",
        "体温",
        "就诊医院",
        "医疗费用",
        "检查周期",
        "年龄",
        "出生日期",
        "就诊日期",
        "复诊时间",
        "住院天数",
        "随访日期",
        "血压",
        "检查项目",
        "检查结果"
    ]
}

