order_records = {
    "display_name": "订单记录",
    "table_title_template": "订单记录数据表",
    "columns": {
        "订单编号": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "ORD######",
                    "OD########",
                    "TX########",
                    "ORDER-####"
                ]
            },
            "data_category": "text"
        },
        "用户ID": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "UID####",
                    "U######",
                    "USER_###"
                ]
            },
            "data_category": "text"
        },
        "用户名": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "下单时间": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2023,
                "end_year": 2023,
                "format": "%Y-%m-%d %H:%M:%S"
            },
            "data_category": "date"
        },
        "支付时间": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2023,
                "end_year": 2023,
                "format": "%Y-%m-%d %H:%M:%S"
            },
            "data_category": "date"
        },
        "订单状态": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "待支付",
                    "已支付",
                    "已发货",
                    "已签收",
                    "已完成",
                    "已取消",
                    "已退款",
                    "部分退款",
                    "退款中",
                    "交易关闭",
                    "已过期",
                    "待评价"
                ]
            },
            "data_category": "text"
        },
        "支付方式": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "支付宝",
                    "微信支付",
                    "银联",
                    "信用卡",
                    "储蓄卡",
                    "花呗",
                    "白条",
                    "货到付款",
                    "余额支付",
                    "积分兑换",
                    "优惠券",
                    "组合支付"
                ]
            },
            "data_category": "text"
        },
        "订单金额": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 9.9,
                "max_value": 9999,
                "decimals": 2,
                "format_string": "{:.2f}"
            },
            "data_category": "numeric"
        },
        "优惠金额": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 0,
                "max_value": 100,
                "decimals": 2,
                "format_string": "{:.2f}"
            },
            "data_category": "numeric"
        },
        "实付金额": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 9.9,
                "max_value": 9999,
                "decimals": 2,
                "format_string": "{:.2f}"
            },
            "data_category": "numeric"
        },
        "商品数量": {
            "generator_type": "integer_range",
            "params": {
                "min": 1,
                "max": 20
            },
            "data_category": "numeric"
        },
        "配送方式": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "快递配送",
                    "到店自提",
                    "同城配送",
                    "跨境物流",
                    "顺丰速运",
                    "京东物流",
                    "邮政EMS",
                    "圆通速递",
                    "中通快递",
                    "申通快递"
                ]
            },
            "data_category": "text"
        },
        "收货地址": {
            "generator_type": "faker_address",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        }
    },
    "text_columns_count": 5,
    "text_columns_names": [
        "用户名",
        "订单状态",
        "支付方式",
        "配送方式",
        "收货地址"
    ],
    "numeric_columns_count": 6,
    "numeric_columns_names": [
        "订单编号",
        "用户ID",
        "订单金额",
        "优惠金额",
        "实付金额",
        "商品数量"
    ],
    "date_columns_count": 3,
    "date_columns_names": [
        "下单时间",
        "支付时间",
        "收货地址"
    ],
    "other_columns_count": 0,
    "other_columns_names": [],
    "all_columns": [
        "用户名",
        "订单状态",
        "支付方式",
        "配送方式",
        "订单编号",
        "用户ID",
        "订单金额",
        "优惠金额",
        "实付金额",
        "商品数量",
        "下单时间",
        "支付时间",
        "收货地址"
    ]
}

