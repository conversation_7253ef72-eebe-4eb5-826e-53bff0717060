vehicle_repair = {
    "display_name": "车辆维修",
    "table_title_template": "车辆维修记录表",
    "columns": {
        "维修单号": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "REP########",
                    "VR########",
                    "CW########",
                    "WX########"
                ]
            },
            "data_category": "text"
        },
        "车牌号": {
            "generator_type": "categorical_with_pattern",
            "params": {
                "prefixes": [
                    "京",
                    "沪",
                    "粤",
                    "津",
                    "冀",
                    "晋",
                    "蒙",
                    "辽",
                    "吉",
                    "黑",
                    "苏",
                    "浙",
                    "皖",
                    "闽",
                    "赣",
                    "鲁",
                    "豫",
                    "鄂",
                    "湘",
                    "粤",
                    "桂",
                    "琼",
                    "渝",
                    "川",
                    "贵",
                    "云",
                    "藏",
                    "陕",
                    "甘",
                    "青",
                    "宁",
                    "新"
                ],
                "suffixes": [
                    "A#####",
                    "B#####",
                    "C#####",
                    "D#####",
                    "E#####",
                    "F#####",
                    "G#####",
                    "H#####",
                    "J#####"
                ]
            },
            "data_category": "text"
        },
        "车型": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "大众帕萨特",
                    "丰田卡罗拉",
                    "本田雅阁",
                    "奥迪A4L",
                    "奔驰E级",
                    "宝马3系",
                    "福特福克斯",
                    "雪佛兰科鲁兹",
                    "日产天籁",
                    "别克君越",
                    "雷克萨斯ES",
                    "凯迪拉克CTS",
                    "沃尔沃S60",
                    "马自达6",
                    "起亚K5",
                    "现代索纳塔",
                    "吉利博瑞",
                    "奇瑞艾瑞泽5",
                    "长安CS75",
                    "哈弗H6",
                    "吉利帝豪",
                    "宝骏510",
                    "比亚迪秦",
                    "荣威i5",
                    "上汽大众途观L",
                    "雪铁龙C5",
                    "标致508",
                    "讴歌TLX",
                    "林肯MKZ",
                    "英菲尼迪Q50",
                    "奥迪Q5",
                    "奔驰GLC",
                    "宝马X5",
                    "福特锐界",
                    "丰田汉兰达",
                    "本田CR-V",
                    "日产X-Trail",
                    "吉普自由光",
                    "路虎发现神行",
                    "沃尔沃XC60",
                    "起亚智跑",
                    "现代途胜",
                    "雪佛兰探界者",
                    "雪铁龙C3-XR",
                    "长安CS35",
                    "哈弗H2",
                    "奇瑞瑞虎5",
                    "奇瑞瑞虎8",
                    "比亚迪宋",
                    "荣威RX5",
                    "传祺GS4",
                    "东风风光580",
                    "宝骏730",
                    "江淮瑞风M4",
                    "上汽大众途安",
                    "五菱宏光",
                    "奇瑞Tiggo 2",
                    "广汽传祺GA6",
                    "雪佛兰迈锐宝XL",
                    "一汽奔腾B70",
                    "北京现代悦动",
                    "长安逸动",
                    "东风日产轩逸",
                    "上汽名爵6",
                    "广汽传祺GA8",
                    "丰田雷凌",
                    "别克英朗",
                    "本田飞度",
                    "标致308",
                    "雪铁龙C4L",
                    "福特嘉年华",
                    "日产逍客",
                    "雪佛兰赛欧",
                    "起亚K2",
                    "大众高尔夫",
                    "本田思域",
                    "丰田RAV4",
                    "奥迪A6L",
                    "宝马5系",
                    "奔驰C级",
                    "雷克萨斯RX",
                    "凯迪拉克XT5",
                    "英菲尼迪QX50",
                    "沃尔沃XC90",
                    "路虎揽胜极光",
                    "上汽大众帕萨特",
                    "吉利远景",
                    "奇瑞风云2",
                    "江淮瑞风S5",
                    "上汽大通MAXUS V80",
                    "丰田兰德酷路泽",
                    "本田Pilot",
                    "日产途乐",
                    "福特猛禽",
                    "雪佛兰Tahoe",
                    "雪铁龙C6",
                    "比亚迪唐",
                    "哈弗H9",
                    "奇瑞虎8",
                    "沃尔沃XC40"
                ]
            },
            "data_category": "text"
        },
        "车辆品牌": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "大众",
                    "丰田",
                    "本田",
                    "别克",
                    "奥迪",
                    "宝马",
                    "奔驰",
                    "日产",
                    "福特",
                    "雪佛兰",
                    "现代",
                    "起亚",
                    "吉利",
                    "奇瑞",
                    "长安",
                    "哈弗",
                    "比亚迪",
                    "荣威",
                    "标致",
                    "雪铁龙",
                    "林肯",
                    "路虎",
                    "沃尔沃",
                    "凯迪拉克",
                    "英菲尼迪",
                    "斯巴鲁",
                    "传祺",
                    "广汽",
                    "奇骏",
                    "Jeep",
                    "领克",
                    "爱驰",
                    "蔚来",
                    "小鹏",
                    "理想",
                    "上汽",
                    "一汽",
                    "东风",
                    "长城",
                    "东风风光",
                    "江淮",
                    "宝骏",
                    "比速",
                    "红旗",
                    "腾势",
                    "广汽传祺",
                    "欧拉",
                    "沃尔沃",
                    "路虎发现",
                    "别克昂科威",
                    "丰田皇冠",
                    "本田雅阁",
                    "雪佛兰科鲁兹",
                    "现代索纳塔",
                    "起亚K5",
                    "吉利博瑞",
                    "奇瑞艾瑞泽",
                    "长安逸动",
                    "哈弗H6",
                    "比亚迪宋",
                    "荣威RX5",
                    "宝骏510",
                    "广汽传祺GS4",
                    "长城哈弗",
                    "长安CS35",
                    "雪铁龙C3",
                    "丰田汉兰达",
                    "大众途观",
                    "本田CR-V",
                    "奔驰GLC",
                    "奥迪Q5",
                    "沃尔沃XC60",
                    "吉普自由光",
                    "宝马X5",
                    "雷克萨斯RX",
                    "凯迪拉克XT5",
                    "起亚智跑",
                    "奇瑞瑞虎5",
                    "吉利远景",
                    "奇瑞虎8",
                    "福特锐界",
                    "广汽传祺GA6",
                    "日产天籁",
                    "雪佛兰赛欧",
                    "丰田雷凌",
                    "一汽奔腾",
                    "本田飞度",
                    "标致308",
                    "雪铁龙C4L",
                    "上汽名爵",
                    "吉利帝豪",
                    "别克英朗",
                    "东风日产轩逸",
                    "长安CS75",
                    "比亚迪秦",
                    "长安致尚XT",
                    "现代途胜",
                    "雪佛兰迈锐宝",
                    "丰田RAV4",
                    "奥迪A6L",
                    "宝马3系",
                    "奔驰E级",
                    "凯迪拉克CTS",
                    "雪铁龙C5",
                    "英菲尼迪QX50"
                ]
            },
            "data_category": "text"
        },
        "车主姓名": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "联系电话": {
            "generator_type": "faker_phone_number",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "维修类型": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "常规保养",
                    "故障维修",
                    "事故维修",
                    "保险理赔",
                    "召回维修",
                    "质保维修",
                    "钣金喷漆",
                    "轮胎更换",
                    "电池更换",
                    "空调维修",
                    "发动机维修",
                    "变速箱维修",
                    "刹车系统",
                    "悬挂系统",
                    "电气系统",
                    "内饰维修",
                    "改装升级"
                ]
            },
            "data_category": "text"
        },
        "维修项目": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "机油更换",
                    "机滤更换",
                    "空滤更换",
                    "汽滤更换",
                    "刹车片更换",
                    "刹车油更换",
                    "火花塞更换",
                    "变速箱油更换",
                    "发动机清洗",
                    "蓄电池更换",
                    "轮胎更换",
                    "轮胎修补",
                    "冷却液更换",
                    "刹车系统检查",
                    "转向系统检查",
                    "空调系统清洁",
                    "空调滤芯更换",
                    "发动机皮带检查",
                    "发动机皮带更换",
                    "防冻液更换",
                    "燃油系统清洁",
                    "喷油嘴清洗",
                    "燃油滤芯更换",
                    "车身底盘检查",
                    "车灯检查与更换",
                    "刮水器更换",
                    "车内消毒",
                    "车漆修复",
                    "车窗玻璃修复",
                    "变速箱修理",
                    "发动机修理",
                    "排气系统检查",
                    "排气管修复",
                    "轮毂修复",
                    "传动轴检查",
                    "动力系统检查",
                    "悬挂系统检查",
                    "车门检查",
                    "车窗升降系统修复",
                    "冷气管道检查",
                    "天窗检查",
                    "车内电气系统检查",
                    "电瓶测试",
                    "电池充电",
                    "车载电脑故障诊断",
                    "ABS系统检查",
                    "ESP系统检查",
                    "发动机气门调整",
                    "发动机压缩测试",
                    "离合器检查",
                    "离合器更换",
                    "转向助力液更换",
                    "空调加氟",
                    "汽车外观清洗",
                    "轮胎动平衡调整",
                    "车内空气净化",
                    "车体防锈处理",
                    "车身抛光打蜡",
                    "座椅修复",
                    "内饰清洁",
                    "水箱修复",
                    "发动机更换",
                    "电动窗维修",
                    "方向盘修复",
                    "车载音响维修",
                    "油门踏板更换",
                    "刹车踏板检查",
                    "发动机机脚胶检查",
                    "空调系统更换",
                    "水泵检查",
                    "雨刮器电机更换",
                    "车灯总成更换",
                    "车内灯光更换",
                    "车窗密封条更换",
                    "车载充电器检查",
                    "车顶行李架安装",
                    "挡风玻璃修复",
                    "油管检查",
                    "防盗系统检查",
                    "钥匙遥控器更换",
                    "车内配件更换",
                    "燃油泵更换",
                    "汽车涂料修补",
                    "机舱清洁",
                    "空气流量计更换",
                    "排气管清洗",
                    "传感器更换",
                    "行驶记录仪安装",
                    "油箱修理",
                    "刹车盘更换",
                    "电动座椅维修",
                    "车门锁修理",
                    "油压检查",
                    "引擎盖修复",
                    "汽车防盗报警系统维修",
                    "气囊检查",
                    "挡泥板更换",
                    "后视镜修复",
                    "排气温度传感器更换",
                    "车载导航系统修复",
                    "行驶系统检测",
                    "汽车声音系统检查",
                    "全车复检",
                    "全车喷漆",
                    "车载空调系统维修"
                ]
            },
            "data_category": "text"
        },
        "进厂时间": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2023,
                "end_year": 2023,
                "format": "%Y-%m-%d %H:%M"
            },
            "data_category": "date"
        },
        "完工时间": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2023,
                "end_year": 2023,
                "format": "%Y-%m-%d %H:%M"
            },
            "data_category": "date"
        },
        "维修技师": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "维修费用": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 100,
                "max_value": 50000,
                "decimals": 2,
                "format_string": "{:.2f}"
            },
            "data_category": "numeric"
        },
        "零件费用": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 50,
                "max_value": 30000,
                "decimals": 2,
                "format_string": "{:.2f}"
            },
            "data_category": "numeric"
        },
        "工时费用": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 50,
                "max_value": 20000,
                "decimals": 2,
                "format_string": "{:.2f}"
            },
            "data_category": "numeric"
        },
        "维修状态": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "已接车",
                    "检测中",
                    "等待配件",
                    "维修中",
                    "已完工",
                    "已交付",
                    "返修中",
                    "取消",
                    "延期维修",
                    "等待付款",
                    "质保中"
                ]
            },
            "data_category": "text"
        },
        "付款方式": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "现金",
                    "信用卡",
                    "借记卡",
                    "支付宝",
                    "微信支付",
                    "Apple Pay",
                    "Google Pay",
                    "PayPal",
                    "银行转账",
                    "银联支付",
                    "分期付款",
                    "现金支付",
                    "二维码支付",
                    "信用卡分期",
                    "花呗",
                    "京东支付",
                    "苏宁支付",
                    "美团支付",
                    "京东白条",
                    "余额宝",
                    "刷卡支付",
                    "代金券",
                    "购物卡",
                    "礼品卡",
                    "积分兑换",
                    "虚拟货币支付",
                    "Paytm",
                    "Venmo",
                    "Zelle",
                    "Stripe",
                    "Amazon Pay",
                    "Samsung Pay",
                    "阿里云支付",
                    "小程序支付",
                    "店铺积分",
                    "云闪付",
                    "银行卡转账",
                    "现金券",
                    "闪付",
                    "分期付",
                    "多付",
                    "社保卡支付",
                    "京东金融支付",
                    "快捷支付",
                    "招商银行支付",
                    "工行支付",
                    "建设银行支付",
                    "农业银行支付",
                    "交通银行支付",
                    "浦发银行支付",
                    "信用卡还款",
                    "支付宝花呗分期",
                    "微信分期",
                    "京东分期",
                    "一卡通支付",
                    "商户付款码",
                    "家居卡",
                    "会员卡",
                    "支付宝收钱码",
                    "加油卡支付",
                    "宠物卡支付",
                    "拼多多支付",
                    "网络银行支付",
                    "旅游卡",
                    "票务卡",
                    "预付卡",
                    "短期信用支付",
                    "大宗支付",
                    "租赁支付",
                    "车主卡支付",
                    "保险卡支付",
                    "快递卡支付",
                    "电力卡支付",
                    "水费卡支付",
                    "邮政储蓄卡",
                    "西联汇款",
                    "MoneyGram",
                    "Facebook Pay",
                    "InstaPay",
                    "Stripe Payments",
                    "AliPay International",
                    "OneCard",
                    "Gift Cards",
                    "Payoneer",
                    "Cash on Delivery",
                    "Green Dot",
                    "Bitcoin",
                    "Ethereum",
                    "Litecoin",
                    "Ripple",
                    "Uphold",
                    "Lemonade Pay",
                    "Sezzle",
                    "Afterpay",
                    "Klarna",
                    "Splitit",
                    "Braintree",
                    "Skrill",
                    "Alipay HK",
                    "WeChat HK",
                    "Circle Pay",
                    "KakaoPay",
                    "Toss",
                    "Rakuten Pay",
                    "Alipay Now",
                    "WeChat Now",
                    "Pundi X Pay",
                    "Globe Pay"
                ]
            },
            "data_category": "text"
        },
        "服务评分": {
            "generator_type": "integer_range",
            "params": {
                "min": 1,
                "max": 5
            },
            "data_category": "numeric"
        }
    },
    "text_columns_count": 7,
    "text_columns_names": [
        "车主姓名",
        "维修类型",
        "维修技师",
        "维修状态",
        "付款方式",
        "维修项目",
        "车辆品牌"

    ],
    "numeric_columns_count": 7,
    "numeric_columns_names": [
        "维修单号",
        "车牌号",
        "联系电话",
        "维修费用",
        "零件费用",
        "工时费用",
        "服务评分"
    ],
    "date_columns_count": 2,
    "date_columns_names": [
        "进厂时间",
        "完工时间"
    ],
    "other_columns_count": 3,
    "other_columns_names": [
        "车型",
        "车辆品牌",
        "维修项目"
    ],
    "all_columns": [
        "车主姓名",
        "维修类型",
        "维修技师",
        "维修状态",
        "付款方式",
        "维修单号",
        "车牌号",
        "联系电话",
        "维修费用",
        "零件费用",
        "工时费用",
        "服务评分",
        "进厂时间",
        "完工时间",
        "车型",
        "车辆品牌",
        "维修项目"
    ]
}

