import os
import random
import json
from PIL import Image, ImageDraw, ImageFont
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
from IPython.display import Image as IPImage, display
from faker import Faker
import datetime
import numpy as np
from faker.providers import DynamicProvider
from faker.providers import internet, company, address, date_time, person, phone_number, ssn
from faker.providers import color, credit_card, currency, file, job, lorem, misc, profile
from faker.providers.person.zh_CN import Provider as CNPersonProvider
import string
import re

# ====================== 全局配置部分 ======================
# 输出配置
OUTPUT_FOLDER = "表格"  # 主输出文件夹
OUTPUT_THREE_LINE_FOLDER = os.path.join(OUTPUT_FOLDER, "三线表")  # 三线表输出文件夹
OUTPUT_TEXT_TABLE_FOLDER = os.path.join(OUTPUT_FOLDER, "二维表")  # 二维表输出文件夹
OUTPUT_NO_LINE_FOLDER = os.path.join(OUTPUT_FOLDER, "无线表")  # 无线表输出文件夹
OUTPUT_ONE_DIM_FOLDER = os.path.join(OUTPUT_FOLDER, "一维表")  # 一维表输出文件夹
OUTPUT_TWO_DIM_FOLDER = os.path.join(OUTPUT_FOLDER, "文本表")  # 文本表输出文件夹
OUTPUT_DATA_TABLE_FOLDER = os.path.join(OUTPUT_FOLDER, "数据表")  # 新增: 数据表输出文件夹 - 仅日期和数值
NUM_IMAGES = 5 # 生成图片数量

# 表格类型
# TABLE_TYPES = ["三线表", "文本表", "无线表", "一维表", "二维表", "数据表"]  # 可用的表格类型
TABLE_TYPES = [ "数据表"]  # 可用的表格类型

# 表格配置
MIN_COLUMNS =2  # 最少列数（包括序号列）
MAX_COLUMNS = 10  # 最多列数（包括序号列）
MIN_ROWS = 2  # 最少数据行数
MAX_ROWS = 15  # 最多数据行数

# 样式配置
TITLE_FONTSIZE = 14
CELL_FONTSIZE = 12
ROW_HEIGHT = 0.5
DPI = 300

# 中文字体配置
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

# JSON配置文件路径
CONFIG_FILE = "table_config.json"

# ====================== 模型定义部分 ======================

class ColumnDefinition:
    """列定义结构，包含列名、数据类型和生成参数"""
    
    def __init__(self, name, data_type=None, generator_params=None, data_category=None):
        """初始化列定义
        Args:
            name: 列名
            data_type: 数据生成类型
            generator_params: 生成器参数
            data_category: 数据类别（text, numeric, date等）
        """
        self.name = name
        self.data_type = data_type  # 生成器类型
        self.generator_params = generator_params or {}
        self.data_category = data_category  # 数据类别
    
    def __str__(self):
        """返回列定义的字符串表示"""
        return f"列名: {self.name}, 类型: {self.data_type}, 类别: {self.data_category}"

class TableSchema:
    """表格模式类，用于定义表格的结构和内容生成规则"""
    
    def __init__(self, theme=None):
        """初始化表格模式，可选指定主题"""
        # 加载JSON配置
        self.config = self._load_config()
        
        # 获取可用主题列表
        self.available_themes = list(self.config["themes"].keys())
        
        # 如果未指定主题或指定的主题不在配置中，随机选择一个
        if theme is None or theme not in self.available_themes:
            self.theme = random.choice(self.available_themes)
        else:
            self.theme = theme
            
        self.column_definitions = []
        self._define_schema()
    
    def _load_config(self):
        """加载JSON配置文件"""
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Error loading config file: {e}")
            # 如果配置文件不存在或解析错误，返回一个空的配置
            return {"themes": {}}
    
    def _define_schema(self):
        """根据当前主题定义表格模式，包括列结构和生成配置"""
        if not self.theme:
            # 如果没有指定主题，随机选择一个
            self._select_random_theme()
        
        # 获取该主题的所有列配置
        theme_config = self.config["themes"].get(self.theme, {})
        columns_config = theme_config.get("columns", {})
        
        # 首先添加序号列
        columns = [ColumnDefinition("序号", "index", {}, "text")]
        
        # 收集所有其他可用列
        available_columns = []
        for col_name, col_config in columns_config.items():
            # 跳过序号列，避免重复
            if col_name == "序号":
                continue
                
            generator_type = col_config.get("generator_type")
            params = col_config.get("params", {})
            data_category = col_config.get("data_category", "text")  # 默认为文本类型
            
            available_columns.append(ColumnDefinition(
                col_name, 
                generator_type, 
                params,
                data_category
            ))
        
        # 确保列数在MIN_COLUMNS和MAX_COLUMNS之间
        # MIN_COLUMNS包含序号列，所以非序号列的最小数量是MIN_COLUMNS-1
        num_columns_to_choose = random.randint(max(MIN_COLUMNS - 1, 0), min(MAX_COLUMNS - 1, len(available_columns)))
        
        # 如果可用列少于需要选择的列数，使用所有可用列
        if len(available_columns) <= num_columns_to_choose:
            selected_columns = available_columns
        else:
            # 否则随机选择指定数量的列
            selected_columns = random.sample(available_columns, num_columns_to_choose)
        
        # 将选中的列添加到列定义中
        columns.extend(selected_columns)
        
        # 保存列定义列表
        self.column_definitions = columns
    
    def get_columns(self):
        """获取列名列表"""
        return [col.name for col in self.column_definitions]
    
    def get_theme(self):
        """获取表格主题"""
        return self.theme
    
    def get_theme_display_name(self):
        """获取主题显示名称"""
        if self.theme in self.config["themes"]:
            return self.config["themes"][self.theme].get("display_name", self.theme)
        return self.theme
    
    def get_table_title_template(self):
        """获取表格标题模板"""
        if self.theme in self.config["themes"]:
            return self.config["themes"][self.theme].get("table_title_template", "数据信息表")
        return "数据信息表"
    
    def get_column_definitions(self):
        """获取列定义列表"""
        return self.column_definitions.copy()

# ====================== 实用工具函数 ======================

def get_faker():
    """获取已配置的Faker实例"""
    fake = Faker(['zh_CN', 'en_US', 'ja_JP'])  # 增加多语言支持
    # 增加种子确保可重复性，但使用不同种子以增加多样性
    Faker.seed(random.randint(0, 999999))
    return fake

# ====================== 数据生成器 ======================

class DataGenerator:
    """基础数据生成器接口"""
    
    def generate(self, column_def, num_rows, theme):
        """生成数据的接口方法
        
        Args:
            column_def: 列定义对象
            num_rows: 需要生成的行数
            theme: 表格主题
            
        Returns:
            包含生成数据的列表
        """
        pass

class EnhancedDataGenerators(DataGenerator):
    """增强版数据生成器，基于JSON配置生成更合理的数据"""
    
    def __init__(self):
        # 使用不同语言的Faker实例
        self.fake_cn = Faker('zh_CN')
        self.fake_en = Faker('en_US')
        self.fake_jp = Faker('ja_JP')
        self.fake = self.fake_cn  # 默认使用中文
        
        # 初始化随机数生成器状态，确保每次生成的数据都有差异
        random.seed(datetime.datetime.now().microsecond)
        np.random.seed(datetime.datetime.now().microsecond)
    
    # ====================== 基础生成方法 ======================
    
    def generate_generic_values(self, n, values_pool, weighted=False, weights=None):
        """从给定的值池中生成随机值"""
        # 确保values_pool是一个序列，不是字典
        if isinstance(values_pool, dict):
            values_pool = list(values_pool.values())
        
        # 确保values_pool不为空
        if not values_pool:
            values_pool = ["无数据"]
            
        if weighted and weights:
            return [random.choices(values_pool, weights=weights)[0] for _ in range(n)]
        else:
            return [random.choice(values_pool) for _ in range(n)]
    
    def generate_random_dates(self, n, start_year=1900, end_year=None, format="%Y-%m-%d"):
        """生成指定范围内的随机日期"""
        if end_year is None:
            end_year = datetime.datetime.now().year
        
        return [self.fake.date_between(
            start_date=datetime.date(start_year, 1, 1),
            end_date=datetime.date(end_year, 12, 31)
        ).strftime(format) for _ in range(n)]
    
    def generate_random_numbers(self, n, min_val, max_val, decimals=0):
        """生成随机数字"""
        if decimals == 0:
            return [random.randint(min_val, max_val) for _ in range(n)]
        else:
            return [round(random.uniform(min_val, max_val), decimals) for _ in range(n)]
    
    def generate_alphanumeric_pattern(self, n, patterns):
        """根据模式生成字母数字组合"""
        result = []
        for _ in range(n):
            pattern = random.choice(patterns)
            # 替换模式中的 # 为数字, X/x 为大/小写字母
            output = ""
            for char in pattern:
                if char == '#':
                    output += str(random.randint(0, 9))
                elif char == 'X':
                    output += random.choice(string.ascii_uppercase)
                elif char == 'x':
                    output += random.choice(string.ascii_lowercase)
                elif char == '?':
                    output += random.choice(string.ascii_letters)
                else:
                    output += char
            result.append(output)
        return result
    
    # ====================== 配置驱动的生成方法 ======================
    
    def generate(self, column_def, num_rows, theme):
        """实现基类接口，基于列定义和生成器类型生成数据"""
        if column_def.name == "序号" or column_def.data_type == "index":
            return list(range(1, num_rows + 1))
        
        # 根据生成器类型分发到对应的数据生成方法
        generator_type = column_def.data_type
        params = column_def.generator_params
        
        # 各种生成器类型的处理
        if generator_type == "categorical":
            return self._generate_categorical(num_rows, params)
        elif generator_type == "categorical_with_pattern":
            return self._generate_categorical_with_pattern(num_rows, params)
        elif generator_type == "faker_name":
            return self._generate_faker_name(num_rows, params)
        elif generator_type == "faker_company":
            return self._generate_faker_company(num_rows, params)
        elif generator_type == "faker_address":
            return self._generate_faker_address(num_rows, params)
        elif generator_type == "faker_job":
            return self._generate_faker_job(num_rows, params)
        elif generator_type == "faker_phone_number":
            return self._generate_faker_phone_number(num_rows, params)
        elif generator_type == "faker_email":
            return self._generate_faker_email(num_rows, params)
        elif generator_type == "faker_product_name":
            return self._generate_faker_product_name(num_rows, params)
        elif generator_type == "date_range":
            return self._generate_date_range(num_rows, params)
        elif generator_type == "integer_range":
            return self._generate_integer_range(num_rows, params)
        elif generator_type == "integer_range_with_unit":
            return self._generate_integer_range_with_unit(num_rows, params)
        elif generator_type == "numerical_range_formatted":
            return self._generate_numerical_range_formatted(num_rows, params)
        elif generator_type == "percentage":
            return self._generate_percentage(num_rows, params)
        elif generator_type == "alphanumeric_pattern":
            return self._generate_alphanumeric_pattern(num_rows, params)
        elif generator_type == "dimension_format":
            return self._generate_dimension_format(num_rows, params)
        elif generator_type == "score_range":
            return self._generate_score_range(num_rows, params)
        elif generator_type == "calculated_sum":
            return self._generate_calculated_sum(num_rows, params)
        elif generator_type == "rank":
            return self._generate_rank(num_rows, params)
        else:
            # 如果没有匹配的生成器类型，生成默认数据
            return [f"数据-{i+1}" for i in range(num_rows)]
    
    # ====================== 具体生成方法实现 ======================
    
    def _generate_categorical(self, n, params):
        """从分类值列表中生成数据"""
        values = params.get("values", ["无数据"])
        # 确保values是一个列表而不是字典
        if isinstance(values, dict):
            values = list(values.values())  # 如果是字典，将值转换为列表
        return self.generate_generic_values(n, values)
    
    def _generate_categorical_with_pattern(self, n, params):
        """根据前缀+后缀模式生成分类数据"""
        prefixes = params.get("prefixes", ["默认"])
        suffixes = params.get("suffixes", ["项目"])
        
        # 确保prefixes和suffixes是列表而不是字典
        if isinstance(prefixes, dict):
            prefixes = list(prefixes.values())
        if isinstance(suffixes, dict):
            suffixes = list(suffixes.values())
            
        # 确保不为空
        if not prefixes:
            prefixes = ["默认"]
        if not suffixes:
            suffixes = ["项目"]
            
        result = []
        for _ in range(n):
            prefix = random.choice(prefixes)
            suffix = random.choice(suffixes)
            result.append(f"{prefix}{suffix}")
        return result
    
    def _generate_faker_name(self, n, params):
        """使用Faker生成姓名"""
        locale = params.get("locale", "zh_CN")
        faker = Faker(locale)
        return [faker.name() for _ in range(n)]
    
    def _generate_faker_company(self, n, params):
        """使用Faker生成公司名称"""
        locale = params.get("locale", "zh_CN")
        faker = Faker(locale)
        return [faker.company() for _ in range(n)]
    
    def _generate_faker_address(self, n, params):
        """使用Faker生成地址"""
        locale = params.get("locale", "zh_CN")
        faker = Faker(locale)
        return [faker.address() for _ in range(n)]
    
    def _generate_faker_job(self, n, params):
        """使用Faker生成职业"""
        locale = params.get("locale", "zh_CN")
        faker = Faker(locale)
        return [faker.job() for _ in range(n)]
    
    def _generate_faker_phone_number(self, n, params):
        """使用Faker生成电话号码"""
        locale = params.get("locale", "zh_CN")
        faker = Faker(locale)
        return [faker.phone_number() for _ in range(n)]
    
    def _generate_faker_email(self, n, params):
        """使用Faker生成电子邮箱"""
        locale = params.get("locale", "zh_CN")
        faker = Faker(locale)
        return [faker.email() for _ in range(n)]
    
    def _generate_faker_product_name(self, n, params):
        """使用自定义逻辑生成产品名称"""
        locale = params.get("locale", "zh_CN")
        faker = Faker(locale)
        product_types = ["智能手机", "笔记本电脑", "平板电脑", "智能手表", "无线耳机", "智能音箱"]
        brands = ["苹果", "华为", "小米", "三星", "索尼", "戴尔", "联想"]
        models = ["Pro", "Max", "Plus", "Lite", "Ultra", "Mini", "SE", "Air"]
        
        result = []
        for _ in range(n):
            brand = random.choice(brands)
            product = random.choice(product_types)
            model = random.choice(models) if random.random() > 0.3 else ""
            year = str(random.randint(2020, 2024)) if random.random() > 0.7 else ""
            result.append(f"{brand} {product}{' ' + model if model else ''}{' ' + year if year else ''}")
        return result
    
    def _generate_date_range(self, n, params):
        """生成日期范围"""
        start_year = params.get("start_year", 2000)
        end_year = params.get("end_year", datetime.datetime.now().year)
        date_format = params.get("format", "%Y-%m-%d")
        return self.generate_random_dates(n, start_year, end_year, date_format)
    
    def _generate_integer_range(self, n, params):
        """生成整数范围"""
        min_val = params.get("min", 0)
        max_val = params.get("max", 100)
        return self.generate_random_numbers(n, min_val, max_val, decimals=0)
    
    def _generate_integer_range_with_unit(self, n, params):
        """生成带单位的整数范围"""
        min_val = params.get("min", 0)
        max_val = params.get("max", 100)
        unit = params.get("unit", "")
        numbers = self.generate_random_numbers(n, min_val, max_val, decimals=0)
        return [f"{num}{unit}" for num in numbers]
    
    def _generate_numerical_range_formatted(self, n, params):
        """生成格式化的数值范围"""
        min_value = params.get("min_value", 0)
        max_value = params.get("max_value", 1000)
        decimals = params.get("decimals", 0)
        format_string = params.get("format_string", "{}")
        
        numbers = self.generate_random_numbers(n, min_value, max_value, decimals)
        
        # 修复格式化处理
        result = []
        for num in numbers:
            try:
                # 尝试使用位置参数格式化（支持 {:,}、{:.2f} 等形式）
                formatted = format_string.format(num)
                result.append(formatted)
            except (IndexError, KeyError):
                try:
                    # 尝试使用命名参数格式化（支持 {value} 形式）
                    formatted = format_string.format(value=num)
                    result.append(formatted)
                except:
                    # 兜底方案：直接拼接
                    result.append(f"{num}{format_string}")
        
        return result
    
    def _generate_percentage(self, n, params):
        """生成百分比"""
        min_value = params.get("min_value", 0)
        max_value = params.get("max_value", 100)
        decimals = params.get("decimals", 0)
        
        numbers = self.generate_random_numbers(n, min_value, max_value, decimals)
        return [f"{num}%" for num in numbers]
    
    def _generate_alphanumeric_pattern(self, n, params):
        """生成字母数字模式"""
        patterns = params.get("patterns", ["A-###"])
        return self.generate_alphanumeric_pattern(n, patterns)
    
    def _generate_dimension_format(self, n, params):
        """生成尺寸格式"""
        min_length = params.get("min_length", 1000)
        max_length = params.get("max_length", 5000)
        min_width = params.get("min_width", 500)
        max_width = params.get("max_width", 2000)
        min_height = params.get("min_height", 300)
        max_height = params.get("max_height", 1500)
        format_string = params.get("format_string", "{}×{}×{}mm")
        
        result = []
        for _ in range(n):
            length = random.randint(min_length, max_length)
            width = random.randint(min_width, max_width)
            height = random.randint(min_height, max_height)
            result.append(format_string.format(length, width, height))
        return result
    
    def _generate_score_range(self, n, params):
        """生成考试成绩范围"""
        min_val = params.get("min", 60)
        max_val = params.get("max", 100)
        return self.generate_random_numbers(n, min_val, max_val, decimals=0)
    
    def _generate_calculated_sum(self, n, params):
        """暂时返回随机总分，实际使用时需要根据其他列计算"""
        # 这个方法需要在TableGenerator中处理，因为它依赖于其他列的值
        min_val = params.get("min", 300)
        max_val = params.get("max", 600)
        return self.generate_random_numbers(n, min_val, max_val, decimals=0)
    
    def _generate_rank(self, n, params):
        """生成排名，实际使用时需要根据指定列排序"""
        # 这个方法需要在TableGenerator中处理，因为它依赖于其他列的值
        return list(range(1, n + 1))

# ====================== 表格渲染 ======================

class TableRenderer:
    """表格渲染器，将数据渲染为表格图像"""
    
    def __init__(self, output_folder=OUTPUT_FOLDER, dpi=DPI, 
                 title_fontsize=TITLE_FONTSIZE, cell_fontsize=CELL_FONTSIZE, 
                 row_height=ROW_HEIGHT):
        """初始化表格渲染器"""
        self.output_folder = output_folder
        self.dpi = dpi
        self.title_fontsize = title_fontsize
        self.cell_fontsize = cell_fontsize
        self.row_height = row_height
        
        # 确保输出文件夹存在
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
    
    def calculate_column_widths(self, columns, data):
        """计算列宽度，根据内容长度动态调整，并处理长文本"""
        col_widths = []
        
        # 为每列计算最大内容宽度
        for idx, col in enumerate(columns):
            # 列标题长度
            max_width = len(str(col)) * 0.25  # 增加文本宽度系数
            
            # 数据内容长度
            if not data.empty:
                col_data = data.iloc[:, idx].astype(str)
                data_max_len = col_data.str.len().max() * 0.25  # 增加文本宽度系数
                max_width = max(max_width, data_max_len)
            
            # 确保最小宽度并限制最大宽度
            max_width = max(1.5, min(max_width, 8.0))  # 设置列宽上限，防止过宽
            col_widths.append(max_width)
        
        return col_widths
    
    def wrap_text(self, text, max_width):
        """将长文本按照最大宽度进行换行处理"""
        if len(text) <= max_width:
            return text
        
        # 简单的文本换行处理
        words = text.replace('，', ',').replace('。', '.').replace('；', ';').split()
        lines = []
        current_line = []
        current_length = 0
        
        for word in words:
            # 如果当前单词加上当前行已有内容会超出最大宽度，则开始新行
            if current_length + len(word) + 1 > max_width:
                if current_line:  # 如果当前行不为空，则保存当前行
                    lines.append(' '.join(current_line))
                    current_line = []
                    current_length = 0
                
                # 如果单个词超过最大宽度，则分割该词
                if len(word) > max_width:
                    for i in range(0, len(word), max_width):
                        chunk = word[i:i + max_width]
                        lines.append(chunk)
                else:
                    current_line = [word]
                    current_length = len(word)
            else:
                current_line.append(word)
                current_length += len(word) + 1  # +1 是为了空格
        
        if current_line:  # 添加最后一行
            lines.append(' '.join(current_line))
        
        return '\n'.join(lines)
    
    def render_three_line_table(self, df, img_index, theme):
        """将数据渲染为三线表格图像并保存"""
        # 设置matplotlib不使用GUI后端
        matplotlib.use('Agg')
        
        # 处理数据：转换长文本为多行文本
        df_processed = df.copy()
        max_text_width = 20  # 最大文本宽度，超过则换行
        
        for col in df_processed.columns:
            df_processed[col] = df_processed[col].astype(str).apply(
                lambda x: self.wrap_text(x, max_text_width)
            )
        
        # 准备数据
        columns = df_processed.columns.tolist()
        data_rows = df_processed.values.tolist()
        table_data = [columns] + data_rows
        
        # 计算列宽度，考虑实际数据
        cell_widths = self.calculate_column_widths(columns, df_processed)
        
        # 创建图像
        fig = plt.figure(figsize=(sum(cell_widths) + 1, self.row_height * (len(table_data) + 2)))
        ax = plt.gca()
        ax.axis('off')
        
        # 创建表格
        table = ax.table(
            cellText=data_rows,
            colLabels=columns,
            cellLoc='center',
            loc='center',
            bbox=[0.0, 0.0, 1.0, 1.0]
        )
        
        # 调整表格样式
        table.auto_set_font_size(False)
        table.set_fontsize(self.cell_fontsize)
        
        # 设置列宽
        table_width = sum(cell_widths)
        for i, width in enumerate(cell_widths):
            table.auto_set_column_width([i])
            for cell in [key for key in table._cells if key[1] == i]:
                table._cells[cell].set_width(width / table_width)
                
        # 三线表特有样式：只保留顶线、表头底线和底线
        for pos, cell in table._cells.items():
            cell.set_height(self.row_height / len(table_data))
            
            # 默认不显示边框
            cell.set_edgecolor('none')
            
            # 设置标题行样式
            if pos[0] == 0:  # 标题行
                cell.set_text_props(weight='bold')
                cell.set_facecolor('#f5f5f5')
                
                # 顶线和表头下方横线
                edge_chars = 'T'  # 顶线
                if pos[1] == 0:  # 第一列的左边线
                    edge_chars += 'L'
                if pos[1] == len(columns) - 1:  # 最后一列的右边线
                    edge_chars += 'R'
                    
                # 设置底边框（表头下方横线）
                edge_chars += 'B'
                
                cell.visible_edges = edge_chars
                    
            elif pos[0] == len(data_rows):  # 最后一行
                # 设置底线
                edge_chars = 'B'  # 底线
                if pos[1] == 0:  # 第一列的左边线
                    edge_chars += 'L'
                if pos[1] == len(columns) - 1:  # 最后一列的右边线
                    edge_chars += 'R'
                    
                cell.visible_edges = edge_chars
                
            else:  # 普通数据行，没有横线
                edge_chars = ''
                if pos[1] == 0:  # 第一列的左边线
                    edge_chars += 'L'
                if pos[1] == len(columns) - 1:  # 最后一列的右边线
                    edge_chars += 'R'
                    
                cell.visible_edges = edge_chars
                
            # 设置边框线宽
            if pos[0] in [0, 1] or pos[0] == len(table_data):
                cell.set_linewidth(1.5)  # 顶线、表头下横线和底线加粗
            else:
                cell.set_linewidth(0.75)
        
        # 获取并设置表格标题
        schema = TableSchema(theme)
        table_title = f"{schema.get_table_title_template()} {img_index+1}"
        ax.set_title(table_title, fontsize=self.title_fontsize, pad=20)
        
        # 创建输出文件路径
        display_name = schema.get_theme_display_name()
        output_path = os.path.join(OUTPUT_THREE_LINE_FOLDER, f"{display_name}三线表_{img_index+1}.jpg")
        
        # 确保输出文件夹存在
        if not os.path.exists(OUTPUT_THREE_LINE_FOLDER):
            os.makedirs(OUTPUT_THREE_LINE_FOLDER)
        
        # 保存图片
        plt.tight_layout()
        plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        return output_path

    def render_text_table(self, df, img_index, theme):
        """将数据渲染为二维表格图像并保存（带全部边框）"""
        # 设置matplotlib不使用GUI后端
        matplotlib.use('Agg')
        
        # 处理数据：转换长文本为多行文本，并确保没有复选框字符
        max_text_width = 20  # 最大文本宽度，超过则换行
        for col in df.columns:
            # 确保没有可能被显示为复选框的字符
            if col == "金额" or col in ["销售额", "成交量", "利润"]:
                # 使用空格替代可能的特殊字符前缀
                df[col] = df[col].astype(str).apply(
                    lambda x: x.replace('¥', ' ').replace('￥', ' ').strip()
                )
            
            df[col] = df[col].astype(str).apply(
                lambda x: self.wrap_text(x, max_text_width)
            )
        
        # 准备数据
        columns = df.columns.tolist()
        data_rows = df.values.tolist()
        table_data = [columns] + data_rows
        
        # 计算列宽度，考虑实际数据
        cell_widths = self.calculate_column_widths(columns, df)
        
        # 创建图像
        fig = plt.figure(figsize=(sum(cell_widths) + 1, self.row_height * (len(table_data) + 2)))
        ax = plt.gca()
        ax.axis('off')
        
        # 创建表格
        table = ax.table(
            cellText=data_rows,
            colLabels=columns,
            cellLoc='center',
            loc='center',
            bbox=[0.0, 0.0, 1.0, 1.0]
        )
        
        # 调整表格样式
        table.auto_set_font_size(False)
        table.set_fontsize(self.cell_fontsize)
        
        # 设置列宽
        table_width = sum(cell_widths)
        for i, width in enumerate(cell_widths):
            table.auto_set_column_width([i])
            for cell in [key for key in table._cells if key[1] == i]:
                table._cells[cell].set_width(width / table_width)
                
        # 设置行高并添加边框
        for pos, cell in table._cells.items():
            cell.set_height(self.row_height / len(table_data))
            cell.set_edgecolor('black')
            
            # 设置标题行和底部粗线
            if pos[0] == 0:  # 标题行
                cell.set_text_props(weight='bold')
                cell.set_linewidth(1.5)
                cell.set_facecolor('#f2f2f2')
            else:  # 数据行
                cell.set_linewidth(0.75)
                
            # 顶部和底部边框加粗
            if pos[0] in [0, 1] or pos[0] == len(table_data) - 1:
                cell.set_linewidth(1.5)
        
        # 获取并设置表格标题
        schema = TableSchema(theme)
        table_title = f"{schema.get_table_title_template()} {img_index+1}"
        ax.set_title(table_title, fontsize=self.title_fontsize, pad=20)
        
        # 创建输出文件路径
        display_name = schema.get_theme_display_name()
        output_path = os.path.join(OUTPUT_TEXT_TABLE_FOLDER, f"{display_name}二维表_{img_index+1}.jpg")
        
        # 确保输出文件夹存在
        if not os.path.exists(OUTPUT_TEXT_TABLE_FOLDER):
            os.makedirs(OUTPUT_TEXT_TABLE_FOLDER)
        
        # 保存图片
        plt.tight_layout()
        plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        return output_path

    def render_no_line_table(self, df, img_index, theme):
        """将数据渲染为无线表格图像并保存（只保留表头下方横线）"""
        # 设置matplotlib不使用GUI后端
        matplotlib.use('Agg')
        
        # 准备数据
        columns = df.columns.tolist()
        data_rows = df.values.tolist()
        table_data = [columns] + data_rows
        
        # 计算列宽度，考虑实际数据
        cell_widths = self.calculate_column_widths(columns, df)
        
        # 计算表格尺寸
        ncols = len(columns)
        nrows = len(table_data)
        total_width = sum(cell_widths)
        total_height = self.row_height * (nrows + 1.5)  # 额外空间用于标题
        
        # 创建图像
        plt.figure(figsize=(total_width + 1, total_height))
        ax = plt.gca()
        ax.axis('off')
        
        # 获取表格标题
        schema = TableSchema(theme)
        table_title = f"{schema.get_table_title_template()} {img_index+1}"
        plt.title(table_title, fontsize=self.title_fontsize, pad=10)
        
        # 计算表格区域
        x_start = 0
        x_end = total_width
        y_head_sep = nrows - 1
        
        # 放置单元格文本
        for row in range(nrows):
            for col in range(ncols):
                value = table_data[row][col]
                plt.text(sum(cell_widths[:col]) + cell_widths[col] / 2, nrows - row - 0.5, str(value),
                       ha='center', va='center', fontsize=self.cell_fontsize)
        
        # 只画表头底线
        plt.plot([x_start, x_end], [y_head_sep, y_head_sep], color='black', linewidth=1)  # 表头底线
        
        # 设置坐标轴范围
        plt.xlim(-0.5, total_width + 0.5)
        plt.ylim(-0.5, nrows + 0.5)
        
        # 创建输出文件路径
        display_name = schema.get_theme_display_name()
        output_path = os.path.join(OUTPUT_NO_LINE_FOLDER, f"{display_name}无线表_{img_index+1}.jpg")
        
        # 确保输出文件夹存在
        if not os.path.exists(OUTPUT_NO_LINE_FOLDER):
            os.makedirs(OUTPUT_NO_LINE_FOLDER)
        
        # 保存图片
        plt.subplots_adjust(left=0.05, right=0.95, top=0.9, bottom=0.05)
        plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        return output_path

    def render_one_dim_table(self, df, img_index, theme):
        """将数据渲染为一维表格图像（只含序号和一个数据列）"""
        # 设置matplotlib不使用GUI后端
        matplotlib.use('Agg')
        
        # 确保只有2列：序号列和一个数据列
        if "序号" not in df.columns:
            # 如果没有序号列，添加一个（不太可能出现，但以防万一）
            df["序号"] = range(1, len(df) + 1)
            
        # 确保序号列是第一列
        columns = ["序号"]
        
        # 选择非序号列中的一列
        data_columns = [col for col in df.columns if col != "序号"]
        if data_columns:
            selected_col = random.choice(data_columns)
            columns.append(selected_col)
            # 只保留序号列和选中的数据列
            df = df[columns].copy()
        else:
            # 极端情况：只有序号列
            # 创建一个新的数据列
            new_col = "数据"
            df[new_col] = [f"数据-{i}" for i in range(1, len(df) + 1)]
            columns.append(new_col)
        
        # 处理数据：转换长文本为多行文本
        df_processed = df.copy()
        max_text_width = 20  # 最大文本宽度，超过则换行
        
        for col in df_processed.columns:
            df_processed[col] = df_processed[col].astype(str).apply(
                lambda x: self.wrap_text(x, max_text_width)
            )
        
        # 准备数据
        columns = df_processed.columns.tolist()
        data_rows = df_processed.values.tolist()
        table_data = [columns] + data_rows
        
        # 计算列宽度，考虑实际数据
        cell_widths = self.calculate_column_widths(columns, df_processed)
        
        # 创建图像
        fig = plt.figure(figsize=(sum(cell_widths) + 1, self.row_height * (len(table_data) + 2)))
        ax = plt.gca()
        ax.axis('off')
        
        # 创建表格
        table = ax.table(
            cellText=data_rows,
            colLabels=columns,
            cellLoc='center',
            loc='center',
            bbox=[0.0, 0.0, 1.0, 1.0]
        )
        
        # 调整表格样式
        table.auto_set_font_size(False)
        table.set_fontsize(self.cell_fontsize)
        
        # 设置列宽
        table_width = sum(cell_widths)
        for i, width in enumerate(cell_widths):
            table.auto_set_column_width([i])
            for cell in [key for key in table._cells if key[1] == i]:
                table._cells[cell].set_width(width / table_width)
                
        # 设置行高并添加边框
        for pos, cell in table._cells.items():
            cell.set_height(self.row_height / len(table_data))
            cell.set_edgecolor('black')
            
            # 设置标题行和第一列的样式
            if pos[0] == 0:  # 标题行
                cell.set_text_props(weight='bold', color='black')
                cell.set_facecolor('#f2f2f2')
            else:  # 数据行
                cell.set_text_props(color='black')
                if pos[1] == 0:  # 第一列（序号）
                    cell.set_text_props(weight='bold')
                cell.set_facecolor('white')
                
            # 设置边框线宽
            cell.set_linewidth(0.75)
            # 顶部和底部边框加粗
            if pos[0] in [0, 1] or pos[0] == len(table_data):
                cell.set_linewidth(1.5)
        
        # 获取并设置表格标题
        schema = TableSchema(theme)
        table_title = f"{schema.get_table_title_template()} {img_index+1}"
        ax.set_title(table_title, fontsize=self.title_fontsize, pad=20)
        
        # 创建输出文件路径
        display_name = schema.get_theme_display_name()
        output_path = os.path.join(OUTPUT_ONE_DIM_FOLDER, f"{display_name}一维表_{img_index+1}.jpg")
        
        # 确保输出文件夹存在
        if not os.path.exists(OUTPUT_ONE_DIM_FOLDER):
            os.makedirs(OUTPUT_ONE_DIM_FOLDER)
        
        # 保存图片
        plt.tight_layout()
        plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        return output_path

    def render_two_dim_table(self, df, img_index, theme):
        """将数据渲染为文本表格图像（专注于文本类型列，并将非文本列转换为文本格式）"""
        # 设置matplotlib不使用GUI后端
        matplotlib.use('Agg')
        
        # 获取表格结构定义
        schema = TableSchema(theme)
        column_definitions = schema.get_column_definitions()
        
        # 创建列名到列定义的映射，用于快速查找
        col_def_map = {col_def.name: col_def for col_def in column_definitions}
        
        # 分类所有列：文本列和非文本列
        text_columns = []  # 纯文本类型的列
        non_text_columns = []  # 非文本类型的列（数值、日期等）
        
        for col in df.columns:
            # 序号列单独处理
            if col == "序号":
                continue
                
            # 检查列是否在定义中，且是文本类型
            if col in col_def_map and col_def_map[col].data_category == "text":
                text_columns.append(col)
            elif col in df.columns:  # 所有其他非文本列
                non_text_columns.append(col)
        
        # 1. 按照全局MIN_COLUMNS/MAX_COLUMNS设置确定最终列数
        # 减1是因为序号列要单独计算
        target_col_count = random.randint(min(MIN_COLUMNS - 1, len(df.columns) - 1), 
                                        min(MAX_COLUMNS - 1, len(df.columns) - 1))
        
        # 2. 优先选择文本列
        final_columns = []
        if len(text_columns) > target_col_count:
            # 如果文本列过多，随机选择
            final_columns = random.sample(text_columns, target_col_count)
        else:
            # 所有文本列都保留
            final_columns = text_columns.copy()
            
            # 3. 文本列不足时，将非文本列转换为文本格式
            remaining_slots = target_col_count - len(final_columns)
            if remaining_slots > 0 and non_text_columns:
                # 确定需要添加的非文本列数量
                cols_to_add = min(remaining_slots, len(non_text_columns))
                selected_non_text = random.sample(non_text_columns, cols_to_add)
                
                # 为每个选中的非文本列添加适当的文本前缀或后缀
                for col in selected_non_text:
                    df_processed = df.copy()
                    
                    # 根据列类型添加合适的前缀或后缀
                    if col in col_def_map:
                        data_category = col_def_map[col].data_category
                        
                        if data_category == "numeric":
                            # 为数值列添加适当的后缀
                            suffixes = ["个", "元", "件", "次", "分", "米", "千克", "%", "人"]
                            suffix = random.choice(suffixes)
                            df[col] = df[col].astype(str) + suffix
                        
                        elif data_category == "date":
                            # 为日期列添加前缀
                            prefixes = ["日期: ", "时间: ", "记录于: ", "登记: "]
                            prefix = random.choice(prefixes)
                            df[col] = prefix + df[col].astype(str)
                        
                        else:
                            # 其他类型列
                            misc_prefixes = ["信息: ", "数据: ", "记录: "]
                            prefix = random.choice(misc_prefixes)
                            df[col] = prefix + df[col].astype(str)
                    else:
                        # 未知类型列，直接转为字符串
                        df[col] = df[col].astype(str)
                    
                    final_columns.append(col)
        
        # 4. 如果经过上述处理后，列数仍然不足，添加备注列
        while len(final_columns) < target_col_count:
            note_col = f"备注{len(final_columns) + 1}"
            df[note_col] = [f"补充信息{i+1}" for i in range(len(df))]
            final_columns.append(note_col)
        
        # 确保有序号列
        if "序号" not in df.columns:
            df["序号"] = range(1, len(df) + 1)
        
        # 准备最终的列顺序，确保序号在第一位
        final_columns = ["序号"] + final_columns
            
        # 只保留筛选后的列
        df_filtered = df[final_columns].copy()
        
        # 处理数据：转换长文本为多行文本
        max_text_width = 20  # 最大文本宽度，超过则换行
        for col in df_filtered.columns:
            df_filtered[col] = df_filtered[col].astype(str).apply(
                lambda x: self.wrap_text(x, max_text_width)
            )
        
        # 准备数据
        columns = df_filtered.columns.tolist()
        data_rows = df_filtered.values.tolist()
        table_data = [columns] + data_rows
        
        # 计算列宽度，考虑实际数据
        cell_widths = self.calculate_column_widths(columns, df_filtered)
        
        # 创建图像
        fig = plt.figure(figsize=(sum(cell_widths) + 1, self.row_height * (len(table_data) + 2)))
        ax = plt.gca()
        ax.axis('off')
        
        # 创建表格
        table = ax.table(
            cellText=data_rows,
            colLabels=columns,
            cellLoc='center',
            loc='center',
            bbox=[0.0, 0.0, 1.0, 1.0]
        )
        
        # 调整表格样式
        table.auto_set_font_size(False)
        table.set_fontsize(self.cell_fontsize)
        
        # 设置列宽
        table_width = sum(cell_widths)
        for i, width in enumerate(cell_widths):
            table.auto_set_column_width([i])
            for cell in [key for key in table._cells if key[1] == i]:
                table._cells[cell].set_width(width / table_width)
                
        # 设置行高并添加边框
        for pos, cell in table._cells.items():
            cell.set_height(self.row_height / len(table_data))
            cell.set_edgecolor('black')
            
            # 设置标题行和第一列的样式
            if pos[0] == 0:  # 标题行
                cell.set_text_props(weight='bold', color='black')
                cell.set_facecolor('#e6f3ff')  # 使用不同的背景色区分于文本表
            else:  # 数据行
                cell.set_text_props(color='black')
                if pos[1] == 0:  # 第一列（序号）
                    cell.set_text_props(weight='bold')
                cell.set_facecolor('white')
                
            # 设置边框线宽
            cell.set_linewidth(0.75)
            # 顶部和底部边框加粗
            if pos[0] in [0, 1] or pos[0] == len(table_data):
                cell.set_linewidth(1.5)
        
        # 获取并设置表格标题
        table_title = f"{schema.get_table_title_template()} {img_index+1}"
        ax.set_title(table_title, fontsize=self.title_fontsize, pad=20)
        
        # 创建输出文件路径
        display_name = schema.get_theme_display_name()
        output_path = os.path.join(OUTPUT_TWO_DIM_FOLDER, f"{display_name}文本表_{img_index+1}.jpg")
        
        # 确保输出文件夹存在
        if not os.path.exists(OUTPUT_TWO_DIM_FOLDER):
            os.makedirs(OUTPUT_TWO_DIM_FOLDER)
        
        # 保存图片
        plt.tight_layout()
        plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        return output_path

    def render_data_table(self, df, img_index, theme):
        """数据表--将数据渲染为数据表格图像（只包含日期和数值类型列）"""
        # 设置matplotlib不使用GUI后端
        matplotlib.use('Agg')
        
        # 获取表格结构定义
        schema = TableSchema(theme)
        column_definitions = schema.get_column_definitions()
        
        # 创建列名到列定义的映射，用于快速查找
        col_def_map = {col_def.name: col_def for col_def in column_definitions}
        
        # 分类所有列：日期列和数值列
        date_columns = []  # 日期类型的列
        numeric_columns = []  # 数值类型的列
        
        for col in df.columns:
            # 序号列单独处理
            if col == "序号":
                continue
                
            # 检查列是否在定义中，且是日期或数值类型
            if col in col_def_map:
                if col_def_map[col].data_category == "date":
                    date_columns.append(col)
                elif col_def_map[col].data_category == "numeric":
                    numeric_columns.append(col)
        
        # 1. 按照全局MIN_COLUMNS/MAX_COLUMNS设置确定最终列数
        # 减1是因为序号列要单独计算
        target_col_count = random.randint(min(MIN_COLUMNS - 1, len(df.columns) - 1), 
                                        min(MAX_COLUMNS - 1, len(df.columns) - 1))
        
        # 2. 优先选择日期和数值列
        final_columns = []
        available_columns = date_columns + numeric_columns
        
        if len(available_columns) > target_col_count:
            # 如果可用列过多，随机选择，但确保至少有一个日期列和一个数值列（如果原数据中有的话）
            if date_columns and numeric_columns:
                # 确保至少选择一个日期列和一个数值列
                selected_date = random.choice(date_columns)
                selected_numeric = random.choice(numeric_columns)
                final_columns = [selected_date, selected_numeric]
                
                # 从剩余列中随机选择，直到达到目标列数
                remaining_columns = [col for col in available_columns if col not in final_columns]
                remaining_slots = target_col_count - len(final_columns)
                
                if remaining_slots > 0 and remaining_columns:
                    # 随机选择剩余的列
                    additional_columns = random.sample(remaining_columns, min(remaining_slots, len(remaining_columns)))
                    final_columns.extend(additional_columns)
            else:
                # 如果不同时存在日期和数值列，则直接随机选择
                final_columns = random.sample(available_columns, min(target_col_count, len(available_columns)))
        else:
            # 所有可用列都保留
            final_columns = available_columns.copy()
        
        # 3. 如果数值列不够，添加一个额外的金额列
        if len(final_columns) < target_col_count:
            # 添加金额列
            money_col = "金额"
            # 生成1000-50000之间的随机金额，不使用任何货币符号
            df[money_col] = [f"{random.uniform(1000, 50000):.2f}" for _ in range(len(df))]
            final_columns.append(money_col)
            
            # 如果仍然不足，可以继续添加其他随机数值列
            additional_columns = ["销售额", "成交量", "利润", "增长率"]
            for col_name in additional_columns:
                if len(final_columns) < target_col_count:
                    if col_name == "增长率":
                        # 生成-20%到50%之间的增长率
                        df[col_name] = [f"{random.uniform(-20, 50):.1f}%" for _ in range(len(df))]
                    else:
                        # 生成其他随机数值
                        df[col_name] = [f"{random.uniform(100, 10000):.2f}" for _ in range(len(df))]
                    final_columns.append(col_name)
                else:
                    break
        
        # 确保有序号列
        if "序号" not in df.columns:
            df["序号"] = range(1, len(df) + 1)
        
        # 准备最终的列顺序，确保序号在第一位
        final_columns = ["序号"] + final_columns
            
        # 只保留筛选后的列
        df_filtered = df[final_columns].copy()
        
        # 处理数据：转换长文本为多行文本，并确保没有复选框字符
        max_text_width = 20  # 最大文本宽度，超过则换行
        for col in df_filtered.columns:
            # 确保没有可能被显示为复选框的字符
            if col == "金额" or col in ["销售额", "成交量", "利润"]:
                # 使用空格替代可能的特殊字符前缀
                df_filtered[col] = df_filtered[col].astype(str).apply(
                    lambda x: x.replace('¥', ' ').replace('￥', ' ').strip()
                )
            
            df_filtered[col] = df_filtered[col].astype(str).apply(
                lambda x: self.wrap_text(x, max_text_width)
            )
        
        # 准备数据
        columns = df_filtered.columns.tolist()
        data_rows = df_filtered.values.tolist()
        table_data = [columns] + data_rows
        
        # 计算列宽度，考虑实际数据
        cell_widths = self.calculate_column_widths(columns, df_filtered)
        
        # 创建图像
        fig = plt.figure(figsize=(sum(cell_widths) + 1, self.row_height * (len(table_data) + 2)))
        ax = plt.gca()
        ax.axis('off')
        
        # 创建表格
        table = ax.table(
            cellText=data_rows,
            colLabels=columns,
            cellLoc='center',
            loc='center',
            bbox=[0.0, 0.0, 1.0, 1.0]
        )
        
        # 调整表格样式
        table.auto_set_font_size(False)
        table.set_fontsize(self.cell_fontsize)
        
        # 设置列宽
        table_width = sum(cell_widths)
        for i, width in enumerate(cell_widths):
            table.auto_set_column_width([i])
            for cell in [key for key in table._cells if key[1] == i]:
                table._cells[cell].set_width(width / table_width)
                
        # 设置行高并添加边框
        for pos, cell in table._cells.items():
            cell.set_height(self.row_height / len(table_data))
            cell.set_edgecolor('black')
            
            # 设置标题行和第一列的样式
            if pos[0] == 0:  # 标题行
                cell.set_text_props(weight='bold', color='black')
                cell.set_facecolor('#f0f8ff')  # 使用浅蓝色背景区分数据表
            else:  # 数据行
                cell.set_text_props(color='black')
                if pos[1] == 0:  # 第一列（序号）
                    cell.set_text_props(weight='bold')
                cell.set_facecolor('white')
                
            # 设置边框线宽
            cell.set_linewidth(0.75)
            # 顶部和底部边框加粗
            if pos[0] in [0, 1] or pos[0] == len(table_data):
                cell.set_linewidth(1.5)
        
        # 获取并设置表格标题
        table_title = f"{schema.get_table_title_template()} {img_index+1}"
        ax.set_title(table_title, fontsize=self.title_fontsize, pad=20)
        
        # 创建输出文件路径
        display_name = schema.get_theme_display_name()
        output_path = os.path.join(OUTPUT_DATA_TABLE_FOLDER, f"{display_name}数据表_{img_index+1}.jpg")
        
        # 确保输出文件夹存在
        if not os.path.exists(OUTPUT_DATA_TABLE_FOLDER):
            os.makedirs(OUTPUT_DATA_TABLE_FOLDER)
        
        # 保存图片
        plt.tight_layout()
        plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        return output_path

# ====================== 表格生成协调部分 ======================

class TableGenerator:
    """表格生成器，协调整个表格生成过程"""
    
    def __init__(self, output_folder=OUTPUT_FOLDER):
        """初始化表格生成器"""
        self.output_folder = output_folder
        self.data_generator = EnhancedDataGenerators()
        self.renderer = TableRenderer(output_folder)
        
        # 确保主输出文件夹和子文件夹存在
        if not os.path.exists(self.output_folder):
            os.makedirs(self.output_folder)
        if not os.path.exists(OUTPUT_THREE_LINE_FOLDER):
            os.makedirs(OUTPUT_THREE_LINE_FOLDER)
        if not os.path.exists(OUTPUT_TEXT_TABLE_FOLDER):
            os.makedirs(OUTPUT_TEXT_TABLE_FOLDER)
        if not os.path.exists(OUTPUT_NO_LINE_FOLDER):
            os.makedirs(OUTPUT_NO_LINE_FOLDER)
        if not os.path.exists(OUTPUT_ONE_DIM_FOLDER):
            os.makedirs(OUTPUT_ONE_DIM_FOLDER)
        if not os.path.exists(OUTPUT_TWO_DIM_FOLDER):
            os.makedirs(OUTPUT_TWO_DIM_FOLDER)
    
    def generate_table_data(self, schema, num_rows):
        """根据表格模式和行数生成表格数据"""
        data = {}
        theme = schema.get_theme()
        column_defs = schema.get_column_definitions()
        
        # 为每列生成数据
        for col_def in column_defs:
            data[col_def.name] = self.data_generator.generate(col_def, num_rows, theme)
        
        df = pd.DataFrame(data)
        
        # 处理特殊的计算列，如总分和排名
        for col_def in column_defs:
            if col_def.data_type == "calculated_sum":
                source_columns = col_def.generator_params.get("columns", [])
                if all(col in df.columns for col in source_columns):
                    df[col_def.name] = df[source_columns].sum(axis=1)
            elif col_def.data_type == "rank":
                based_on = col_def.generator_params.get("based_on", "")
                if based_on in df.columns:
                    # 根据指定列降序排名
                    df[col_def.name] = df[based_on].rank(method='min', ascending=False).astype(int)
        
        return df
    
    def render_table(self, df, img_index, theme, table_type="三线表"):
        """根据表格类型渲染相应的表格"""
        if table_type == "文本表":
            return self.renderer.render_text_table(df, img_index, theme)
        elif table_type == "无线表":
            return self.renderer.render_no_line_table(df, img_index, theme)
        elif table_type == "一维表":
            return self.renderer.render_one_dim_table(df, img_index, theme)
        elif table_type == "二维表":
            return self.renderer.render_two_dim_table(df, img_index, theme)
        elif table_type == "数据表":
            return self.renderer.render_data_table(df, img_index, theme)
        else:  # 默认为三线表
            return self.renderer.render_three_line_table(df, img_index, theme)
    
    def generate_tables(self, num_images=NUM_IMAGES, batch_size=100, table_types=None):
        """生成指定数量的表格图像
        
        Args:
            num_images: 每种表格类型生成的图片数量
            batch_size: 批处理大小
            table_types: 要生成的表格类型列表，如果为None则生成所有类型
        """
        # 如果未指定表格类型，则使用所有支持的类型
        if table_types is None:
            table_types = TABLE_TYPES
        elif isinstance(table_types, str):
            table_types = [table_types]  # 如果是单个字符串，转换为列表
            
        print(f"开始生成表格图像，类型: {', '.join(table_types)}")
        
        generated_images = []
        
        for table_type in table_types:
            print(f"\n开始生成{table_type}，数量: {num_images}张")
            
            # 加载配置，获取可用主题
            schema = TableSchema()
            available_themes = schema.available_themes
            
            # 批量处理
            total_batches = (num_images + batch_size - 1) // batch_size  # 向上取整
            print(f"计划生成 {num_images} 张{table_type}，分 {total_batches} 批处理")
            
            for batch in range(total_batches):
                start_idx = batch * batch_size
                end_idx = min(start_idx + batch_size, num_images)
                current_batch_size = end_idx - start_idx
                
                print(f"开始生成第 {batch+1}/{total_batches} 批，共 {current_batch_size} 张{table_type}...")
                
                # 为这一批次选择主题和创建表格模式
                schemas = []
                for _ in range(current_batch_size):
                    # 使用不同的随机种子来增加多样性
                    random.seed(datetime.datetime.now().microsecond + _)
                    if available_themes:
                        theme = random.choice(available_themes)
                        schemas.append(TableSchema(theme))
                    else:
                        schemas.append(TableSchema())  # 使用默认主题
                
                for i in range(current_batch_size):
                    idx = start_idx + i
                    schema = schemas[i]
                    theme = schema.get_theme()
                    print(f"生成{table_type} {idx+1}，主题: {theme}")
                    
                    # 随机选择行数
                    num_rows = random.randint(MIN_ROWS, MAX_ROWS)
                    print(f"选择行数: {num_rows}")
                    
                    # 生成数据
                    print("生成数据...")
                    df = self.generate_table_data(schema, num_rows)
                    
                    # 渲染表格图像
                    print(f"渲染{table_type}图像...")
                    output_path = self.render_table(df, idx, theme, table_type)
                    print(f"图像已保存: {output_path}")
                    
                    generated_images.append(output_path)
                    
                    # 每生成10张图片输出一次进度
                    if (i + 1) % 10 == 0 or (i + 1) == current_batch_size:
                        print(f"批次 {batch+1}: 已生成 {i+1}/{current_batch_size} 张{table_type}")
                
                print(f"完成第 {batch+1}/{total_batches} 批{table_type}生成")
            
            print(f"完成{table_type}生成，共 {num_images} 张")
        
        print(f"总共生成了 {len(generated_images)} 张图片")
        return generated_images
    
    def generate_one_table(self, theme=None, num_rows=None, table_type="三线表"):
        """生成单个表格，可用于测试"""
        schema = TableSchema(theme)
        
        if num_rows is None:
            num_rows = random.randint(MIN_ROWS, MAX_ROWS)
        
        df = self.generate_table_data(schema, num_rows)
        output_path = self.render_table(df, 0, schema.get_theme(), table_type)
        
        return output_path, df

# ====================== 主执行函数 ======================

def generate_three_line_tables(num_images=3, batch_size=100):
    """生成指定数量的三线表图像，支持批量处理"""
    print("Starting generate_three_line_tables")
    
    # 使用TableGenerator类来处理表格生成
    generator = TableGenerator(OUTPUT_FOLDER)
    generated_images = generator.generate_tables(num_images, batch_size, table_types=["三线表"])
    
    print(f"总共生成了 {len(generated_images)} 张三线表图片")
    return generated_images

def generate_text_tables(num_images=3, batch_size=100):
    """生成指定数量的文本表图像，支持批量处理"""
    print("Starting generate_text_tables")
    
    # 使用TableGenerator类来处理表格生成
    generator = TableGenerator(OUTPUT_FOLDER)
    generated_images = generator.generate_tables(num_images, batch_size, table_types=["文本表"])
    
    print(f"总共生成了 {len(generated_images)} 张文本表图片")
    return generated_images

def generate_no_line_tables(num_images=3, batch_size=100):
    """生成指定数量的无线表图像，支持批量处理"""
    print("Starting generate_no_line_tables")
    
    # 使用TableGenerator类来处理表格生成
    generator = TableGenerator(OUTPUT_FOLDER)
    generated_images = generator.generate_tables(num_images, batch_size, table_types=["无线表"])
    
    print(f"总共生成了 {len(generated_images)} 张无线表图片")
    return generated_images

def generate_one_dim_tables(num_images=3, batch_size=100):
    """生成指定数量的一维表图像，支持批量处理"""
    print("Starting generate_one_dim_tables")
    
    # 使用TableGenerator类来处理表格生成
    generator = TableGenerator(OUTPUT_FOLDER)
    generated_images = generator.generate_tables(num_images, batch_size, table_types=["一维表"])
    
    print(f"总共生成了 {len(generated_images)} 张一维表图片")
    return generated_images

def generate_two_dim_tables(num_images=3, batch_size=100):
    """生成指定数量的二维表图像，支持批量处理"""
    print("Starting generate_two_dim_tables")
    
    # 使用TableGenerator类来处理表格生成
    generator = TableGenerator(OUTPUT_FOLDER)
    generated_images = generator.generate_tables(num_images, batch_size, table_types=["二维表"])
    
    print(f"总共生成了 {len(generated_images)} 张二维表图片")
    return generated_images

def generate_data_tables(num_images=3, batch_size=100):
    """生成指定数量的数据表图像，支持批量处理"""
    print("Starting generate_data_tables")
    
    # 使用TableGenerator类来处理表格生成
    generator = TableGenerator(OUTPUT_FOLDER)
    generated_images = generator.generate_tables(num_images, batch_size, table_types=["数据表"])
    
    print(f"总共生成了 {len(generated_images)} 张数据表图片")
    return generated_images

def generate_all_tables(num_images=3, batch_size=100, table_types=None):
    """生成指定数量的各类表格图像，支持批量处理
    
    Args:
        num_images: 每种表格类型生成的图片数量
        batch_size: 批处理大小
        table_types: 要生成的表格类型列表，如果为None则生成所有类型
    """
    print("Starting generate_all_tables")
    
    # 使用TableGenerator类来处理表格生成
    generator = TableGenerator(OUTPUT_FOLDER)
    generated_images = generator.generate_tables(num_images, batch_size, table_types=table_types)
    
    print(f"总共生成了 {len(generated_images)} 张表格图片")
    return generated_images

# ====================== 脚本执行入口 ======================

if __name__ == "__main__":
    print("Script started")
    
    # 设置批处理大小，减少内存使用
    BATCH_SIZE = 5  # 减少批次大小便于调试
    
    print("Generating tables...")
    
    try:
        # 生成所有类型的表格
        generated_images = generate_all_tables(NUM_IMAGES, BATCH_SIZE)
        
        print(f"Successfully generated {len(generated_images)} tables")
        print(f"Tables saved in folder: {OUTPUT_FOLDER}")
        
    except Exception as e:
        print(f"Error occurred: {e}")
        import traceback
        traceback.print_exc()
    
    print("Script completed") 