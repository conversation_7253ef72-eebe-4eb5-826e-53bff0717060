personnel = {
    "display_name": "人员信息",
    "table_title_template": "人员基本信息表",
    "columns": {
        "姓名": {
            "generator_type": "faker_name",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "年龄": {
            "generator_type": "integer_range",
            "data_category": "numeric",
            "params": {
                "min": 18,
                "max": 65
            }
        },
        "性别": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "男",
                    "女",
                    "未知",
                    "保密"
                ]
            }
        },
        "部门": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "研发部",
                    "市场部",
                    "销售部",
                    "行政部",
                    "财务部",
                    "人力资源部",
                    "法务部"
                ]
            }
        },
        "职位": {
            "generator_type": "faker_job",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "入职日期": {
            "generator_type": "date_range",
            "data_category": "date",
            "params": {
                "start_year": 2010,
                "end_year": 2024,
                "format": "%Y-%m-%d"
            }
        },
        "工号": {
            "generator_type": "alphanumeric_pattern",
            "data_category": "text",
            "params": {
                "patterns": [
                    "EMP####",
                    "WK-####",
                    "P####"
                ]
            }
        },
        "联系电话": {
            "generator_type": "faker_phone_number",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "电子邮箱": {
            "generator_type": "faker_email",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "学历": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "高中",
                    "中专",
                    "大专",
                    "本科",
                    "硕士",
                    "博士",
                    "MBA"
                ]
            }
        },
        "薪资等级": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "P1",
                    "P2",
                    "P3",
                    "P4",
                    "P5",
                    "P6",
                    "M1",
                    "M2",
                    "D1",
                    "D2"
                ]
            }
        },
        "月薪": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 5000,
                "max_value": 50000,
                "format_string": "{:,}"
            }
        },
        "籍贯": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "北京",
                    "上海",
                    "广东",
                    "江苏",
                    "浙江",
                    "四川",
                    "湖北",
                    "山东",
                    "河南",
                    "河北"
                ]
            }
        },
        "紧急联系人": {
            "generator_type": "faker_name",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "绩效评级": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "A+",
                    "A",
                    "B+",
                    "B",
                    "C",
                    "D",
                    "未评级"
                ]
            }
        },
        "技能特长": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "编程",
                    "设计",
                    "营销",
                    "管理",
                    "财务分析",
                    "项目管理",
                    "外语",
                    "沟通",
                    "销售",
                    "数据分析"
                ]
            }
        },
        "身份证号": {
            "generator_type": "alphanumeric_pattern",
            "data_category": "text",
            "params": {
                "patterns": [
                    "##############X",
                    "##############0",
                    "##############1"
                ]
            }
        },
        "婚姻状况": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "未婚",
                    "已婚",
                    "离异",
                    "丧偶",
                    "保密"
                ]
            }
        },
        "户籍地址": {
            "generator_type": "faker_address",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "合同类型": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "正式",
                    "临时",
                    "实习",
                    "顾问",
                    "外包",
                    "兼职",
                    "项目制"
                ]
            }
        },
        "试用期结束": {
            "generator_type": "date_range",
            "data_category": "date",
            "params": {
                "start_year": 2023,
                "end_year": 2024,
                "format": "%Y-%m-%d"
            }
        },
        "合同到期": {
            "generator_type": "date_range",
            "data_category": "date",
            "params": {
                "start_year": 2024,
                "end_year": 2027,
                "format": "%Y-%m-%d"
            }
        },
        "负责项目": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "项目A",
                    "项目B",
                    "项目C",
                    "项目D",
                    "项目E",
                    "多项目",
                    "未分配"
                ]
            }
        },
        "工作地点": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "总部",
                    "分公司",
                    "区域办事处",
                    "远程",
                    "驻场",
                    "海外",
                    "混合"
                ]
            }
        },
        "入职渠道": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "社会招聘",
                    "校园招聘",
                    "猎头推荐",
                    "内部推荐",
                    "人才市场",
                    "招聘网站",
                    "LinkedIn"
                ]
            }
        },
        "培训记录": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "已完成",
                    "进行中",
                    "未开始",
                    "不需要",
                    "待安排",
                    "已认证",
                    "待复训"
                ]
            }
        },
        "教育背景": {
            "generator_type": "categorical_with_pattern",
            "data_category": "text",
            "params": {
                "prefixes": [
                    "北京大学",
                    "清华大学",
                    "复旦大学",
                    "浙江大学",
                    "上海交大",
                    "武汉大学",
                    "南京大学",
                    "中山大学",
                    "厦门大学",
                    "普通高校"
                ],
                "suffixes": [
                    "计算机系",
                    "经济系",
                    "管理系",
                    "法学院",
                    "医学院",
                    "工商管理",
                    "机械工程",
                    "电子信息",
                    "人文学院",
                    "理学院"
                ]
            }
        },
        "语言能力": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "英语流利",
                    "英语良好",
                    "日语N1",
                    "日语N2",
                    "韩语",
                    "法语",
                    "德语",
                    "西班牙语",
                    "多语种",
                    "中文"
                ]
            }
        },
        "加班情况": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "无加班",
                    "偶尔加班",
                    "经常加班",
                    "项目期间加班",
                    "节假日加班",
                    "高峰期加班",
                    "弹性工作",
                    "正常工作时间",
                    "远程加班",
                    "不定期"
                ]
            }
        },
        "职业规划": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "技术路线",
                    "管理路线",
                    "专家路线",
                    "创业",
                    "顾问",
                    "国际发展",
                    "学术研究",
                    "自由职业",
                    "待定",
                    "多元发展"
                ]
            }
        },
        "社保缴纳": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "五险一金",
                    "五险",
                    "三险一金",
                    "全额缴纳",
                    "基数封顶",
                    "最低基数",
                    "未缴纳",
                    "外籍社保",
                    "异地社保",
                    "补充商业保险"
                ]
            }
        },
        "年假天数": {
            "generator_type": "integer_range",
            "data_category": "numeric",
            "params": {
                "min": 5,
                "max": 20
            }
        },
        "健康状况": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "健康",
                    "良好",
                    "一般",
                    "有慢性病",
                    "需定期体检",
                    "孕期",
                    "哺乳期",
                    "术后恢复",
                    "亚健康",
                    "体弱"
                ]
            }
        },
        "办公地点": {
            "generator_type": "categorical_with_pattern",
            "data_category": "text",
            "params": {
                "prefixes": [
                    "A座",
                    "B座",
                    "C座",
                    "科技园",
                    "研发中心",
                    "总部",
                    "分部",
                    "园区"
                ],
                "suffixes": [
                    "101室",
                    "202室",
                    "303室",
                    "开放区",
                    "会议区",
                    "创新区",
                    "办公区",
                    "协作区"
                ]
            }
        },
        "绩效奖金": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 1000,
                "max_value": 50000,
                "format_string": "{:,}"
            }
        },
        "证书资质": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "PMP",
                    "CPA",
                    "律师资格证",
                    "教师资格证",
                    "医师资格证",
                    "注册工程师",
                    "软件工程师",
                    "项目管理师",
                    "人力资源管理师",
                    "无"
                ]
            }
        },
        "上次晋升日期": {
            "generator_type": "date_range",
            "data_category": "date",
            "params": {
                "start_year": 2020,
                "end_year": 2024,
                "format": "%Y-%m-%d"
            }
        },
        "下次评估日期": {
            "generator_type": "date_range",
            "data_category": "date",
            "params": {
                "start_year": 2024,
                "end_year": 2025,
                "format": "%Y-%m-%d"
            }
        },
        "效率指数": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 75,
                "max_value": 98,
                "decimals": 1,
                "format_string": "{:.1f}"
            }
        },
        "团队贡献度": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 70,
                "max_value": 100,
                "decimals": 0,
                "format_string": "{:d}分"
            }
        }
    },
    "text_columns_count": 19,
    "text_columns_names": [
        "姓名",
        "性别",
        "部门",
        "职位",
        "电子邮箱",
        "学历",
        "籍贯",
        "紧急联系人",
        "绩效评级",
        "技能特长",
        "婚姻状况",
        "户籍地址",
        "合同类型",
        "负责项目",
        "工作地点",
        "加班情况",
        "社保缴纳",
        "健康状况",
        "证书资质"
    ],
    "numeric_columns_count": 9,
    "numeric_columns_names": [
        "工号",
        "联系电话",
        "薪资等级",
        "身份证号",
        "教育背景",
        "办公地点",
        "绩效奖金",
        "效率指数",
        "团队贡献度"
    ],
    "date_columns_count": 10,
    "date_columns_names": [
        "年龄",
        "入职日期",
        "月薪",
        "试用期结束",
        "合同到期",
        "入职渠道",
        "培训记录",
        "年假天数",
        "上次晋升日期",
        "下次评估日期"
    ],
    "other_columns_count": 2,
    "other_columns_names": [
        "语言能力",
        "职业规划"
    ],
    "all_columns": [
        "姓名",
        "性别",
        "部门",
        "职位",
        "电子邮箱",
        "学历",
        "籍贯",
        "紧急联系人",
        "绩效评级",
        "技能特长",
        "婚姻状况",
        "户籍地址",
        "合同类型",
        "负责项目",
        "工作地点",
        "加班情况",
        "社保缴纳",
        "健康状况",
        "证书资质",
        "工号",
        "联系电话",
        "薪资等级",
        "身份证号",
        "教育背景",
        "办公地点",
        "绩效奖金",
        "效率指数",
        "团队贡献度",
        "年龄",
        "入职日期",
        "月薪",
        "试用期结束",
        "合同到期",
        "入职渠道",
        "培训记录",
        "年假天数",
        "上次晋升日期",
        "下次评估日期",
        "语言能力",
        "职业规划"
    ]
}

