# 批量表格生成工具

这是一个用于批量生成各种类型表格的工具，包括交叉表、平衡计分卡(BSC)等。

## 功能概述

- 生成交叉表(Cross Tables)
- 生成平衡计分卡(BSC)图表
- 支持多进程并行处理
- 基于配置文件(table_config.json)生成多样化内容

## 安装依赖

确保你已安装以下依赖:

```
pip install imgkit pandas numpy matplotlib faker
```

此外，需要安装wkhtmltopdf:
- Windows: 从[wkhtmltopdf官网](https://wkhtmltopdf.org/downloads.html)下载安装包
- Linux: `sudo apt-get install wkhtmltopdf`
- MacOS: `brew install wkhtmltopdf`

## 使用说明

### 生成平衡计分卡(BSC)图表

平衡计分卡是一种战略规划和管理工具，将组织的愿景和战略转化为一系列绩效衡量指标，从财务、客户、内部流程、学习与成长四个维度进行评估。

#### 单张BSC图表生成

在`generate_40k_tables_multiprocess.py`中，使用`generate_blocked_table`函数:

```python
from generate_40k_tables_multiprocess import generate_blocked_table

generate_blocked_table(
    central_text="企业愿景与战略：...",
    financial_headers=["财务目标", "衡量指标", "目标值", "行动方案"],
    financial_question="为取得财务上的成功...",
    customer_headers=["客户目标", "衡量指标", "目标值", "行动方案"],
    customer_question="为达成公司愿景...",
    internal_headers=["内部流程", "衡量指标", "目标值", "行动方案"],
    internal_question="为满足客户和股东的期望...",
    learning_headers=["学习与成长", "衡量指标", "目标值", "行动方案"],
    learning_question="为达成公司愿景...",
    output_filename="bsc_example.jpg",
    image_width=1800
)
```

#### 批量生成BSC图表

使用`batch_bsc_generator.py`批量生成BSC图表:

```bash
# 基本用法 - 生成10张BSC图表
python batch_bsc_generator.py -c 10

# 指定主题 - 仅生成交通主题的BSC图表
python batch_bsc_generator.py -c 5 -t transportation

# 指定输出目录
python batch_bsc_generator.py -c 5 -o ./output/bsc_charts
```

##### 命令行参数

- `-c, --count`: 要生成的BSC图表数量(默认: 30)
- `-t, --theme`: 主题过滤器，只生成包含指定字符串的主题
- `-o, --output`: 输出文件夹路径(默认: ./表格/分块表)

##### 代码调用

```python
from batch_bsc_generator import generate_batch_bsc_tables

# 生成20张BSC图表
generate_batch_bsc_tables(count=20)

# 生成5张教育主题的BSC图表
generate_batch_bsc_tables(count=5, theme_filter="education")

# 指定输出目录
generate_batch_bsc_tables(count=10, output_folder="./output/charts")
```

### BSC图表定制

#### 箭头位置调整

SVG箭头坐标系统使用百分比值定位，原点(0%,0%)在左上角:
- x值从左到右增加(0%-100%)
- y值从上到下增加(0%-100%)
- 每条线由起点(x1,y1)和终点(x2,y2)定义

**垂直箭头调整**:
- 左右移动: 修改x1和x2值(保持x1=x2以确保垂直)
- 加长/缩短: 减小/增加y1值或增加/减小y2值
- 整体上移/下移: 同时减小/增加y1和y2值

**水平箭头调整**:
- 上下移动: 修改y1和y2值(保持y1=y2以确保水平)
- 加长/缩短: 减小/增加x1值或增加/减小x2值
- 整体左移/右移: 同时减小/增加x1和x2值

更多详细的箭头调整指南请参考`generate_40k_tables_multiprocess.py`文件中的注释。

### 生成交叉表

使用`generate_40k_tables_multiprocess.py`中的`generate_cross_tables_multiprocess`函数:

```python
from generate_40k_tables_multiprocess import generate_cross_tables_multiprocess

# 生成100张交叉表
generate_cross_tables_multiprocess(100)
```

## 文件结构

- `generate_40k_tables_multiprocess.py`: 主模块，包含多进程生成交叉表和BSC图表的功能
- `batch_bsc_generator.py`: 批量生成BSC图表的专用模块
- `交叉表.py`: 交叉表生成相关函数
- `table_config.json`: 配置文件，定义各种表格的主题和列定义

## 输出目录

默认情况下，生成的表格将保存在以下目录:
- 交叉表: `./表格/交叉表/`
- 平衡计分卡: `./表格/分块表/`
- 其他类型表格: `./表格/{表格类型}/` 