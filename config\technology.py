technology = {
    "display_name": "科技创新",
    "table_title_template": "科技项目数据表",
    "columns": {
        "项目名称": {
            "generator_type": "categorical_with_pattern",
            "params": {
                "prefixes": [
                    "智能",
                    "云计算",
                    "区块链",
                    "人工智能",
                    "物联网",
                    "大数据",
                    "5G",
                    "虚拟现实",
                    "量子",
                    "机器学习",
                    "智能",
                    "云计算",
                    "区块链",
                    "人工智能",
                    "物联网",
                    "大数据",
                    "5G",
                    "虚拟现实",
                    "量子",
                    "机器学习",
                    "边缘计算",
                    "深度学习",
                    "自动驾驶",
                    "脑机接口",
                    "数字孪生",
                    "绿色能源",
                    "低碳技术",
                    "智慧城市",
                    "无人系统",
                    "生物识别"
                ],
                "suffixes": [
                    "平台",
                    "系统",
                    "解决方案",
                    "框架",
                    "技术",
                    "应用",
                    "网络",
                    "产品",
                    "生态",
                    "平台",
                    "系统",
                    "管理平台",
                    "解决方案",
                    "中台",
                    "门户",
                    "服务框架",
                    "引擎",
                    "工具集",
                    "网络",
                    "集成平台",
                    "运营平台",
                    "管控系统",
                    "开发框架",
                    "云服务",
                    "生态系统",
                    "智能体",
                    "模型库",
                    "算法平台",
                    "监测系统"
                ]
            },
            "data_category": "text"
        },
        "研发单位": {
            "generator_type": "faker_company",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "技术领域": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "信息技术",
                    "生物技术",
                    "新材料",
                    "新能源",
                    "智能制造",
                    "航空航天",
                    "医疗健康",
                    "环境保护",
                    "农业科技",
                    "海洋技术",
                    "人工智能",
                    "大数据分析",
                    "云计算",
                    "物联网",
                    "区块链",
                    "量子计算",
                    "5G通信",
                    "6G研究",
                    "虚拟现实",
                    "增强现实",
                    "混合现实",
                    "脑机接口",
                    "基因编辑",
                    "干细胞研究",
                    "合成生物学",
                    "微纳技术",
                    "光电技术",
                    "超导材料",
                    "石墨烯应用",
                    "高分子材料",
                    "生物材料",
                    "智能机器人",
                    "无人驾驶",
                    "智慧城市",
                    "智能电网",
                    "网络安全",
                    "太空探索",
                    "卫星通信",
                    "绿色能源",
                    "能源存储",
                    "智慧农业",
                    "精准医疗",
                    "远程医疗",
                    "环境监测",
                    "污染治理",
                    "循环经济",
                    "智能交通",
                    "数字孪生",
                    "边缘计算",
                    "可穿戴设备",
                    "深度学习",
                    "机器学习",
                    "强化学习",
                    "自然语言处理",
                    "计算机视觉",
                    "语音识别",
                    "生物信息学",
                    "蛋白质组学",
                    "药物发现",
                    "纳米技术",
                    "金属有机框架",
                    "复合材料",
                    "智能材料",
                    "仿生材料",
                    "氢能源",
                    "太阳能发电",
                    "风能发电",
                    "生物质能",
                    "潮汐能",
                    "地热能",
                    "核聚变",
                    "工业4.0",
                    "智能工厂",
                    "3D打印",
                    "增材制造",
                    "航天飞机",
                    "探月工程",
                    "火星探测",
                    "深空探测",
                    "卫星导航",
                    "再生医学",
                    "生物打印",
                    "免疫疗法",
                    "生物传感器",
                    "碳捕获",
                    "水处理技术",
                    "土壤修复",
                    "精准农业",
                    "垂直农业",
                    "植物工厂",
                    "海水淡化",
                    "深海采矿",
                    "海洋能源",
                    "量子通信",
                    "量子密码",
                    "量子传感"
                ]
            },
            "data_category": "text"
        },
        "研发状态": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "概念阶段",
                    "立项阶段",
                    "研发中",
                    "测试中",
                    "试运行",
                    "已完成",
                    "已商用",
                    "迭代升级",
                    "终止",
                    "可行性研究",
                    "需求分析",
                    "方案设计",
                    "原型开发",
                    "技术验证",
                    "小规模试验",
                    "大规模测试",
                    "beta测试",
                    "内部测试",
                    "用户测试",
                    "生产准备",
                    "初步上线",
                    "全面上线",
                    "市场推广",
                    "持续优化",
                    "版本迭代",
                    "项目暂停",
                    "重新规划",
                    "技术瓶颈",
                    "寻求合作",
                    "专利申请中",
                    "资金筹集中",
                    "团队组建中",
                    "项目重组",
                    "成果转化",
                    "商业化运营"
                ]
            },
            "data_category": "text"
        },
        "开始时间": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2019,
                "end_year": 2023,
                "format": "%Y-%m-%d"
            },
            "data_category": "date"
        },
        "团队人数": {
            "generator_type": "integer_range",
            "params": {
                "min": 5,
                "max": 100
            },
            "data_category": "numeric"
        },
        "预期成果": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "专利技术",
                    "软件著作权",
                    "技术标准",
                    "学术论文",
                    "产品原型",
                    "商业产品",
                    "技术报告",
                    "解决方案",
                    "开源框架",
                    "实用新型专利",
                    "外观设计专利",
                    "国际专利",
                    "核心算法",
                    "技术白皮书",
                    "研究报告",
                    "数据模型",
                    "行业标准",
                    "国家标准",
                    "国际标准",
                    "示范工程",
                    "产业应用",
                    "系统平台",
                    "应用程序",
                    "企业服务",
                    "消费级产品",
                    "工业级产品",
                    "技术咨询",
                    "技术服务",
                    "科研基地",
                    "人才培养",
                    "产学研合作",
                    "技术转让",
                    "衍生企业",
                    "产业孵化",
                    "创新生态"
                ]
            },
            "data_category": "text"
        }
    },
    "text_columns_count": 5,
    "text_columns_names": [
        "研发单位",
        "研发状态",
        "预期成果",
        "项目名称",
        "技术领域"
    ],
    "numeric_columns_count": 2,
    "numeric_columns_names": [
        "项目名称",
        "团队人数"
    ],
    "date_columns_count": 1,
    "date_columns_names": [
        "开始时间"
    ],
    "other_columns_count": 1,
    "other_columns_names": [
        "技术领域"
    ],
    "all_columns": [
        "研发单位",
        "研发状态",
        "预期成果",
        "项目名称",
        "团队人数",
        "开始时间",
        "技术领域"
    ]
}

