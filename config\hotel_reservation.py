hotel_reservation = {
    "display_name": "酒店预订",
    "table_title_template": "酒店预订信息表",
    "columns": {
        "预订编号": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "BK########",
                    "HTL######",
                    "RESV######",
                    "HB########"
                ]
            },
            "data_category": "text"
        },
        "客人姓名": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "联系电话": {
            "generator_type": "faker_phone_number",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "联系邮箱": {
            "generator_type": "faker_email",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "酒店名称": {
            "generator_type": "categorical_with_pattern",
            "params": {
                "prefixes": {
                    "type": "common_data",
                    "key": "major_cities"
                },
                "suffixes": [
                    "希尔顿酒店",
                    "万豪酒店",
                    "四季酒店",
                    "洲际酒店",
                    "凯悦酒店",
                    "丽思卡尔顿酒店",
                    "华尔道夫酒店",
                    "香格里拉酒店",
                    "喜达屋酒店",
                    "雅高酒店",
                    "文华东方酒店",
                    "瑞士酒店",
                    "威斯汀酒店",
                    "温德姆酒店",
                    "阿联酋大酒店",
                    "半岛酒店",
                    "大都会酒店",
                    "滨江花园酒店",
                    "君悦酒店",
                    "瑞吉酒店",
                    "速8酒店",
                    "汉庭酒店",
                    "如家酒店",
                    "7天酒店",
                    "格林豪泰酒店",
                    "锦江之星酒店",
                    "全季酒店",
                    "汉莎酒店",
                    "贝尔特酒店",
                    "英迪格酒店",
                    "假日酒店",
                    "凯宾斯基酒店",
                    "索菲特酒店",
                    "贝尔法斯特酒店",
                    "金陵酒店",
                    "北京饭店",
                    "上海外滩茂悦酒店",
                    "布丁酒店",
                    "优阁酒店",
                    "维也纳酒店",
                    "铂尔曼酒店",
                    "南非大酒店",
                    "澳大利亚环球酒店",
                    "丽景酒店",
                    "中信酒店",
                    "千禧大酒店",
                    "皇宫酒店",
                    "青岛海尔美酒店",
                    "圣淘沙酒店",
                    "新加坡滨海酒店",
                    "丽都酒店",
                    "苏州大酒店",
                    "杭州大酒店",
                    "广州塔楼酒店",
                    "泉州大酒店",
                    "武汉国际酒店",
                    "合肥万达酒店",
                    "澳门银河酒店",
                    "珠海长隆酒店",
                    "丽悦酒店",
                    "潮州市大酒店",
                    "平遥古城酒店",
                    "洛杉矶贝尔艾尔酒店",
                    "拉斯维加斯金沙酒店",
                    "巴厘岛梦幻酒店",
                    "东京新宿酒店",
                    "巴黎拉斐特酒店",
                    "伦敦大都会酒店",
                    "纽约曼哈顿酒店",
                    "香港迪士尼乐园酒店",
                    "大阪环球影城酒店",
                    "温哥华海滨酒店",
                    "巴塞罗那高尔夫酒店",
                    "布鲁塞尔天际酒店",
                    "西雅图滨江酒店",
                    "马尔代夫海岛度假酒店",
                    "菲律宾长滩岛酒店",
                    "哥本哈根哥本哈根塔酒店",
                    "阿姆斯特丹皇家酒店",
                    "洛杉矶海滨酒店",
                    "蒙特利湾酒店",
                    "温哥华里士满酒店",
                    "瑞士湖畔酒店",
                    "新西兰奥克兰酒店",
                    "新加坡滨海湾酒店",
                    "北京王府井大酒店",
                    "上海浦东大酒店",
                    "重庆喜来登大酒店",
                    "成都富力丽思卡尔顿酒店",
                    "南京希尔顿酒店",
                    "济南舜耕大酒店",
                    "台北福华大酒店",
                    "香港柏丽酒店",
                    "澳大利亚悉尼大酒店",
                    "东京王子大酒店",
                    "伦敦绿宝石酒店",
                    "巴塞罗那圣胡安酒店",
                    "斯里兰卡度假酒店",
                    "马德里科尔多瓦酒店",
                    "曼谷曼谷酒店",
                    "阿根廷阿尔卡酒店"
                ]
            },
            "data_category": "text"
        },
        "酒店地址": {
            "generator_type": "faker_address",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "房间类型": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "单人间",
                    "双人间",
                    "豪华单人间",
                    "豪华双人间",
                    "套房",
                    "总统套房",
                    "商务套房",
                    "家庭套房",
                    "海景房",
                    "山景房",
                    "花园房",
                    "湖景房",
                    "行政套房",
                    "蜜月套房",
                    "日式房间",
                    "标准间",
                    "高级标准间",
                    "行政楼层房",
                    "大床房",
                    "双床房",
                    "连通房",
                    "单间",
                    "经济舱房",
                    "高级经济舱房",
                    "情侣套房",
                    "亲子房",
                    "贵宾房",
                    "豪华套房",
                    "迷你套房",
                    "商务房",
                    "阁楼房",
                    "高级阁楼房",
                    "复式房",
                    "庭院房",
                    "露台房",
                    "观景房",
                    "VIP房",
                    "总统房",
                    "标准豪华间",
                    "海滨房",
                    "温泉房",
                    "按摩房",
                    "健身房",
                    "浴室套房",
                    "防噪音房",
                    "无障碍房",
                    "宠物友好房",
                    "纯净房",
                    "贵族房",
                    "网红房",
                    "超大房",
                    "豪华单人海景房",
                    "豪华双人海景房",
                    "超豪华套房",
                    "精品房",
                    "奢华房",
                    "景观房",
                    "温馨房",
                    "商务单人房",
                    "豪华家庭房",
                    "VIP海景房",
                    "天空之城房",
                    "顶楼套房",
                    "浪漫套房",
                    "情侣蜜月房",
                    "温泉套房",
                    "钢琴套房",
                    "白金套房",
                    "艺术房",
                    "经典房",
                    "舒适单人房",
                    "舒适双人房",
                    "豪华浴室房",
                    "景观观光房",
                    "天窗房",
                    "私人泳池房",
                    "别墅房",
                    "海滩别墅房",
                    "度假套房",
                    "家庭亲子房",
                    "滑雪房",
                    "高尔夫房",
                    "私人海滩房",
                    "度假村房",
                    "游艇房",
                    "瑜伽房",
                    "商务会议室房",
                    "Spa房",
                    "电影主题房",
                    "艺术工作室房",
                    "游戏房",
                    "摄影房",
                    "奢华日式房",
                    "超豪华家庭房",
                    "露营房",
                    "泳池别墅房",
                    "星空房",
                    "全景房",
                    "湖畔别墅房",
                    "创意房",
                    "未来房",
                    "极简房",
                    "复古房",
                    "摩登风格房",
                    "蓝色海岸房",
                    "阳光房",
                    "城市景观房",
                    "花园庭院房",
                    "高端会所房",
                    "民宿房",
                    "精品艺术房"
                ]
            },
            "data_category": "text"
        },
        "入住日期": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2023,
                "end_year": 2023,
                "format": "%Y-%m-%d"
            },
            "data_category": "date"
        },
        "离店日期": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2023,
                "end_year": 2023,
                "format": "%Y-%m-%d"
            },
            "data_category": "date"
        },
        "入住天数": {
            "generator_type": "integer_range",
            "params": {
                "min": 1,
                "max": 30
            },
            "data_category": "numeric"
        },
        "入住人数": {
            "generator_type": "integer_range",
            "params": {
                "min": 1,
                "max": 6
            },
            "data_category": "numeric"
        },
        "预订渠道": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "携程网",
                    "飞猪",
                    "去哪儿网",
                    "阿里旅行",
                    "艺龙",
                    "Booking.com",
                    "Agoda",
                    "Expedia",
                    "Airbnb",
                    "途牛网",
                    "同程旅游",
                    "马蜂窝",
                    "京东旅游",
                    "旅行社",
                    "酒店官网",
                    "优步",
                    "穷游网",
                    "旅游搜索引擎",
                    "携程旅行",
                    "百度旅行",
                    "拼多多旅游",
                    "途家网",
                    "美团酒店",
                    "旅游套餐平台",
                    "住客之家",
                    "驴妈妈",
                    "有米酒店",
                    "高端旅游平台",
                    "旅游代理商",
                    "专车服务",
                    "旅行达人平台",
                    "省钱预定平台",
                    "全球酒店平台",
                    "度假村官网",
                    "特色民宿平台",
                    "国际航班平台",
                    "自驾游平台",
                    "巴士预订平台",
                    "豪华旅行社",
                    "奢华度假平台",
                    "短租公寓平台",
                    "中国铁路客户服务中心",
                    "国际机票平台",
                    "火车票预订平台",
                    "在线旅游服务商",
                    "全球航班预订",
                    "本地旅行平台",
                    "国内机票平台",
                    "国际机票代理",
                    "环球邮轮平台",
                    "旅游代理商平台",
                    "跟团游平台",
                    "自助游平台",
                    "高端自驾旅游平台",
                    "城市旅游平台",
                    "个人旅游推荐平台",
                    "奢华私人定制平台",
                    "旅游论坛",
                    "度假村预定",
                    "酒店及民宿预定",
                    "全国旅游信息平台",
                    "高端客房平台",
                    "特色酒店预定",
                    "全球短租平台",
                    "旅行酒店顾问平台",
                    "在线旅游咨询平台",
                    "专业旅行顾问平台",
                    "旅行信息发布平台",
                    "景区门票预定平台",
                    "节庆活动预定平台",
                    "旅行会员平台",
                    "航空公司官网",
                    "跨国旅游平台",
                    "航班与酒店预订平台",
                    "全球旅游信息平台",
                    "旅游规划平台",
                    "国内旅游规划平台",
                    "国际旅游规划平台",
                    "旅游需求平台",
                    "出行预定平台",
                    "共享住宿平台",
                    "高端旅行体验平台",
                    "门票预订平台",
                    "旅行拍摄服务平台",
                    "个性化旅游平台",
                    "人气旅游平台",
                    "背包客平台",
                    "精品旅行网站",
                    "家庭旅游平台",
                    "商务出行平台",
                    "会议酒店预订平台",
                    "特色度假村平台",
                    "全球度假酒店平台",
                    "会员预定平台",
                    "汽车租赁平台",
                    "旅游活动预定平台",
                    "国际旅游套餐平台",
                    "家庭度假平台",
                    "酒店代理平台",
                    "自驾游网站",
                    "世界旅游组织官网",
                    "环球自驾平台",
                    "移动旅行平台",
                    "轻奢旅行平台"
                ]
            },
            "data_category": "text"
        },
        "房费": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 100,
                "max_value": 10000,
                "decimals": 0,
                "format_string": "{:d}"
            },
            "data_category": "numeric"
        },
        "总费用": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 300,
                "max_value": 50000,
                "decimals": 2,
                "format_string": "{:.2f}"
            },
            "data_category": "numeric"
        },
        "支付状态": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "已支付",
                    "未支付",
                    "部分支付",
                    "待支付",
                    "已取消",
                    "已退款",
                    "预授权"
                ]
            },
            "data_category": "text"
        },
        "支付方式": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "支付宝",
                    "微信支付",
                    "银联支付",
                    "信用卡",
                    "借记卡",
                    "PayPal",
                    "Apple Pay",
                    "Google Pay",
                    "微信钱包",
                    "支付宝钱包",
                    "Stripe",
                    "Visa卡",
                    "MasterCard",
                    "American Express",
                    "Discover",
                    "Diners Club",
                    "JCB卡",
                    "中国银行支付",
                    "招商银行支付",
                    "建设银行支付",
                    "工商银行支付",
                    "农业银行支付",
                    "交通银行支付",
                    "浦发银行支付",
                    "中信银行支付",
                    "光大银行支付",
                    "汇丰银行支付",
                    "三菱UFJ支付",
                    "贝宝支付",
                    "快捷支付",
                    "网银支付",
                    "在线支付",
                    "银行卡支付",
                    "银行卡转账",
                    "小额支付",
                    "无卡支付",
                    "二维码支付",
                    "POS机支付",
                    "分期付款",
                    "余额支付",
                    "电子钱包",
                    "虚拟货币支付",
                    "比特币支付",
                    "以太坊支付",
                    "USDT支付",
                    "Payoneer支付",
                    "银行转账",
                    "货到付款",
                    "现金支付",
                    "银联云闪付",
                    "Apple Pay Express",
                    "京东支付",
                    "美团支付",
                    "腾讯云支付",
                    "百度钱包",
                    "京东白条",
                    "蚂蚁花呗",
                    "京东支付白条",
                    "金融分期支付",
                    "京东金融",
                    "京东信用卡支付",
                    "消费积分支付",
                    "积分兑换",
                    "优惠券支付",
                    "红包支付",
                    "商家账户余额",
                    "支付宝余额宝支付",
                    "支付宝花呗分期",
                    "腾讯信用支付",
                    "滴滴支付",
                    "淘宝支付",
                    "新浪支付",
                    "58支付",
                    "支付宝账户余额",
                    "余额宝支付",
                    "京东金融账户支付",
                    "Zelle支付",
                    "Venmo支付",
                    "Android Pay",
                    "ECheck支付",
                    "FastPay支付",
                    "支付宝实时支付",
                    "微信实时支付",
                    "银联支付扫码",
                    "支付宝扫码支付",
                    "微信扫码支付",
                    "支付宝快捷支付",
                    "信用卡分期支付",
                    "京东支付",
                    "淘宝支付宝",
                    "支付宝生活号支付",
                    "餐饮支付平台",
                    "零钱支付",
                    "跨境支付",
                    "预付卡支付",
                    "客户充值支付",
                    "虚拟支付平台",
                    "国际卡支付",
                    "信用卡本地支付",
                    "数字钱包支付",
                    "信用卡在线支付",
                    "云支付平台",
                    "Tether支付",
                    "买单支付",
                    "消费积分支付",
                    "信用账户支付",
                    "ZPay支付",
                    "Face Pay支付",
                    "指纹支付",
                    "人脸识别支付"
                ]
            },
            "data_category": "text"
        },
        "预订状态": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "已确认",
                    "待确认",
                    "已取消",
                    "已完成",
                    "已入住",
                    "提前退房",
                    "未入住",
                    "延期",
                    "爽约"
                ]
            },
            "data_category": "text"
        }
    },
    "text_columns_count": 5,
    "text_columns_names": [
        "客人姓名",
        "酒店地址",
        "支付状态",
        "预订状态",
        "预订渠道"
    ],
    "numeric_columns_count": 5,
    "numeric_columns_names": [
        "预订编号",
        "联系电话",
        "酒店名称",
        "房费",
        "总费用"
    ],
    "date_columns_count": 4,
    "date_columns_names": [
        "入住日期",
        "离店日期",
        "入住天数",
        "入住人数"
    ],
    "other_columns_count": 3,
    "other_columns_names": [
        "房间类型",
        "预订渠道",
        "支付方式"
    ],
    "all_columns": [
        "客人姓名",
        "联系邮箱",
        "酒店地址",
        "支付状态",
        "预订状态",
        "预订编号",
        "联系电话",
        "酒店名称",
        "房费",
        "总费用",
        "入住日期",
        "离店日期",
        "入住天数",
        "入住人数",
        "房间类型",
        "预订渠道",
        "支付方式"
    ]
}

