bank_account = {
    "display_name": "银行账户",
    "table_title_template": "银行账户明细表",
    "columns": {
        "账户ID": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "ACC######",
                    "BA########",
                    "BANK######"
                ]
            },
            "data_category": "text"
        },
        "账户名称": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "账户类型": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "活期储蓄账户",
                    "定期储蓄账户",
                    "支票账户",
                    "信用卡账户",
                    "贷款账户",
                    "基金账户",
                    "股票账户",
                    "理财账户",
                    "企业账户",
                    "联名账户",
                    "外币账户"
                ]
            },
            "data_category": "text"
        },
        "开户银行": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "中国工商银行",
                    "中国农业银行",
                    "中国银行",
                    "中国建设银行",
                    "交通银行",
                    "招商银行",
                    "中信银行",
                    "浦发银行",
                    "民生银行",
                    "兴业银行",
                    "平安银行",
                    "华夏银行",
                    "光大银行",
                    "广发银行",
                    "邮储银行",
                    "浙商银行",
                    "渤海银行"
                ]
            },
            "data_category": "text"
        },
        "开户支行": {
            "generator_type": "categorical_with_pattern",
            "params": {
                "prefixes": {
                    "type": "common_data",
                    "key": "major_cities"
                },
                "suffixes": [
                    "分行",
                    "支行",
                    "第一支行",
                    "营业部",
                    "中心支行",
                    "科技园支行",
                    "工业区支行",
                    "城南支行",
                    "城北支行",
                    "城东支行",
                    "城西支行"
                ]
            },
            "data_category": "text"
        },
        "开户日期": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2010,
                "end_year": 2023,
                "format": "%Y-%m-%d"
            },
            "data_category": "date"
        },
        "账户状态": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "正常",
                    "冻结",
                    "挂失",
                    "止付",
                    "销户",
                    "休眠",
                    "锁定",
                    "临时冻结"
                ]
            },
            "data_category": "text"
        },
        "币种": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "人民币",
                    "美元",
                    "欧元",
                    "英镑",
                    "日元",
                    "港币",
                    "澳元",
                    "加元",
                    "瑞士法郎"
                ]
            },
            "data_category": "text"
        },
        "账户余额": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 0,
                "max_value": 1000000,
                "decimals": 2,
                "format_string": "{:,.2f}"
            },
            "data_category": "numeric"
        },
        "可用余额": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 0,
                "max_value": 1000000,
                "decimals": 2,
                "format_string": "{:,.2f}"
            },
            "data_category": "numeric"
        },
        "利率": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 0.1,
                "max_value": 5.0,
                "decimals": 2,
                "format_string": "{:.2f}%"
            },
            "data_category": "numeric"
        },
        "上次交易日期": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2023,
                "end_year": 2023,
                "format": "%Y-%m-%d"
            },
            "data_category": "date"
        },
        "账户级别": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "普通账户",
                    "金卡账户",
                    "白金账户",
                    "钻石账户",
                    "黑金账户",
                    "VIP账户",
                    "私人银行",
                    "企业账户",
                    "公司账户",
                    "个人账户"
                ]
            },
            "data_category": "text"
        },
        "年费": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 0,
                "max_value": 10000,
                "decimals": 2,
                "format_string": "{:.2f}"
            },
            "data_category": "numeric"
        }
    },
    "text_columns_count": 4,
    "text_columns_names": [
        "账户名称",
        "账户类型",
        "开户银行",
        "账户状态"
    ],
    "numeric_columns_count": 6,
    "numeric_columns_names": [
        "账户ID",
        "开户支行",
        "账户余额",
        "可用余额",
        "利率",
        "账户级别"
    ],
    "date_columns_count": 3,
    "date_columns_names": [
        "开户日期",
        "上次交易日期",
        "年费"
    ],
    "other_columns_count": 1,
    "other_columns_names": [
        "币种"
    ],
    "all_columns": [
        "账户名称",
        "账户类型",
        "开户银行",
        "账户状态",
        "账户ID",
        "开户支行",
        "账户余额",
        "可用余额",
        "利率",
        "账户级别",
        "开户日期",
        "上次交易日期",
        "年费",
        "币种"
    ]
}

