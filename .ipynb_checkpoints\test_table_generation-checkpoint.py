#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
交叉表生成测试脚本 - 用于验证生成过程
"""
import os
import time
import sys
from datetime import datetime

def test_single_process():
    """测试单进程生成"""
    from generate_40k_tables import generate_large_batch_cross_tables
    
    print("=" * 60)
    print("测试单进程交叉表生成")
    print("=" * 60)
    
    # 生成少量表格
    test_count = 10
    start_time = time.time()
    result = generate_large_batch_cross_tables(test_count)
    elapsed = time.time() - start_time
    
    print(f"单进程测试完成，生成了{result}张表格，耗时: {elapsed:.2f}秒")
    print(f"平均每张耗时: {elapsed/result if result else 0:.2f}秒")
    
    return result

def test_multi_process():
    """测试多进程生成"""
    from generate_40k_tables_multiprocess import generate_multiprocess_cross_tables
    
    print("=" * 60)
    print("测试多进程交叉表生成")
    print("=" * 60)
    
    # 生成少量表格
    test_count = 20
    start_time = time.time()
    result = generate_multiprocess_cross_tables(test_count)
    elapsed = time.time() - start_time
    
    print(f"多进程测试完成，生成了{result}张表格，耗时: {elapsed:.2f}秒")
    print(f"平均每张耗时: {elapsed/result if result else 0:.2f}秒")
    
    return result

def test_table_config_parsing():
    """测试表格配置解析"""
    from 交叉表 import load_table_config, extract_categories_from_config, extract_metrics_from_config
    
    print("=" * 60)
    print("测试表格配置解析")
    print("=" * 60)
    
    # 加载配置
    config = load_table_config()
    if not config:
        print("错误: 无法加载table_config.json")
        return False
    
    # 提取分类变量
    print("提取分类变量...")
    start_time = time.time()
    categories = extract_categories_from_config(config)
    cat_elapsed = time.time() - start_time
    
    print(f"成功提取{len(categories)}个分类变量，耗时: {cat_elapsed:.2f}秒")
    
    # 提取度量变量
    print("提取度量变量...")
    start_time = time.time()
    metrics = extract_metrics_from_config(config)
    metric_elapsed = time.time() - start_time
    
    print(f"成功提取{len(metrics)}个度量变量，耗时: {metric_elapsed:.2f}秒")
    
    # 打印主题统计
    themes = {}
    for key, cat in categories.items():
        theme = cat["theme"]
        if theme not in themes:
            themes[theme] = 0
        themes[theme] += 1
    
    print("\n主题分类变量统计:")
    for theme, count in themes.items():
        print(f"  - {theme}: {count}个分类变量")
    
    # 打印一些详细信息
    if categories:
        print("\n示例分类变量:")
        sample_key = list(categories.keys())[0]
        sample_cat = categories[sample_key]
        print(f"  - 名称: {sample_cat['name']}")
        print(f"  - 显示名称: {sample_cat['display_name']}")
        print(f"  - 主题: {sample_cat['theme']}")
        print(f"  - 取值: {sample_cat['values']}")
    
    if metrics:
        print("\n示例度量变量:")
        sample_key = list(metrics.keys())[0]
        sample_metric = metrics[sample_key]
        print(f"  - 名称: {sample_metric['name']}")
        print(f"  - 显示名称: {sample_metric['display_name']}")
        print(f"  - 主题: {sample_metric['theme']}")
        print(f"  - 最小值: {sample_metric['min_value']}")
        print(f"  - 最大值: {sample_metric['max_value']}")
    
    return len(categories) > 0 and len(metrics) > 0

def verify_output_files(expected_count):
    """验证输出文件"""
    output_folder = os.path.join("表格", "交叉表")
    
    if not os.path.exists(output_folder):
        print(f"错误: 输出目录{output_folder}不存在")
        return False
    
    # 计算生成的JPG文件数量
    jpg_files = [f for f in os.listdir(output_folder) if f.endswith('.jpg')]
    
    print("=" * 60)
    print("验证输出文件")
    print("=" * 60)
    print(f"预期生成: {expected_count}个文件")
    print(f"实际找到: {len(jpg_files)}个JPG文件")
    
    if jpg_files:
        print(f"\n示例文件:")
        for i, f in enumerate(sorted(jpg_files)[:5]):
            file_path = os.path.join(output_folder, f)
            size_kb = os.path.getsize(file_path) / 1024
            print(f"  {i+1}. {f} ({size_kb:.1f} KB)")
    
    return len(jpg_files) >= expected_count

def run_all_tests():
    """运行所有测试"""
    print("=" * 60)
    print(f"交叉表生成测试 - {datetime.now()}")
    print("=" * 60)
    
    # 测试配置解析
    config_ok = test_table_config_parsing()
    if not config_ok:
        print("配置解析测试失败，终止后续测试")
        return False
    
    # 运行单进程测试
    single_result = test_single_process()
    
    # 测试生成的文件
    files_ok = verify_output_files(single_result)
    if not files_ok:
        print("警告: 输出文件验证不通过")
    
    # 运行多进程测试
    multi_result = test_multi_process()
    
    # 最终验证
    final_check = verify_output_files(single_result + multi_result)
    
    print("=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"配置解析: {'成功' if config_ok else '失败'}")
    print(f"单进程生成: {'成功' if single_result > 0 else '失败'} ({single_result}张表格)")
    print(f"多进程生成: {'成功' if multi_result > 0 else '失败'} ({multi_result}张表格)")
    print(f"文件验证: {'成功' if final_check else '失败'}")
    
    overall_success = config_ok and single_result > 0 and multi_result > 0
    print(f"\n总体测试结果: {'成功' if overall_success else '失败'}")
    
    if overall_success:
        print("\n测试成功完成，可以开始大规模生成40,000张交叉表")
        print("建议使用多进程版本，命令:")
        print("python generate_40k_tables_multiprocess.py 40000")
    
    return overall_success

if __name__ == "__main__":
    run_all_tests() 