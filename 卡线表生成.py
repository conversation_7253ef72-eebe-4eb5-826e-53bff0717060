#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
卡线表批量生成器 - 高效并行生成工具
专注于大批量卡线表生成，支持多线程并行处理
"""

import os
import time
import random
import threading
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import List, Dict, Any
from card_line_table_generator import CardLineTableGenerator

class CustomCardLineTableGenerator:
    """自定义卡线表生成器，支持自定义文件命名和并行生成"""

    def __init__(self, output_folder: str = "我的卡线表"):
        self.output_folder = output_folder
        self.generator = CardLineTableGenerator(output_folder=output_folder)
        self._file_counter = 0
        self._lock = threading.Lock()

    def get_next_filename(self):
        """获取下一个文件名，格式为卡线表1、卡线表2等"""
        with self._lock:
            self._file_counter += 1
            return f"卡线表{self._file_counter}.jpg"

    def generate_single_card_line_table_custom(self, header_format='single'):
        """生成单个卡线表，使用自定义文件名"""
        try:
            # 确保输出文件夹存在
            if not os.path.exists(self.output_folder):
                os.makedirs(self.output_folder)

            # 使用原始生成器生成表格
            result = self.generator.generate_single_card_line_table(
                img_index=0,  # 使用固定索引，因为我们会重命名文件
                header_format=header_format
            )

            if result and result.get('success'):
                # 获取原始文件路径
                original_path = result.get('output_path')

                if not original_path or not os.path.exists(original_path):
                    return {
                        'success': False,
                        'error': f'原始文件不存在: {original_path}'
                    }

                # 验证原始文件大小
                original_size = os.path.getsize(original_path)
                if original_size < 1024:  # 文件太小，可能是空白或损坏
                    return {
                        'success': False,
                        'error': f'生成的文件过小 ({original_size} bytes)，可能是空白图像'
                    }

                # 获取自定义文件名
                custom_filename = self.get_next_filename()
                custom_output_path = os.path.join(self.output_folder, custom_filename)

                # 重命名文件
                try:
                    if os.path.exists(custom_output_path):
                        os.remove(custom_output_path)  # 删除已存在的文件
                    os.rename(original_path, custom_output_path)

                    # 验证重命名后的文件
                    if not os.path.exists(custom_output_path):
                        return {
                            'success': False,
                            'error': f'文件重命名失败: {custom_output_path}'
                        }

                    # 更新结果中的路径信息
                    result['output_path'] = custom_output_path
                    result['filename'] = custom_filename
                    result['file_size'] = os.path.getsize(custom_output_path)

                except OSError as e:
                    return {
                        'success': False,
                        'error': f'文件操作失败: {e}'
                    }
            else:
                # 原始生成失败
                error_msg = result.get('error', 'Unknown error') if result else 'No result returned'
                return {
                    'success': False,
                    'error': f'底层生成器失败: {error_msg}'
                }

            return result

        except Exception as e:
            return {
                'success': False,
                'error': f'生成过程异常: {str(e)}'
            }


class ParallelCardLineTableGenerator:
    """并行卡线表生成器"""

    def __init__(self, output_folder: str = "我的卡线表"):
        """
        初始化并行生成器

        Args:
            output_folder: 输出文件夹路径
        """
        self.output_folder = output_folder
        self.generator = CustomCardLineTableGenerator(output_folder=output_folder)
        self._lock = threading.Lock()  # 线程锁，用于文件命名同步
        self._generated_count = 0      # 已生成计数器

    def _generate_single_with_random_header(self, img_index: int, random_header: bool = True) -> Dict[str, Any]:
        """
        生成单个表格，支持随机表头格式

        Args:
            img_index: 图像索引
            random_header: 是否随机选择表头格式

        Returns:
            生成结果字典
        """
        header_format = 'single'  # 初始化默认值
        try:
            # 随机选择表头格式
            if random_header:
                header_format = random.choice(['single', 'double'])
            else:
                header_format = 'single'  # 默认单斜线

            # 生成表格 - 添加重试机制
            max_retries = 3
            result = None

            for attempt in range(max_retries):
                try:
                    result = self.generator.generate_single_card_line_table_custom(
                        header_format=header_format
                    )

                    # 验证生成结果
                    if result and result.get('success'):
                        # 检查文件是否真实存在且不为空
                        output_path = result.get('output_path')
                        if output_path and os.path.exists(output_path):
                            file_size = os.path.getsize(output_path)
                            if file_size > 1024:  # 文件大小至少1KB
                                break  # 成功生成，跳出重试循环
                            else:
                                print(f"⚠️ 警告: 生成的文件过小 ({file_size} bytes), 重试中...")
                                if os.path.exists(output_path):
                                    os.remove(output_path)  # 删除无效文件
                        else:
                            print(f"⚠️ 警告: 生成的文件不存在，重试中...")
                    else:
                        print(f"⚠️ 警告: 生成失败，重试中... 错误: {result.get('error', 'Unknown')}")

                    # 如果不是最后一次尝试，等待一小段时间再重试
                    if attempt < max_retries - 1:
                        time.sleep(0.1)

                except Exception as inner_e:
                    print(f"⚠️ 警告: 生成过程异常 (尝试 {attempt + 1}/{max_retries}): {inner_e}")
                    if attempt < max_retries - 1:
                        time.sleep(0.1)
                    else:
                        result = {
                            'success': False,
                            'error': f"重试{max_retries}次后仍失败: {inner_e}"
                        }

            # 添加表头格式信息到结果中
            if result and result.get('success'):
                result['header_format'] = header_format

                # 线程安全的计数更新
                with self._lock:
                    self._generated_count += 1
            else:
                # 确保失败结果包含必要信息
                if not result:
                    result = {'success': False, 'error': '生成结果为空'}
                result['header_format'] = header_format
                result['img_index'] = img_index

            return result

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'img_index': img_index,
                'header_format': header_format
            }

    def _generate_batch_worker(self, start_index: int, batch_size: int, random_header: bool) -> List[Dict[str, Any]]:
        """
        批量生成工作函数（单个线程执行）

        Args:
            start_index: 起始索引
            batch_size: 批量大小
            random_header: 是否随机表头格式

        Returns:
            批量生成结果列表
        """
        batch_results = []

        for i in range(batch_size):
            img_index = start_index + i
            result = self._generate_single_with_random_header(img_index, random_header)
            batch_results.append(result)

            # 简单的进度输出（线程安全）
            if result['success']:
                with self._lock:
                    print(f"✓ 线程 {threading.current_thread().name}: 完成第 {self._generated_count} 张表格")

        return batch_results

    def generate_tables_parallel(self,
                                num_images: int = 100,
                                max_workers: int = 4,
                                batch_size: int = 25,
                                random_header: bool = True) -> Dict[str, Any]:
        """
        并行生成大批量卡线表

        Args:
            num_images: 总生成数量
            max_workers: 最大并行工作线程数
            batch_size: 每批处理数量
            random_header: 是否随机分配表头格式

        Returns:
            生成结果汇总字典
        """
        print(f"🚀 开始并行生成 {num_images} 张卡线表")
        print(f"📊 配置: {max_workers} 个工作线程, 每批 {batch_size} 张")
        print(f"🎲 表头格式: {'随机分配' if random_header else '单斜线'}")
        print("=" * 60)

        start_time = time.time()
        all_results = []

        # 重置计数器
        with self._lock:
            self._generated_count = 0

        # 计算批次分配
        batches = []
        for i in range(0, num_images, batch_size):
            actual_batch_size = min(batch_size, num_images - i)
            batches.append((i, actual_batch_size))

        print(f"📦 任务分配: {len(batches)} 个批次")

        # 使用线程池执行并行生成
        with ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="CardGen") as executor:
            # 提交所有批次任务
            future_to_batch = {
                executor.submit(self._generate_batch_worker, start_idx, size, random_header): (start_idx, size)
                for start_idx, size in batches
            }

            # 收集结果
            completed_batches = 0
            for future in as_completed(future_to_batch):
                start_idx, size = future_to_batch[future]
                try:
                    batch_results = future.result()
                    all_results.extend(batch_results)
                    completed_batches += 1

                    print(f"📦 批次 {completed_batches}/{len(batches)} 完成 (索引 {start_idx}-{start_idx+size-1})")

                except Exception as e:
                    print(f"❌ 批次 {start_idx}-{start_idx+size-1} 执行异常: {e}")

        # 统计结果
        end_time = time.time()
        duration = end_time - start_time

        success_results = [r for r in all_results if r['success']]
        failed_results = [r for r in all_results if not r['success']]

        # 统计表头格式分布
        single_count = len([r for r in success_results if r.get('header_format') == 'single'])
        double_count = len([r for r in success_results if r.get('header_format') == 'double'])

        # 生成汇总报告
        summary = {
            'total_requested': num_images,
            'total_generated': len(success_results),
            'total_failed': len(failed_results),
            'success_rate': len(success_results) / num_images * 100 if num_images > 0 else 0,
            'duration_seconds': duration,
            'generation_rate': len(success_results) / duration if duration > 0 else 0,
            'header_format_distribution': {
                'single': single_count,
                'double': double_count
            },
            'parallel_config': {
                'max_workers': max_workers,
                'batch_size': batch_size,
                'random_header': random_header
            },
            'results': all_results,
            'success_results': success_results,
            'failed_results': failed_results
        }

        # 输出汇总报告
        self._print_summary_report(summary)

        return summary

    def _print_summary_report(self, summary: Dict[str, Any]):
        """打印汇总报告"""
        print("\n" + "=" * 60)
        print("📊 并行生成汇总报告")
        print("=" * 60)

        print(f"📈 生成统计:")
        print(f"  请求生成: {summary['total_requested']} 张")
        print(f"  成功生成: {summary['total_generated']} 张")
        print(f"  生成失败: {summary['total_failed']} 张")
        print(f"  成功率: {summary['success_rate']:.1f}%")

        print(f"\n⏱️ 性能统计:")
        print(f"  总耗时: {summary['duration_seconds']:.2f} 秒")
        print(f"  生成速度: {summary['generation_rate']:.2f} 张/秒")

        print(f"\n🎲 表头格式分布:")
        print(f"  单斜线表头: {summary['header_format_distribution']['single']} 张")
        print(f"  双斜线表头: {summary['header_format_distribution']['double']} 张")

        print(f"\n⚙️ 并行配置:")
        print(f"  工作线程数: {summary['parallel_config']['max_workers']}")
        print(f"  批处理大小: {summary['parallel_config']['batch_size']}")
        print(f"  随机表头: {summary['parallel_config']['random_header']}")

        if summary['failed_results']:
            print(f"\n❌ 失败详情:")
            for failed in summary['failed_results'][:5]:  # 只显示前5个失败
                print(f"  索引 {failed.get('img_index', 'N/A')}: {failed.get('error', 'Unknown error')}")
            if len(summary['failed_results']) > 5:
                print(f"  ... 还有 {len(summary['failed_results']) - 5} 个失败项")

        print(f"\n📁 输出目录: {self.output_folder}")
        print("=" * 60)


def generate_card_line_tables_batch(num_images=100, max_workers=4, batch_size=25,
                                   output_folder="我的卡线表", random_header=True):
    """
    批量生成卡线表的主函数

    Args:
        num_images: 生成图像数量
        max_workers: 最大并行工作线程数
        batch_size: 每批处理数量
        output_folder: 输出文件夹
        random_header: 是否随机分配表头格式

    Returns:
        生成结果汇总字典
    """
    print(f"🚀 开始批量生成 {num_images} 张卡线表")
    print(f"📊 配置: {max_workers} 个工作线程, 每批 {batch_size} 张")
    print(f"🎲 表头格式: {'随机分配' if random_header else '单斜线'}")
    print(f"📁 输出目录: {output_folder}")
    print("=" * 60)

    # 创建并行生成器
    parallel_generator = ParallelCardLineTableGenerator(output_folder=output_folder)

    # 执行并行生成
    results = parallel_generator.generate_tables_parallel(
        num_images=num_images,
        max_workers=max_workers,
        batch_size=batch_size,
        random_header=random_header
    )

    return results


if __name__ == "__main__":
    print("🎯 卡线表批量生成器")
    print("=" * 60)

    # 默认配置生成
    results = generate_card_line_tables_batch(
        num_images=10,        # 生成1100张图像
        max_workers=8,          # 8个并行线程
        batch_size=25,          # 每批25张
        output_folder="我的卡线表",
        random_header=True      # 随机表头格式
    )

    print("\n🎉 批量生成完成！")
    print(f"📊 请求生成: {results['total_requested']} 张")
    print(f"📊 成功生成: {results['total_generated']} 张")
    print(f"❌ 生成失败: {results['total_failed']} 张")
    print(f"📈 成功率: {results['success_rate']:.1f}%")
    print(f"⚡ 处理速度: {results['generation_rate']:.2f} 张/秒")
    print(f"📁 输出目录: 我的卡线表")

    # 如果有失败的情况，显示详细信息
    if results['total_failed'] > 0:
        print(f"\n⚠️ 失败原因分析:")
        for i, failed in enumerate(results['failed_results'][:3]):
            print(f"  {i+1}. {failed.get('error', 'Unknown error')}")

    print("\n💡 提示: 可修改参数进行自定义批量生成")