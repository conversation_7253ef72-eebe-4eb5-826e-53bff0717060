residence_data = {
    "display_name": "住宿数据",
    "table_title_template": "住宿统计表",
    "columns": {
        "入住天数": {
            "generator_type": "integer_range",
            "params": {
                "min": 1,
                "max": 30
            },
            "data_category": "numeric"
        },
        "房间号": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "A###",
                    "B###",
                    "C###",
                    "D###"
                ]
            },
            "data_category": "other"
        },
        "住客姓名": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "入住时间": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2023,
                "end_year": 2024,
                "format": "%Y-%m-%d"
            },
            "data_category": "date"
        },
        "房间类型": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "标准间",
                    "大床房",
                    "豪华套房",
                    "家庭房",
                    "行政套房"
                ]
            },
            "data_category": "text"
        },
        "房费": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 199,
                "max_value": 1999,
                "format_string": "¥{}"
            },
            "data_category": "numeric"
        },
        "押金": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 200,
                "max_value": 1000,
                "format_string": "¥{}"
            },
            "data_category": "numeric"
        },
        "状态": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "已入住",
                    "已退房",
                    "已预订",
                    "已取消"
                ]
            },
            "data_category": "text"
        },
        "备注": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "无",
                    "需要加床",
                    "特殊需求",
                    "延迟退房",
                    "提前入住",
                    "常客"
                ]
            },
            "data_category": "text"
        }
    },
    "text_columns_count": 4,
    "text_columns_names": [
        "住客姓名",
        "房间类型",
        "状态",
        "备注"
    ],
    "numeric_columns_count": 3,
    "numeric_columns_names": [
        "房费",
        "押金",
        "入住天数"
    ],
    "date_columns_count": 1,
    "date_columns_names": [
        "入住时间"
    ],
    "other_columns_count": 1,
    "other_columns_names": [
        "房间号"
    ],
    "all_columns": [
        "住客姓名",
        "房间类型",
        "状态",
        "备注",
        "房间号",
        "房费",
        "押金",
        "入住天数",
        "入住时间"
    ]
}

