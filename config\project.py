project = {
    "display_name": "项目管理",
    "table_title_template": "项目任务管理表",
    "columns": {
        "项目名称": {
            "generator_type": "categorical_with_pattern",
            "data_category": "text",
            "params": {
                "prefixes": [
                    "智能",
                    "数字化",
                    "云平台",
                    "大数据",
                    "AI",
                    "移动",
                    "在线",
                    "物联网",
                    "区块链",
                    ""
                ],
                "suffixes": [
                    "系统开发",
                    "平台升级",
                    "数据分析",
                    "用户研究",
                    "应用开发",
                    "解决方案",
                    "营销推广",
                    "基础设施建设"
                ]
            }
        },
        "项目编号": {
            "generator_type": "alphanumeric_pattern",
            "data_category": "text",
            "params": {
                "patterns": [
                    "PRJ-####-##",
                    "P####",
                    "PROJ######"
                ]
            }
        },
        "任务ID": {
            "generator_type": "alphanumeric_pattern",
            "data_category": "text",
            "params": {
                "patterns": [
                    "TASK-###",
                    "T####",
                    "TID-######"
                ]
            }
        },
        "任务名称": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "需求分析",
                    "系统设计",
                    "UI设计",
                    "前端开发",
                    "后端开发",
                    "数据库设计",
                    "测试",
                    "部署",
                    "文档编写",
                    "培训",
                    "项目管理",
                    "质量控制"
                ]
            }
        },
        "任务描述": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "完成用户需求收集",
                    "设计系统架构",
                    "开发核心功能模块",
                    "修复已知bug",
                    "优化性能",
                    "撰写技术文档",
                    "进行用户测试",
                    "部署测试环境",
                    "与客户沟通确认需求",
                    "评审代码质量"
                ]
            }
        },
        "优先级": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "高",
                    "中",
                    "低",
                    "紧急",
                    "非常高",
                    "次要",
                    "可选"
                ]
            }
        },
        "状态": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "未开始",
                    "进行中",
                    "已完成",
                    "已延期",
                    "已取消",
                    "待评审",
                    "待测试",
                    "待部署",
                    "已关闭"
                ]
            }
        },
        "计划开始日期": {
            "generator_type": "date_range",
            "data_category": "date",
            "params": {
                "start_year": 2023,
                "end_year": 2024,
                "format": "%Y-%m-%d"
            }
        },
        "计划结束日期": {
            "generator_type": "date_range",
            "data_category": "date",
            "params": {
                "start_year": 2023,
                "end_year": 2025,
                "format": "%Y-%m-%d"
            }
        },
        "实际开始日期": {
            "generator_type": "date_range",
            "data_category": "date",
            "params": {
                "start_year": 2023,
                "end_year": 2024,
                "format": "%Y-%m-%d"
            }
        },
        "实际结束日期": {
            "generator_type": "date_range",
            "data_category": "date",
            "params": {
                "start_year": 2023,
                "end_year": 2025,
                "format": "%Y-%m-%d"
            }
        },
        "负责人": {
            "generator_type": "faker_name",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "团队成员": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "张工,李工",
                    "王工,赵工,钱工",
                    "孙工,周工",
                    "吴工,郑工,冯工",
                    "陈工,褚工",
                    "魏工,蒋工,沈工,韩工",
                    "杨工,朱工",
                    "秦工,许工"
                ]
            }
        },
        "工时估计": {
            "generator_type": "integer_range_with_unit",
            "data_category": "numeric",
            "params": {
                "min": 1,
                "max": 100,
                "unit": "小时"
            }
        },
        "实际工时": {
            "generator_type": "integer_range_with_unit",
            "data_category": "numeric",
            "params": {
                "min": 1,
                "max": 120,
                "unit": "小时"
            }
        },
        "完成百分比": {
            "generator_type": "percentage",
            "data_category": "numeric",
            "params": {
                "min_value": 0,
                "max_value": 100,
                "decimals": 0
            }
        },
        "关联任务": {
            "generator_type": "alphanumeric_pattern",
            "data_category": "text",
            "params": {
                "patterns": [
                    "TASK-###",
                    "T####",
                    "无"
                ]
            }
        },
        "风险级别": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "高",
                    "中",
                    "低",
                    "无风险"
                ]
            }
        },
        "备注": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "需要加急处理",
                    "与客户进一步确认需求",
                    "等待其他团队协助",
                    "技术难点需要攻克",
                    "资源配置不足",
                    "进展顺利",
                    "已提前完成",
                    "质量有待提高",
                    "需要更多测试"
                ]
            }
        },
        "所属迭代": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "Sprint 1",
                    "Sprint 2",
                    "Sprint 3",
                    "迭代1",
                    "迭代2",
                    "迭代3",
                    "版本1.0",
                    "版本2.0",
                    "MVP阶段",
                    "产品验证阶段"
                ]
            }
        },
        "项目预算": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 10000,
                "max_value": 1000000,
                "format_string": "{:,}"
            }
        },
        "项目类型": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "内部项目",
                    "外包项目",
                    "研发项目",
                    "维护项目",
                    "升级项目",
                    "整合项目",
                    "试点项目",
                    "战略项目",
                    "创新项目",
                    "商业化项目"
                ]
            }
        },
        "客户名称": {
            "generator_type": "faker_company",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "项目进度": {
            "generator_type": "percentage",
            "data_category": "numeric",
            "params": {
                "min_value": 0,
                "max_value": 100,
                "decimals": 0
            }
        },
        "技术栈": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "Java/Spring",
                    "Python/Django",
                    "JavaScript/React",
                    "PHP/Laravel",
                    "C#/.NET",
                    "Ruby on Rails",
                    "Node.js/Express",
                    "Flutter/Dart",
                    "Go/Gin",
                    "Vue.js/Nuxt"
                ]
            }
        },
        "预期成果": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "系统上线",
                    "功能完善",
                    "性能提升",
                    "用户满意度提高",
                    "成本降低",
                    "流程优化",
                    "数据整合",
                    "平台迁移",
                    "代码重构",
                    "安全性提升"
                ]
            }
        },
        "质量评级": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "A+",
                    "A",
                    "B+",
                    "B",
                    "C",
                    "需改进",
                    "合格",
                    "优秀",
                    "不合格",
                    "超出预期"
                ]
            }
        },
        "会议频率": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "每日站会",
                    "每周例会",
                    "双周会议",
                    "月度汇报",
                    "需求驱动",
                    "临时会议",
                    "里程碑会议",
                    "远程协作",
                    "不定期沟通",
                    "按需会议"
                ]
            }
        },
        "部署环境": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "开发环境",
                    "测试环境",
                    "预发布环境",
                    "生产环境",
                    "本地环境",
                    "云服务",
                    "混合云",
                    "私有云",
                    "容器化环境",
                    "微服务架构"
                ]
            }
        },
        "资源需求": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "前端开发2人",
                    "后端开发3人",
                    "UI设计1人",
                    "测试2人",
                    "产品经理1人",
                    "DevOps工程师",
                    "数据分析师",
                    "全栈开发3人",
                    "项目经理1人",
                    "技术顾问"
                ]
            }
        },
        "开发成本": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 5000,
                "max_value": 100000,
                "format_string": "{:,}/月"
            }
        },
        "上线日期": {
            "generator_type": "date_range",
            "data_category": "date",
            "params": {
                "start_year": 2023,
                "end_year": 2025,
                "format": "%Y-%m-%d"
            }
        }
    },
    "text_columns_count": 11,
    "text_columns_names": [
        "任务名称",
        "任务描述",
        "优先级",
        "状态",
        "负责人",
        "团队成员",
        "备注",
        "项目类型",
        "客户名称",
        "质量评级",
        "部署环境"
    ],
    "numeric_columns_count": 12,
    "numeric_columns_names": [
        "项目名称",
        "项目编号",
        "任务ID",
        "工时估计",
        "实际工时",
        "完成百分比",
        "关联任务",
        "风险级别",
        "项目预算",
        "项目进度",
        "会议频率",
        "开发成本"
    ],
    "date_columns_count": 5,
    "date_columns_names": [
        "计划开始日期",
        "计划结束日期",
        "实际开始日期",
        "实际结束日期",
        "上线日期"
    ],
    "other_columns_count": 4,
    "other_columns_names": [
        "所属迭代",
        "技术栈",
        "预期成果",
        "资源需求"
    ],
    "all_columns": [
        "任务名称",
        "任务描述",
        "优先级",
        "状态",
        "负责人",
        "团队成员",
        "备注",
        "项目类型",
        "客户名称",
        "质量评级",
        "部署环境",
        "项目名称",
        "项目编号",
        "任务ID",
        "工时估计",
        "实际工时",
        "完成百分比",
        "关联任务",
        "风险级别",
        "项目预算",
        "项目进度",
        "会议频率",
        "开发成本",
        "计划开始日期",
        "计划结束日期",
        "实际开始日期",
        "实际结束日期",
        "上线日期",
        "所属迭代",
        "技术栈",
        "预期成果",
        "资源需求"
    ]
}

