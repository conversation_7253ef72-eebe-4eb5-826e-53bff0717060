user_info = {
    "display_name": "用户信息",
    "table_title_template": "用户基本信息表",
    "columns": {
        "用户ID": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "UID####",
                    "U######",
                    "USER_###",
                    "ID_#####"
                ]
            },
            "data_category": "text"
        },
        "用户名": {
            "generator_type": "categorical_with_pattern",
            "params": {
                "prefixes": [
                    "happy",
                    "cool",
                    "super",
                    "crazy",
                    "lovely",
                    "funny",
                    "smart",
                    "bright",
                    "quick",
                    "clever"
                ],
                "suffixes": [
                    "user",
                    "person",
                    "star",
                    "gamer",
                    "reader",
                    "writer",
                    "player",
                    "fan",
                    "friend",
                    "smile"
                ]
            },
            "data_category": "text"
        },
        "真实姓名": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "性别": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "男",
                    "女",
                    "未知",
                    "保密"
                ]
            },
            "data_category": "text"
        },
        "年龄": {
            "generator_type": "integer_range",
            "params": {
                "min": 12,
                "max": 70
            },
            "data_category": "numeric"
        },
        "手机号": {
            "generator_type": "faker_phone_number",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "邮箱": {
            "generator_type": "faker_email",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "注册时间": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2015,
                "end_year": 2023,
                "format": "%Y-%m-%d %H:%M:%S"
            },
            "data_category": "date"
        },
        "最后登录时间": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2023,
                "end_year": 2023,
                "format": "%Y-%m-%d %H:%M:%S"
            },
            "data_category": "date"
        },
        "会员等级": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "普通会员",
                    "青铜会员",
                    "白银会员",
                    "黄金会员",
                    "铂金会员",
                    "钻石会员",
                    "VIP会员"
                ]
            },
            "data_category": "text"
        },
        "账户状态": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "正常",
                    "已冻结",
                    "已封禁",
                    "待验证",
                    "休眠",
                    "注销中",
                    "已注销"
                ]
            },
            "data_category": "text"
        },
        "注册来源": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "官网",
                    "安卓APP",
                    "iOS APP",
                    "微信小程序",
                    "微博",
                    "QQ",
                    "邮箱",
                    "手机号",
                    "Facebook",
                    "Twitter",
                    "邀请注册",
                    "活动注册"
                ]
            },
            "data_category": "text"
        }
    },
    "text_columns_count": 6,
    "text_columns_names": [
        "真实姓名",
        "性别",
        "邮箱",
        "账户状态",
        "用户名",
        "会员等级",
        "注册来源"
    ],
    "numeric_columns_count": 4,
    "numeric_columns_names": [
        "用户ID",
        "用户名",
        "手机号",
        "会员等级"
    ],
    "date_columns_count": 4,
    "date_columns_names": [
        "年龄",
        "注册时间",
        "最后登录时间",
        "注册来源"
    ],
    "other_columns_count": 0,
    "other_columns_names": [],
    "all_columns": [
        "真实姓名",
        "性别",
        "邮箱",
        "账户状态",
        "用户ID",
        "用户名",
        "手机号",
        "会员等级",
        "年龄",
        "注册时间",
        "最后登录时间",
        "注册来源"
    ]
}

