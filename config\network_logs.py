network_logs = {
    "display_name": "网络访问日志",
    "table_title_template": "网络访问日志数据表",
    "columns": {
        "日志ID": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "LOG########",
                    "NL-####-####",
                    "NET######",
                    "ACCESS######"
                ]
            },
            "data_category": "text"
        },
        "时间戳": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2023,
                "end_year": 2023,
                "format": "%Y-%m-%d %H:%M:%S.%f"
            },
            "data_category": "date"
        },
        "请求方法": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "GET",
                    "POST",
                    "PUT",
                    "DELETE",
                    "PATCH",
                    "HEAD",
                    "OPTIONS"
                ]
            },
            "data_category": "text"
        },
        "URL路径": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "/api/users",
                    "/api/products",
                    "/api/orders",
                    "/api/login",
                    "/api/logout",
                    "/api/auth",
                    "/api/profile",
                    "/api/settings",
                    "/api/dashboard",
                    "/api/search",
                    "/home",
                    "/about",
                    "/contact",
                    "/products",
                    "/services",
                    "/blog",
                    "/news",
                    "/cart",
                    "/checkout",
                    "/payment",
                    "/success",
                    "/error",
                    "/404",
                    "/admin"
                ]
            },
            "data_category": "text"
        },
        "备注": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "无",
                    "重要",
                    "紧急",
                    "待跟进",
                    "已完成",
                    "已取消"
                ]
            },
            "data_category": "text"
        },
           "状态码": {
        "generator_type": "categorical",
        "params": {
            "values": [
                "200",
                "201",
                "204",
                "301",
                "302",
                "304",
                "400",
                "401",
                "403",
                "404",
                "405",
                "408",
                "422",
                "429",
                "500",
                "502",
                "503",
                "504"
            ]
        },
        "data_category": "text"
    },
    "响应时间": {
        "generator_type": "numerical_range_formatted",
        "params": {
            "min_value": 5,
            "max_value": 10000,
            "format_string": "{:,}ms"
        },
        "data_category": "numeric"
    },
    "IP地址": {
        "generator_type": "categorical",
        "params": {
            "values": [
                "***********",
                "********",
                "**********",
                "127.0.0.1",
                "*******",
                "***************",
                "*********",
                "*********",
                "*******",
                "*******",
                "**************",
                "**************",
                "***************",
                "***************",
                "***********",
                "**************"
            ]
        },
        "data_category": "text"
    },
    "用户代理": {
        "generator_type": "categorical",
        "params": {
            "values": [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
                "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
                "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
                "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36"
            ]
        },
        "data_category": "text"
    },
    "引用页": {
        "generator_type": "categorical",
        "params": {
            "values": [
                "https://www.google.com/",
                "https://www.baidu.com/",
                "https://www.bing.com/",
                "https://www.example.com/",
                "https://www.facebook.com/",
                "https://twitter.com/",
                "https://www.instagram.com/",
                "https://www.linkedin.com/",
                "https://www.youtube.com/",
                "https://www.zhihu.com/",
                "https://weibo.com/",
                "https://www.douyin.com/",
                "https://www.qq.com/",
                "https://www.163.com/",
                "direct",
                "none"
            ]
        },
        "data_category": "text"
    },
    "用户ID": {
        "generator_type": "alphanumeric_pattern",
        "params": {
            "patterns": [
                "UID####",
                "USER####",
                "U######"
            ]
        },
        "data_category": "text"
    },
    "会话ID": {
        "generator_type": "alphanumeric_pattern",
        "params": {
            "patterns": [
                "SID-####-####-####",
                "SESSION######",
                "S-####-####"
            ]
        },
        "data_category": "text"
    },
    "数据传输量": {
        "generator_type": "numerical_range_formatted",
        "params": {
            "min_value": 1,
            "max_value": 10000,
            "format_string": "{:,}KB"
        },
        "data_category": "numeric"
    },
    "设备类型": {
        "generator_type": "categorical",
        "params": {
            "values": [
                "Desktop",
                "Mobile",
                "Tablet",
                "Smart TV",
                "Game Console",
                "Wearable",
                "Bot",
                "Unknown"
            ]
        },
        "data_category": "text"
    },
    "操作系统": {
        "generator_type": "categorical",
        "params": {
            "values": [
                "Windows 10",
                "Windows 11",
                "macOS",
                "iOS",
                "Android",
                "Linux",
                "Chrome OS",
                "Ubuntu",
                "Debian",
                "CentOS",
                "Windows 7",
                "Windows 8.1",
                "Not Detected"
            ]
        },
        "data_category": "text"
    },
    "浏览器": {
        "generator_type": "categorical",
        "params": {
            "values": [
                "Chrome",
                "Firefox",
                "Safari",
                "Edge",
                "Opera",
                "Internet Explorer",
                "Samsung Browser",
                "UC Browser",
                "QQ Browser",
                "Baidu Browser",
                "Yandex Browser",
                "Unknown"
            ]
        },
        "data_category": "text"
    },
    "text_columns_count": 5,
    "text_columns_names": [
        "请求方法",
        "备注",
        "设备类型",
        "操作系统",
        "浏览器"
    ],
    "numeric_columns_count": 0,
    "numeric_columns_names": [],
    "date_columns_count": 2,
    "date_columns_names": [
        "日志ID",
        "时间戳"
    ],
    "other_columns_count": 1,
    "other_columns_names": [
        "URL路径"
    ],
    "all_columns": [
        "请求方法",
        "备注",
        "日志ID",
        "时间戳",
        "URL路径"
    ]
}
}
