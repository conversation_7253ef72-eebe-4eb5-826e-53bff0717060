education_institution = {
    "display_name": "教育机构",
    "table_title_template": "教育机构数据表",
    "columns": {
        "机构名称": {
            "generator_type": "categorical_with_pattern",
            "params": {
                "prefixes": [
                    "北京",
                    "上海",
                    "广州",
                    "深圳",
                    "杭州",
                    "成都",
                    "武汉",
                    "南京",
                    "西安",
                    "重庆",
                    "新东方",
                    "学而思",
                    "环球",
                    "启德",
                    "博思"
                ],
                "suffixes": [
                    "教育",
                    "培训中心",
                    "学院",
                    "进修学校",
                    "教育集团",
                    "辅导班",
                    "教育研究院",
                    "国际学校",
                    "少儿英语",
                    "学习中心"
                ]
            },
            "data_category": "text"
        },
        "机构类型": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "K12培训",
                    "语言培训",
                    "国际教育",
                    "职业培训",
                    "考研培训",
                    "IT培训",
                    "艺术培训",
                    "兴趣培训",
                    "体育培训",
                    "早教中心",
                    "留学机构",
                    "高考补习",
                    "幼儿园",
                    "私立学校",
                    "公立学校",
                    "在线教育"
                ]
            },
            "data_category": "text"
        },
        "成立时间": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2000,
                "end_year": 2022,
                "format": "%Y-%m-%d"
            },
            "data_category": "date"
        },
        "校区数量": {
            "generator_type": "integer_range",
            "params": {
                "min": 1,
                "max": 500
            },
            "data_category": "numeric"
        },
        "师资规模": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "10人以下",
                    "10-50人",
                    "50-100人",
                    "100-300人",
                    "300-500人",
                    "500-1000人",
                    "1000人以上"
                ]
            },
            "data_category": "text"
        },
        "学员人数": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 100,
                "max_value": 1000000,
                "format_string": "{:,}人"
            },
            "data_category": "numeric"
        },
        "主要课程": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "英语",
                    "数学",
                    "语文",
                    "物理",
                    "化学",
                    "生物",
                    "编程",
                    "美术",
                    "音乐",
                    "舞蹈",
                    "体育",
                    "历史",
                    "地理",
                    "政治",
                    "雅思",
                    "托福",
                    "SAT",
                    "AP课程",
                    "IB课程",
                    "A-Level",
                    "留学申请"
                ]
            },
            "data_category": "text"
        },
        "平均班级人数": {
            "generator_type": "integer_range",
            "params": {
                "min": 5,
                "max": 50
            },
            "data_category": "numeric"
        },
        "教学模式": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "小班教学",
                    "一对一",
                    "双师模式",
                    "在线直播",
                    "录播课程",
                    "混合式教学",
                    "沉浸式教学",
                    "项目制学习",
                    "实践教学",
                    "游戏化教学",
                    "翻转课堂"
                ]
            },
            "data_category": "text"
        },
        "地址": {
            "generator_type": "faker_address",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "联系人": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "联系电话": {
            "generator_type": "faker_phone_number",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "邮箱": {
            "generator_type": "faker_email",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "课程费用": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 1000,
                "max_value": 50000,
                "format_string": "{:,}"
            },
            "data_category": "numeric"
        },
        "学员满意度": {
            "generator_type": "percentage",
            "params": {
                "min_value": 70,
                "max_value": 99,
                "decimals": 1
            },
            "data_category": "numeric"
        },
        "备注": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "无",
                    "重要",
                    "紧急",
                    "待跟进",
                    "已完成",
                    "已取消"
                ]
            },
            "data_category": "text"
        }
    },
    "text_columns_count": 10,
    "text_columns_names": [
        "机构名称",
        "机构类型",
        "师资规模",
        "主要课程",
        "教学模式",
        "地址",
        "联系人",
        "联系电话",
        "邮箱",
        "备注"
    ],
    "numeric_columns_count": 5,
    "numeric_columns_names": [
        "校区数量",
        "学员人数",
        "平均班级人数",
        "课程费用",
        "学员满意度"
    ],
    "date_columns_count": 1,
    "date_columns_names": [
        "成立时间"
    ],
    "other_columns_count": 0,
    "other_columns_names": [],
    "all_columns": [
        "机构名称",
        "机构类型",
        "师资规模",
        "主要课程",
        "教学模式",
        "地址",
        "联系人",
        "联系电话",
        "邮箱",
        "备注",
        "校区数量",
        "学员人数",
        "平均班级人数",
        "课程费用",
        "学员满意度",
        "成立时间"
    ]
}

