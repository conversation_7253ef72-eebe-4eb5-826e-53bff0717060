environment = {
    "display_name": "环境数据",
    "table_title_template": "环境监测数据表",
    "columns": {
        "监测项目": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "空气质量",
                    "水质",
                    "噪声",
                    "辐射",
                    "土壤",
                    "固体废物",
                    "森林覆盖率",
                    "碳排放",
                    "温室气体",
                    "绿化率",
                    "生物多样性"
                ]
            }
        },
        "监测地点": {
            "generator_type": "categorical_with_pattern",
            "data_category": "text",
            "params": {
                "prefixes": {
                    "type": "common_data",
                    "key": "major_cities"
                },
                "suffixes": [
                    "中心城区",
                    "工业园区",
                    "生态保护区",
                    "水源地",
                    "居民区",
                    "郊区",
                    "农业区"
                ]
            }
        },
        "监测时间": {
            "generator_type": "date_range",
            "data_category": "date",
            "params": {
                "start_year": 2020,
                "end_year": 2023,
                "format": "%Y-%m-%d"
            }
        },
        "污染程度": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "轻度污染",
                    "中度污染",
                    "重度污染",
                    "严重污染",
                    "无污染",
                    "临界值",
                    "达标",
                    "超标"
                ]
            }
        },
        "指标值": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 0,
                "max_value": 500,
                "decimals": 2,
                "format_string": "{:.2f}"
            }
        },
        "执行标准": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "国家标准",
                    "行业标准",
                    "地方标准",
                    "企业标准",
                    "国际标准",
                    "欧盟标准",
                    "美国标准",
                    "日本标准"
                ]
            }
        },
        "治理措施": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "源头控制",
                    "过程管理",
                    "末端治理",
                    "综合治理",
                    "生态修复",
                    "清洁生产",
                    "循环利用",
                    "监测预警"
                ]
            }
        },
        "治理效果": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "显著改善",
                    "逐步改善",
                    "局部改善",
                    "无明显改善",
                    "持续恶化",
                    "反复波动",
                    "长期稳定",
                    "季节性变化"
                ]
            }
        },
        "负责单位": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "环保局",
                    "生态环境局",
                    "水务局",
                    "林业局",
                    "农业农村局",
                    "住建局",
                    "规划局",
                    "自然资源局",
                    "气象局"
                ]
            }
        },
        "投入资金": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 100,
                "max_value": 10000,
                "format_string": "{:,}万"
            }
        },
        "环境容量": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 1000,
                "max_value": 100000,
                "format_string": "{:,}吨"
            }
        },
        "监测频率": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "实时监测",
                    "每日监测",
                    "每周监测",
                    "每月监测",
                    "季度监测",
                    "半年监测",
                    "年度监测",
                    "不定期监测"
                ]
            }
        }
    },
    "text_columns_count": 8,
    "text_columns_names": [
        "监测项目",
        "治理措施",
        "治理效果",
        "负责单位",
        "监测地点",
        "污染程度",
        "执行标准",
        "治理措施",
        "监测频率"
    ],
    "numeric_columns_count": 5,
    "numeric_columns_names": [
        "监测地点",
        "指标值",
        "投入资金",
        "环境容量",
        "监测频率"
    ],
    "date_columns_count": 2,
    "date_columns_names": [
        "监测时间",
        "执行标准"
    ],
    "other_columns_count": 1,
    "other_columns_names": [
        "污染程度"
    ],
    "all_columns": [
        "监测项目",
        "治理措施",
        "治理效果",
        "负责单位",
        "监测地点",
        "指标值",
        "投入资金",
        "环境容量",
        "监测频率",
        "监测时间",
        "执行标准",
        "污染程度"
    ]
}

