library_lending = {
    "display_name": "图书馆借阅",
    "table_title_template": "图书馆借阅记录表",
    "columns": {
        "借阅编号": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "LIB########",
                    "BOR######",
                    "LB########",
                    "L########"
                ]
            },
            "data_category": "text"
        },
        "读者ID": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "R####",
                    "RD####",
                    "REA######",
                    "USER####"
                ]
            },
            "data_category": "text"
        },
        "读者姓名": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "读者类型": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "本科生",
                    "研究生",
                    "博士生",
                    "教师",
                    "职工",
                    "校外读者",
                    "VIP读者",
                    "特殊读者",
                    "访问学者",
                    "校友",
                    "社会读者"
                ]
            },
            "data_category": "text"
        },
        "图书ISBN": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "978-7-####-####-#",
                    "978-0-###-#####-#",
                    "979-1-####-####-#"
                ]
            },
            "data_category": "text"
        },
        "图书名称": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "人工智能导论",
                    "数据结构与算法分析",
                    "计算机网络原理",
                    "数据库系统概念",
                    "操作系统设计与实现",
                    "软件工程实践",
                    "机器学习基础",
                    "深度学习",
                    "大数据分析技术",
                    "云计算架构设计",
                    "物联网技术与应用",
                    "信息安全原理",
                    "编程语言实现",
                    "计算机图形学",
                    "集合论与图论",
                    "高等数学",
                    "线性代数",
                    "离散数学",
                    "概率论与数理统计",
                    "微积分学教程",
                    "量子力学",
                    "固体物理学",
                    "电动力学",
                    "热力学与统计物理",
                    "普通物理学",
                    "有机化学",
                    "无机化学",
                    "物理化学",
                    "分析化学",
                    "生物化学",
                    "分子生物学",
                    "细胞生物学",
                    "遗传学",
                    "生态学",
                    "进化生物学",
                    "西方哲学史",
                    "中国哲学史",
                    "伦理学导论",
                    "美学原理",
                    "逻辑学基础",
                    "世界史",
                    "中国古代史",
                    "中国近代史",
                    "中国现代史",
                    "世界经济学",
                    "微观经济学",
                    "宏观经济学",
                    "金融学原理",
                    "管理学原理",
                    "市场营销学",
                    "活着",
                    "追风筝的人",
                    "平凡的世界",
                    "三体",
                    "解忧杂货店",
                    "岛上书店",
                    "嫌疑人X的献身",
                    "挪威的森林",
                    "月亮与六便士",
                    "百年孤独",
                    "穆斯林的葬礼",
                    "人类简史",
                    "未来简史",
                    "资治通鉴",
                    "大卫·科波菲尔",
                    "霍乱时期的爱情",
                    "瓦尔登湖",
                    "活下去的理由",
                    "小王子",
                    "时间简史",
                    "动物庄园",
                    "简爱",
                    "悲惨世界",
                    "唐吉诃德",
                    "追忆似水年华",
                    "百年孤独",
                    "圣经",
                    "福尔摩斯探案集",
                    "大闹天宫",
                    "心理学与生活",
                    "如何高效阅读",
                    "从零到一",
                    "黑客与画家",
                    "人性的弱点",
                    "小宇宙",
                    "计算机程序设计艺术",
                    "思考，快与慢",
                    "原则",
                    "天才在左，疯子在右",
                    "万历十五年",
                    "活在当下",
                    "万物简史",
                    "人类的群星闪耀时",
                    "成为",
                    "秘密",
                    "改变的力量",
                    "曾国藩",
                    "极简主义",
                    "教父",
                    "简约人生",
                    "夜色温柔",
                    "将进酒",
                    "百年孤独",
                    "摆渡人",
                    "周易",
                    "长尾理论",
                    "第三类接触",
                    "大数据时代",
                    "丰乳肥臀",
                    "月亮与六便士",
                    "霍乱时期的爱情",
                    "鲁迅全集",
                    "战争与和平",
                    "失落的秘符",
                    "沉默的大多数",
                    "杀死一只知更鸟",
                    "卓越经理人的修炼",
                    "高效能人士的七个习惯",
                    "游戏改变世界",
                    "反脆弱",
                    "解忧杂货店",
                    "白夜行",
                    "时间的皱纹",
                    "天使与魔鬼",
                    "魔戒",
                    "黑暗塔",
                    "银河帝国",
                    "去月球",
                    "挪威的森林",
                    "歌德全集",
                    "武侠小说大系",
                    "水浒传",
                    "唐诗三百首",
                    "明朝那些事儿",
                    "资治通鉴",
                    "草房子",
                    "红与黑",
                    "死亡诗社",
                    "白雪公主",
                    "无声告白",
                    "解密",
                    "名利场",
                    "美丽新世界",
                    "丧家之犬",
                    "毒液",
                    "狄仁杰探案全集",
                    "远大前程",
                    "贞观之治",
                    "步步惊心",
                    "孙子兵法",
                    "愤怒的葡萄",
                    "高原上的星星",
                    "狼图腾",
                    "最后的晚餐",
                    "时间的潮流",
                    "李鸿章传",
                    "毛泽东传",
                    "哈利·波特与魔法石",
                    "猫鼠游戏",
                    "青春纪实",
                    "知行合一"
                ]
            },
            "data_category": "text"
        },
        "作者": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "出版社": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "人民文学出版社",
                    "长江文艺出版社",
                    "中信出版社",
                    "作家出版社",
                    "华东师范大学出版社",
                    "译林出版社",
                    "上海人民出版社",
                    "商务印书馆",
                    "新星出版社",
                    "北京联合出版公司",
                    "清华大学出版社",
                    "中国工人出版社",
                    "中国青年出版社",
                    "社会科学文献出版社",
                    "浙江人民出版社",
                    "湖南文艺出版社",
                    "中国广播电视出版社",
                    "南海出版公司",
                    "华文出版社",
                    "中华书局",
                    "陕西师范大学出版社",
                    "科学出版社",
                    "吉林出版集团",
                    "人民出版社",
                    "外文出版社",
                    "中国大百科全书出版社",
                    "山东画报出版社",
                    "广西师范大学出版社",
                    "中国社会科学出版社",
                    "北京出版社",
                    "四川人民出版社",
                    "云南人民出版社",
                    "华夏出版社",
                    "北京大学出版社",
                    "知识产权出版社",
                    "中国社会出版社",
                    "光明日报出版社",
                    "中国法制出版社",
                    "华中科技大学出版社",
                    "天津人民出版社",
                    "外语教学与研究出版社",
                    "华东师范大学出版社",
                    "福建人民出版社",
                    "南京出版社",
                    "中国经济出版社",
                    "当代世界出版社",
                    "重庆出版社",
                    "首都经济贸易大学出版社",
                    "现代出版社",
                    "天津科技出版社",
                    "安徽人民出版社",
                    "北京时代华文书局",
                    "中南出版传媒集团",
                    "天桥出版社",
                    "甘肃人民出版社",
                    "现代教育出版社",
                    "中国华侨出版社",
                    "中国画报出版社",
                    "北方文艺出版社",
                    "香港中文大学出版社",
                    "东南大学出版社",
                    "甘肃科技出版社",
                    "中国财政经济出版社",
                    "国防工业出版社",
                    "南方出版传媒公司",
                    "北京科学技术出版社",
                    "江西人民出版社",
                    "上海科学技术出版社",
                    "上海财经大学出版社",
                    "内蒙古人民出版社",
                    "北京外语教学与研究出版社",
                    "沈阳出版社",
                    "浙江大学出版社",
                    "东北财经大学出版社",
                    "新疆人民出版社",
                    "安徽教育出版社",
                    "中央编译出版社",
                    "华东出版社",
                    "中国民族出版社",
                    "台海出版社",
                    "湖北人民出版社",
                    "中国农业出版社",
                    "浙江文艺出版社",
                    "石油工业出版社",
                    "上海音乐出版社",
                    "现代通信出版社",
                    "澳门新世纪出版社",
                    "文汇出版社",
                    "北京理工大学出版社",
                    "安徽文艺出版社",
                    "新世界出版社",
                    "中南大学出版社",
                    "中国建设出版社",
                    "军事科学出版社",
                    "外文出版社",
                    "兰州大学出版社",
                    "无锡出版社",
                    "东方出版社",
                    "商务印书馆",
                    "华东理工大学出版社",
                    "北京国风出版社"
                ]
            },
            "data_category": "text"
        },
        "借出日期": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2023,
                "end_year": 2023,
                "format": "%Y-%m-%d"
            },
            "data_category": "date"
        },
        "应还日期": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2023,
                "end_year": 2023,
                "format": "%Y-%m-%d"
            },
            "data_category": "date"
        },
        "实际归还日期": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2023,
                "end_year": 2023,
                "format": "%Y-%m-%d"
            },
            "data_category": "date"
        },
        "借阅状态": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "借出",
                    "已归还",
                    "逾期未还",
                    "续借",
                    "预约",
                    "遗失",
                    "损坏",
                    "正在处理"
                ]
            },
            "data_category": "text"
        },
        "续借次数": {
            "generator_type": "integer_range",
            "params": {
                "min": 0,
                "max": 3
            },
            "data_category": "numeric"
        },
        "借阅地点": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "图书馆一楼",
                    "图书馆二楼",
                    "市中心图书馆",
                    "大学图书馆",
                    "科技馆借阅处",
                    "社区图书馆",
                    "公共图书馆",
                    "文化馆",
                    "书店借阅区",
                    "学校图书馆",
                    "图书馆借书台",
                    "大学生服务中心",
                    "校园内书吧",
                    "网络借阅平台",
                    "乡村图书室",
                    "市图书馆阅览室",
                    "自助借阅机",
                    "移动图书车",
                    "区级图书馆",
                    "市图书馆阅览室",
                    "教育馆借阅区",
                    "儿童图书馆",
                    "市立图书馆",
                    "书屋",
                    "科技图书室",
                    "小区图书角",
                    "学校教辅中心",
                    "学校电子书库",
                    "图书馆阅览区",
                    "办公室书架",
                    "线上借阅平台",
                    "学术图书馆",
                    "远程借阅中心",
                    "电子书借阅站",
                    "书籍自助借阅机",
                    "学校教室书架",
                    "公共文化中心",
                    "科研中心图书馆",
                    "网吧借书处",
                    "商业大厦借阅区",
                    "公园图书馆",
                    "图书馆文献中心",
                    "新华书店",
                    "网点自取借阅机",
                    "数字图书馆",
                    "邮局借阅点",
                    "小区自取点",
                    "旅游景区图书角",
                    "公交车站借书点",
                    "社区阅览室",
                    "学术交流中心",
                    "市文化馆",
                    "专门书店",
                    "网络借阅平台",
                    "线上借书网站",
                    "机场借书区",
                    "车站借书角",
                    "书店自取点",
                    "公司阅览区",
                    "茶馆借阅角",
                    "校外阅览区",
                    "学校宿舍借阅点",
                    "商场图书角",
                    "校园网络书库",
                    "临时借阅点",
                    "社区活动中心",
                    "移动借阅站",
                    "写字楼阅览室",
                    "自行车站借书点",
                    "酒店借书台",
                    "乡镇图书室",
                    "书架借阅点",
                    "市图书分馆",
                    "线上学习平台",
                    "大学校园图书角",
                    "青少年文化中心",
                    "县图书馆",
                    "历史博物馆阅览室",
                    "剧院借阅点",
                    "艺术馆图书区",
                    "体育馆借书区",
                    "生活服务中心",
                    "艺术展览馆",
                    "大厦阅览台",
                    "企业借书站",
                    "购物中心图书角",
                    "食品广场借书区",
                    "公共卫生图书馆",
                    "博物馆图书馆",
                    "科技研究中心",
                    "数字文化图书馆",
                    "学生会借书区",
                    "大学生交流中心",
                    "科普中心阅览室",
                    "文艺中心借阅处",
                    "旅行社借阅处",
                    "学术园区图书角",
                    "技术馆借阅区"
                ]
            },
            "data_category": "text"
        }
    },
    "text_columns_count": 6,
    "text_columns_names": [
        "读者姓名",
        "读者类型",
        "作者",
        "出版社",
        "借阅状态",
        "借阅地点"
    ],
    "numeric_columns_count": 4,
    "numeric_columns_names": [
        "借阅编号",
        "读者ID",
        "图书ISBN",
        "续借次数"
    ],
    "date_columns_count": 3,
    "date_columns_names": [
        "借出日期",
        "应还日期",
        "实际归还日期"
    ],
    "other_columns_count": 1,
    "other_columns_names": [
        "图书名称"
    ],
    "all_columns": [
        "读者姓名",
        "读者类型",
        "作者",
        "出版社",
        "借阅状态",
        "借阅地点",
        "借阅编号",
        "读者ID",
        "图书ISBN",
        "续借次数",
        "借出日期",
        "应还日期",
        "实际归还日期",
        "图书名称"
    ]
}

