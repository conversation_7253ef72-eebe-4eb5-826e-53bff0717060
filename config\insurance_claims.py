insurance_claims = {
    "display_name": "保险理赔",
    "table_title_template": "保险理赔记录表",
    "columns": {
        "理赔编号": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "CLM########",
                    "INS######",
                    "IC########",
                    "CLAIM######"
                ]
            },
            "data_category": "text"
        },
        "保单号": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "POL########",
                    "P########",
                    "POLICY######",
                    "INS-POL-####"
                ]
            },
            "data_category": "text"
        },
        "客户姓名": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "保险类型": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "车险",
                    "寿险",
                    "健康险",
                    "意外险",
                    "财产险",
                    "旅游险",
                    "责任险",
                    "医疗险",
                    "重疾险",
                    "养老险",
                    "教育险",
                    "家财险",
                    "企业财产险",
                    "团体险",
                    "车险",
                    "寿险",
                    "健康险",
                    "意外险",
                    "财产险",
                    "旅行险",
                    "航空险",
                    "家庭财产险",
                    "宠物险",
                    "人身险",
                    "企业财产险",
                    "责任险",
                    "综合险",
                    "重大疾病险",
                    "防盗险",
                    "火灾险",
                    "海上险",
                    "水灾险",
                    "失业险",
                    "养老险",
                    "教育险",
                    "车上人员险",
                    "第三者责任险",
                    "车损险",
                    "车玻璃险",
                    "车盗抢险",
                    "车划痕险",
                    "车险附加险",
                    "道路救援险",
                    "医疗险",
                    "住院险",
                    "门诊险",
                    "长期护理险",
                    "人身伤害险",
                    "交通事故险",
                    "雇主责任险",
                    "工伤险",
                    "集体险",
                    "团体险",
                    "企业保险",
                    "高风险险",
                    "环保险",
                    "气候险",
                    "信用险",
                    "建筑工程险",
                    "车险责任险",
                    "火灾责任险",
                    "铁路运输险",
                    "船舶险",
                    "航空运输险",
                    "外汇风险险",
                    "农险",
                    "农业保险",
                    "农业灾害险",
                    "生物保险",
                    "农业种植险",
                    "水产养殖险",
                    "农业机械险",
                    "农田设施险",
                    "农业房屋险",
                    "农业作物险",
                    "信用贷款险",
                    "购房险",
                    "租赁险",
                    "个人健康险",
                    "住院医疗险",
                    "医疗补充险",
                    "学费险",
                    "父母责任险",
                    "定期寿险",
                    "终身寿险",
                    "年金保险",
                    "双倍赔付险",
                    "储蓄型险",
                    "教育储蓄险",
                    "重疾险",
                    "癌症险",
                    "团体健康险",
                    "失能险",
                    "护理险",
                    "父母健康险",
                    "家庭医疗险",
                    "重大事故险",
                    "机动车责任险",
                    "遗产税险",
                    "职工保险",
                    "失业救济险",
                    "公共责任险",
                    "法律责任险",
                    "知识产权险",
                    "工程险",
                    "创业险",
                    "投资险",
                    "银行保险",
                    "投资理财险",
                    "数字货币保险",
                    "高端医疗险",
                    "高风险健康险",
                    "糖尿病险",
                    "老年险",
                    "人寿保险",
                    "提前支取险",
                    "跨境险"
                ]
            },
            "data_category": "text"
        },
        "保险公司": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "中国人寿保险公司",
                    "平安保险公司",
                    "太保人寿保险公司",
                    "新华保险公司",
                    "中国太平洋保险公司",
                    "中国人民保险公司",
                    "友邦保险公司",
                    "阳光保险公司",
                    "中邮人寿保险公司",
                    "安邦保险公司",
                    "大都会人寿保险公司",
                    "中华联合保险公司",
                    "富邦保险公司",
                    "汇丰人寿保险公司",
                    "民生保险公司",
                    "国华人寿保险公司",
                    "中华人寿保险公司",
                    "国寿安保人寿保险公司",
                    "泰康人寿保险公司",
                    "人保财险公司",
                    "中信保诚人寿保险公司",
                    "大华保险公司",
                    "万寿保险公司",
                    "瑞士再保险公司",
                    "中银保险公司",
                    "中信保险公司",
                    "合众人寿保险公司",
                    "东吴人寿保险公司",
                    "华泰保险公司",
                    "苏黎世保险公司",
                    "富安保险公司",
                    "安盛天平保险公司",
                    "中国大地保险公司",
                    "美国国际集团",
                    "中华联合财产保险公司",
                    "国富保险公司",
                    "贝尔保险公司",
                    "都邦保险公司",
                    "宝盛人寿保险公司",
                    "华盛保险公司",
                    "安盛保险公司",
                    "人寿大都会保险公司",
                    "恒安标准人寿保险公司",
                    "长安责任保险公司",
                    "达信保险公司",
                    "理赔保险公司",
                    "长城保险公司",
                    "中国再保险公司",
                    "安邦财产保险公司",
                    "中华财产保险公司",
                    "中银保险财产公司",
                    "泛华保险公司",
                    "大都会保险公司",
                    "中国人寿财产保险公司",
                    "世嘉保险公司",
                    "天安保险公司",
                    "平安财产保险公司",
                    "安诚保险公司",
                    "中意保险公司",
                    "长生保险公司",
                    "同方保险公司",
                    "中财保险公司",
                    "众安保险公司",
                    "泰康财产保险公司",
                    "华安保险公司",
                    "天平保险公司",
                    "新华财产保险公司",
                    "华贵人寿保险公司",
                    "安诚财产保险公司",
                    "普惠保险公司",
                    "天壕保险公司",
                    "中保保险公司",
                    "瑞士保险公司",
                    "中国外运保险公司",
                    "南京人寿保险公司",
                    "富国保险公司",
                    "优选保险公司",
                    "万安保险公司",
                    "中银保诚保险公司",
                    "国富人寿保险公司",
                    "安邦财产保险公司",
                    "中国金融保险公司",
                    "江泰保险公司",
                    "华东保险公司",
                    "宏利保险公司",
                    "招商信诺人寿保险公司",
                    "平安养老保险公司",
                    "国寿保险公司",
                    "中德安联人寿保险公司",
                    "中国保险公司",
                    "金盾保险公司",
                    "和谐保险公司",
                    "北京人寿保险公司",
                    "华北人寿保险公司",
                    "百年人寿保险公司",
                    "上海人寿保险公司",
                    "国民保险公司",
                    "云上保险公司",
                    "建信人寿保险公司",
                    "安盛保险财产公司",
                    "中信财产保险公司",
                    "艾文保险公司",
                    "华创保险公司"
                ]
            },
            "data_category": "text"
        },
        "出险日期": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2023,
                "end_year": 2023,
                "format": "%Y-%m-%d"
            },
            "data_category": "date"
        },
        "申请日期": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2023,
                "end_year": 2023,
                "format": "%Y-%m-%d"
            },
            "data_category": "date"
        },
        "出险原因": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "交通事故",
                    "疾病",
                    "自然灾害",
                    "火灾",
                    "盗窃",
                    "水灾",
                    "设备故障",
                    "工伤",
                    "暴风雨",
                    "地震",
                    "暴雪",
                    "雷击",
                    "洪水",
                    "爆炸",
                    "恐怖袭击",
                    "盗窃行为",
                    "人为破坏",
                    "电力故障",
                    "航空事故",
                    "船舶事故",
                    "煤气泄漏",
                    "油品泄漏",
                    "食物中毒",
                    "急性疾病",
                    "中暑",
                    "过敏反应",
                    "交通堵塞",
                    "道路滑坡",
                    "高温天气",
                    "不当操作",
                    "设备损坏",
                    "宠物伤害",
                    "家庭暴力",
                    "施工事故",
                    "建筑物坍塌",
                    "滑倒摔伤",
                    "迷路",
                    "缺氧",
                    "突发疾病",
                    "流感",
                    "病毒感染",
                    "高空坠物",
                    "屋顶漏水",
                    "偷盗",
                    "手机被盗",
                    "火灾爆炸",
                    "电器故障",
                    "电梯故障",
                    "邻里纠纷",
                    "毒物接触",
                    "误食毒品",
                    "孕妇并发症",
                    "车祸",
                    "肠胃炎",
                    "肺炎",
                    "心脏病发作",
                    "脑卒中",
                    "癌症",
                    "溺水",
                    "中毒",
                    "内伤",
                    "外伤",
                    "车祸受伤",
                    "意外摔伤",
                    "冰雹",
                    "洗车误操作",
                    "旅游意外",
                    "航空延误",
                    "急性中毒",
                    "电器火灾",
                    "建筑施工事故",
                    "野外露营事故",
                    "登山事故",
                    "滑雪事故",
                    "潜水事故",
                    "火灾中毒",
                    "气体泄漏",
                    "管道爆裂",
                    "战争",
                    "恐怖袭击受伤",
                    "环境污染",
                    "粉尘暴露",
                    "电击",
                    "非法入侵",
                    "冰冻损坏",
                    "失火",
                    "非自然死亡",
                    "暴力袭击",
                    "炸药爆炸",
                    "高空作业事故",
                    "危险化学品泄漏",
                    "建筑外墙脱落",
                    "寒潮天气",
                    "雷暴",
                    "盗窃造成损失",
                    "无人机事故",
                    "机械伤害",
                    "电池爆炸",
                    "交通肇事",
                    "恶劣天气",
                    "矿山事故",
                    "雪崩",
                    "飞行事故",
                    "地质灾害",
                    "风暴潮",
                    "火山爆发",
                    "矿物中毒"
                ]
            },
            "data_category": "text"
        },
        "理赔金额": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 500,
                "max_value": 100000,
                "decimals": 2,
                "format_string": "{:.2f}"
            },
            "data_category": "numeric"
        },
        "理赔状态": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "已申请",
                    "审核中",
                    "已赔付",
                    "部分赔付",
                    "拒赔",
                    "补充材料中",
                    "调查中",
                    "待评估",
                    "已撤销",
                    "已结案",
                    "申诉中",
                    "重新评估"
                ]
            },
            "data_category": "text"
        },
        "处理人员": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "处理时长": {
            "generator_type": "integer_range",
            "params": {
                "min": 1,
                "max": 30
            },
            "data_category": "numeric"
        },
        "关联文件": {
            "generator_type": "integer_range",
            "params": {
                "min": 1,
                "max": 10
            },
            "data_category": "numeric"
        }
    },
    "text_columns_count": 6,
    "text_columns_names": [
        "客户姓名",
        "保险类型",
        "保险公司",
        "出险原因",
        "理赔状态",
        "处理人员"
    ],
    "numeric_columns_count": 5,
    "numeric_columns_names": [
        "理赔编号",
        "保单号",
        "理赔金额",
        "处理时长",
        "关联文件"
    ],
    "date_columns_count": 2,
    "date_columns_names": [
        "出险日期",
        "申请日期"
    ],
    "other_columns_count": 0,
    "other_columns_names": [],
    "all_columns": [
        "客户姓名",
        "保险类型",
        "保险公司",
        "出险原因",
        "理赔状态",
        "处理人员",
        "理赔编号",
        "保单号",
        "理赔金额",
        "处理时长",
        "关联文件",
        "出险日期",
        "申请日期"
    ]
}

