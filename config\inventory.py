inventory = {
    "display_name": "库存信息",
    "table_title_template": "商品库存数据表",
    "columns": {
        "商品ID": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "SKU####",
                    "PROD######",
                    "IT########",
                    "INV-####"
                ]
            },
            "data_category": "text"
        },
        "商品名称": {
            "generator_type": "categorical_with_pattern",
            "params": {
                "prefixes": [
                    "高级",
                    "精选",
                    "豪华",
                    "优质",
                    "经典",
                    "时尚",
                    "专业",
                    "定制",
                    "限量版"
                ],
                "suffixes": [
                    "手机",
                    "电脑",
                    "平板",
                    "耳机",
                    "电视",
                    "洗衣机",
                    "冰箱",
                    "空调",
                    "沙发",
                    "餐桌"
                ]
            },
            "data_category": "text"
        },
        "品牌": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "苹果",
                    "三星",
                    "华为",
                    "小米",
                    "索尼",
                    "联想",
                    "戴尔",
                    "华硕",
                    "宏碁",
                    "魅族",
                    "OPPO",
                    "vivo",
                    "一加",
                    "中兴",
                    "金立",
                    "酷派",
                    "诺基亚",
                    "LG",
                    "摩托罗拉",
                    "HTC",
                    "富士通",
                    "TCL",
                    "爱立信",
                    "锐捷",
                    "华为荣耀",
                    "亿通",
                    "长城",
                    "蓝星科技",
                    "流行电子",
                    "绿动科技",
                    "雷霆电子",
                    "云动科技",
                    "超频科技",
                    "光明智能",
                    "京东",
                    "阿里巴巴",
                    "百度",
                    "美团",
                    "拼多多",
                    "网易",
                    "腾讯",
                    "小米米家",
                    "富士",
                    "卡西欧",
                    "佳能",
                    "尼康",
                    "松下",
                    "雷克萨斯",
                    "丰田",
                    "宝马",
                    "奔驰",
                    "奥迪",
                    "特斯拉",
                    "雪佛兰",
                    "福特",
                    "本田",
                    "宝马Mini",
                    "哈雷戴维森",
                    "劳斯莱斯",
                    "兰博基尼",
                    "迪奥",
                    "香奈儿",
                    "古驰",
                    "爱马仕",
                    "路易威登",
                    "迪士尼",
                    "华特迪士尼",
                    "海尔",
                    "美的",
                    "西门子",
                    "格力",
                    "志高",
                    "澳柯玛",
                    "奥克斯",
                    "长虹",
                    "创维",
                    "苏宁",
                    "国美",
                    "小熊电器",
                    "奥迪双钻",
                    "小米生态链",
                    "乐视",
                    "三星半导体",
                    "海信",
                    "东芝",
                    "赛米控",
                    "纽曼",
                    "爱普生",
                    "联想ThinkPad",
                    "华为MateBook",
                    "神州",
                    "小米Redmi",
                    "TCL电视",
                    "瓦克",
                    "兰芝",
                    "高露洁",
                    "贝达药业",
                    "药明康德",
                    "联邦制药",
                    "联邦快递"
                ]
            },
            "data_category": "text"
        },
        "分类": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "电子产品",
                    "家具",
                    "家电",
                    "服装",
                    "鞋帽",
                    "箱包",
                    "食品",
                    "饮料",
                    "日用品",
                    "母婴用品",
                    "运动健身",
                    "户外装备",
                    "数码配件",
                    "手机",
                    "电脑",
                    "平板",
                    "智能手表",
                    "耳机",
                    "音响",
                    "相机",
                    "摄影器材",
                    "游戏机",
                    "电玩",
                    "办公设备",
                    "办公用品",
                    "文具",
                    "书籍",
                    "电子书",
                    "影音",
                    "居家装饰",
                    "厨房用品",
                    "卫浴用品",
                    "清洁用品",
                    "洗护用品",
                    "化妆品",
                    "护肤品",
                    "香水",
                    "珠宝首饰",
                    "手表",
                    "眼镜",
                    "健康保健",
                    "医疗器械",
                    "药品",
                    "保健品",
                    "茶叶",
                    "咖啡",
                    "酒类",
                    "零食",
                    "生鲜",
                    "水果",
                    "蔬菜",
                    "肉类",
                    "海鲜",
                    "奶制品",
                    "粮油",
                    "调味品",
                    "宠物用品",
                    "宠物食品",
                    "乐器",
                    "玩具",
                    "模型",
                    "手办",
                    "艺术品",
                    "收藏品",
                    "汽车用品",
                    "汽车配件",
                    "摩托车装备",
                    "自行车装备",
                    "家居软装",
                    "窗帘",
                    "地毯",
                    "沙发",
                    "床上用品",
                    "灯具",
                    "花卉园艺",
                    "工具",
                    "五金",
                    "电工",
                    "智能家居",
                    "建材",
                    "男装",
                    "女装",
                    "童装",
                    "内衣",
                    "袜子",
                    "运动服",
                    "休闲服",
                    "礼品",
                    "旅行用品",
                    "行李箱",
                    "背包",
                    "手袋",
                    "手机壳",
                    "数据线",
                    "充电器",
                    "移动电源",
                    "显示器",
                    "键盘",
                    "鼠标",
                    "打印机",
                    "学习用品",
                    "教辅材料",
                    "教材",
                    "纸尿裤",
                    "奶瓶",
                    "婴儿床",
                    "婴儿车",
                    "月子用品",
                    "孕妇装",
                    "儿童玩具",
                    "童车",
                    "围巾",
                    "帽子",
                    "手套",
                    "太阳镜",
                    "雨伞"
                ]
            },
            "data_category": "text"
        },
        "库存数量": {
            "generator_type": "integer_range",
            "params": {
                "min": 0,
                "max": 10000
            },
            "data_category": "numeric"
        },
        "库存状态": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "充足",
                    "正常",
                    "偏低",
                    "紧缺",
                    "缺货",
                    "待补货",
                    "在途",
                    "预售",
                    "停售",
                    "已下架"
                ]
            },
            "data_category": "text"
        },
        "供应商": {
            "generator_type": "faker_company",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "仓库位置": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "北京仓",
                    "上海仓",
                    "广州仓",
                    "深圳仓",
                    "成都仓",
                    "武汉仓",
                    "西安仓",
                    "杭州仓",
                    "南京仓",
                    "重庆仓",
                    "天津仓",
                    "东北仓",
                    "华中仓",
                    "华南仓",
                    "华北仓"
                ]
            },
            "data_category": "text"
        },
        "进货价": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 10,
                "max_value": 5000,
                "decimals": 2,
                "format_string": "{:.2f}"
            },
            "data_category": "numeric"
        },
        "售价": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 15,
                "max_value": 10000,
                "decimals": 2,
                "format_string": "{:.2f}"
            },
            "data_category": "numeric"
        },
        "最后进货日期": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2023,
                "end_year": 2023,
                "format": "%Y-%m-%d"
            },
            "data_category": "date"
        }
    },
    "text_columns_count": 6,
    "text_columns_names": [
        "分类",
        "库存状态",
        "供应商",
        "仓库位置",
        "品牌",
        "商品名称",
    ],
    "numeric_columns_count": 5,
    "numeric_columns_names": [
        "商品ID",
        "商品名称",
        "库存数量",
        "进货价",
        "售价"
    ],
    "date_columns_count": 1,
    "date_columns_names": [
        "最后进货日期"
    ],
    "other_columns_count": 1,
    "other_columns_names": [
        "品牌"
    ],
    "all_columns": [
        "分类",
        "库存状态",
        "供应商",
        "仓库位置",
        "商品ID",
        "商品名称",
        "库存数量",
        "进货价",
        "售价",
        "最后进货日期",
        "品牌"
    ]
}

