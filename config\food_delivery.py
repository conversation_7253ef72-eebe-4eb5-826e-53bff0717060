food_delivery = {
    "display_name": "外卖订单",
    "table_title_template": "外卖订单数据表",
    "columns": {
        "订单编号": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "WM########",
                    "FD########",
                    "ORDER######",
                    "F########"
                ]
            },
            "data_category": "text"
        },
        "用户ID": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "UID####",
                    "U######",
                    "USER####",
                    "CUST######"
                ]
            },
            "data_category": "text"
        },
        "用户姓名": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "联系电话": {
            "generator_type": "faker_phone_number",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "下单时间": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2023,
                "end_year": 2023,
                "format": "%Y-%m-%d %H:%M:%S"
            },
            "data_category": "date"
        },
        "商家名称": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "川香酸菜鱼",
                    "麻辣香锅",
                    "汉堡王",
                    "肯德基",
                    "麦当劳",
                    "必胜客",
                    "华莱士",
                    "味多美",
                    "巴奴火锅",
                    "湘鄂情",
                    "小肥羊",
                    "海底捞",
                    "东来顺",
                    "吉野家",
                    "德克士",
                    "大快活",
                    "呷哺呷哺",
                    "老乡鸡",
                    "面包新语",
                    "重庆小面",
                    "酸菜鱼之家",
                    "金鼎轩",
                    "大食代",
                    "汉餐馆",
                    "尚品宅配",
                    "火锅一号",
                    "美滋滋",
                    "张亮麻辣烫",
                    "大龙燚火锅",
                    "东南亚餐厅",
                    "豪客来",
                    "沙县小吃",
                    "成都小吃",
                    "醉鹅娘",
                    "火宫殿",
                    "辣味大排档",
                    "千味涮",
                    "小杨生煎",
                    "川味火锅",
                    "乌鲁木齐大盘鸡",
                    "韩式烧烤",
                    "抹茶点心屋",
                    "粤菜馆",
                    "汕头牛肉火锅",
                    "海鲜自助餐",
                    "吉祥馄饨",
                    "鲍师傅",
                    "大喜大",
                    "阿甘锅盔",
                    "汤姆之家",
                    "丰味餐厅",
                    "西贝莜面村",
                    "绿茶餐厅",
                    "蜂蜜小屋",
                    "妈妈菜",
                    "品味餐厅",
                    "三顾冒菜",
                    "串串香",
                    "麻辣小龙虾",
                    "袁记串串香",
                    "圆缘火锅",
                    "胖哥俩",
                    "韩餐屋",
                    "纯粹拉面",
                    "乐凯撒",
                    "生煎包",
                    "牛肉面",
                    "维尔顿餐厅",
                    "烧烤达人",
                    "源昌麻辣烫",
                    "锅包肉",
                    "馋嘴猫",
                    "曹记泡馍",
                    "煌上煌",
                    "老妈米线",
                    "翠华餐厅",
                    "鲜芋仙",
                    "鼎泰丰",
                    "新味道",
                    "乡村基",
                    "二两生煎",
                    "犀牛小镇",
                    "煮妇厨房",
                    "食客时代",
                    "澳门豆捞",
                    "酱香烤肉",
                    "古老肉店",
                    "幸福馄饨",
                    "比萨手工坊",
                    "金味餐厅",
                    "老北京炸酱面",
                    "食尚坊",
                    "火星食堂",
                    "过桥米线",
                    "壹号烧烤",
                    "牛仔餐厅",
                    "悦达餐厅",
                    "平江餐厅",
                    "樱花日料",
                    "九号餐厅",
                    "外婆家",
                    "潮汕砂锅粥",
                    "滋味餐厅",
                    "小龙坎火锅",
                    "杨国福麻辣烫",
                    "丧茶",
                    "四季小厨",
                    "新派快餐",
                    "阿里郎韩国餐厅",
                    "小南国",
                    "胡辣汤",
                    "大碗茶",
                    "比萨客",
                    "泰式风味",
                    "永和大王",
                    "每日黑巧",
                    "辣妹子",
                    "快餐小站",
                    "千味涮肉",
                    "鱼火锅"
                ]
            },
            "data_category": "text"
        },
        "商家ID": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "SID####",
                    "S######",
                    "SHOP####",
                    "VD######"
                ]
            },
            "data_category": "text"
        },
        "订单内容": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "红烧牛肉面+可乐",
                    "黄焖鸡米饭(大)+冰红茶",
                    "麻辣香锅(中)+米饭*2+雪碧",
                    "宫保鸡丁+米饭",
                    "炸鸡翅+薯条",
                    "香辣小龙虾+啤酒",
                    "牛排+沙拉",
                    "酸辣粉+冰绿茶",
                    "水煮鱼+米饭",
                    "香煎三文鱼+果汁",
                    "麻辣火锅(小)+饮料",
                    "生煎包+豆浆",
                    "炸春卷+啤酒",
                    "披萨(大)+果汁",
                    "碳烤鸡胸肉+矿泉水",
                    "炸鸡+玉米浓汤",
                    "番茄炒蛋+米饭",
                    "酱爆鱿鱼+啤酒",
                    "牛肉串+米饭",
                    "卤味拼盘+冰水",
                    "生蚝+青岛啤酒",
                    "宫保鸡丁+米饭*2",
                    "鱼香肉丝+米饭",
                    "排骨饭+可乐",
                    "麻辣香锅(大)+米饭",
                    "牛肉馅饼+奶茶",
                    "炸鱼薯条+可乐",
                    "炖牛腩+米饭",
                    "糖醋排骨+米饭",
                    "东坡肉+米饭",
                    "番茄牛腩面+酸奶",
                    "红烧大虾+米饭",
                    "回锅肉+米饭",
                    "黑椒牛柳+米饭",
                    "沙拉+鲜榨果汁",
                    "咖喱鸡+米饭",
                    "椒盐大虾+啤酒",
                    "炸薯条+可乐",
                    "炒年糕+橙汁",
                    "日式炸鸡+米饭",
                    "甜酸猪排+米饭",
                    "爆炒虾仁+啤酒",
                    "生煎包+豆浆",
                    "炒米粉+绿茶",
                    "炸鸡翅+橙汁",
                    "日式拉面+绿茶",
                    "红烧排骨+米饭",
                    "宫保鸡丁+米饭*3",
                    "北京烤鸭+啤酒",
                    "羊肉串+米饭",
                    "麻辣烫(大)+雪碧",
                    "烤肉拼盘+啤酒",
                    "海鲜炒饭+冰茶",
                    "豉汁蒸排骨+米饭",
                    "鱼头豆腐汤+米饭",
                    "铁板烧+矿泉水",
                    "小龙虾+冰啤酒",
                    "茄汁虾仁+米饭",
                    "炒土豆丝+米饭",
                    "香菇滑鸡+米饭",
                    "烧鸡翅+蔬菜沙拉",
                    "泡菜炒饭+饮料",
                    "酸菜鱼+米饭",
                    "水煮牛肉+米饭",
                    "香煎牛排+沙拉",
                    "猪脚饭+可乐",
                    "烤翅中+奶茶",
                    "炸鸡+小菜",
                    "素炒面+橙汁",
                    "油炸臭豆腐+绿茶",
                    "炒青菜+米饭",
                    "火锅拼盘+啤酒",
                    "干锅牛蛙+啤酒",
                    "鱿鱼炒饭+绿茶",
                    "蜜汁烤鸭+米饭",
                    "回锅肉+米饭",
                    "香煎鳕鱼+饮料",
                    "炸串+可乐",
                    "鸳鸯火锅+米饭",
                    "酥炸大虾+啤酒",
                    "糖醋里脊+米饭",
                    "炖羊排+米饭",
                    "白灼虾+啤酒",
                    "酸辣面+饮料",
                    "扒鸡+可乐",
                    "日式炸酱面+绿茶",
                    "铁板烧牛肉+米饭",
                    "烤翅+啤酒",
                    "香辣炸鸡+果汁",
                    "炖排骨+米饭",
                    "椒盐虾+绿茶",
                    "韩式拌饭+饮料",
                    "豆腐火锅+米饭",
                    "黑椒牛排+蔬菜",
                    "青椒肉丝+米饭",
                    "香菜炒蛋+米饭",
                    "铁板豆腐+米饭",
                    "韩式泡菜炒饭+冰绿茶",
                    "海鲜煲+米饭",
                    "椒盐小龙虾+啤酒",
                    "手抓羊肉+米饭",
                    "排骨汤+米饭",
                    "小笼包+茶",
                    "干锅茄子+米饭",
                    "香辣炸鸡翅+果汁",
                    "泰式酸辣汤+米饭",
                    "大盘鸡+米饭",
                    "鸳鸯锅+啤酒",
                    "海鲜粥+绿茶",
                    "酱汁排骨+米饭",
                    "红糖糍粑+可乐",
                    "金针菇火锅+饮料",
                    "牛肉火锅+米饭",
                    "炸酱面+可乐",
                    "麻辣串串+饮料",
                    "小笼包+橙汁",
                    "三杯鸡+米饭",
                    "鱼香茄子+米饭",
                    "麻辣小龙虾+饮料",
                    "干锅田鸡+啤酒",
                    "蒜蓉大虾+米饭",
                    "酱烤翅+蔬菜沙拉",
                    "泡椒凤爪+啤酒",
                    "干锅花菜+米饭",
                    "宫保鸡丁+饮料",
                    "炸牛排+米饭",
                    "田园沙拉+果汁",
                    "炖汤排骨+米饭",
                    "炒肝尖+米饭",
                    "烤羊肉串+啤酒",
                    "宫保豆腐+米饭",
                    "香葱炒面+奶茶",
                    "番茄鸡蛋面+可乐",
                    "辣炒海带丝+米饭",
                    "椒盐排骨+米饭",
                    "海鲜沙拉+可乐",
                    "鸭头+啤酒",
                    "香煎大马哈鱼+米饭",
                    "鲜虾粥+冰绿茶",
                    "凉拌三丝+饮料",
                    "手抓饭+可乐",
                    "香煎鸡胸肉+沙拉",
                    "牛肉煲+米饭",
                    "炸牛肉串+啤酒",
                    "黑椒牛柳+米饭",
                    "鱼头泡饼+冰啤酒",
                    "冰爽果汁+米饭",
                    "大肚子炒饭+茶",
                    "土豆炖牛肉+米饭",
                    "果仁焗鸡+米饭",
                    "蒜香大虾+米饭",
                    "蜜汁烤鱼+米饭",
                    "素三明治+可乐",
                    "炸大肠+橙汁",
                    "香草奶油面包+果汁",
                    "香烤扇贝+啤酒",
                    "五香牛肉干+奶茶",
                    "泰式红咖喱+米饭",
                    "牛肉炒面+啤酒",
                    "酸辣酱鸡翅+米饭",
                    "爆炒鸡丁+米饭",
                    "椒盐鱿鱼+冰啤酒",
                    "特色烧烤+果汁",
                    "酥炸茄子+米饭",
                    "紫菜蛋花汤+米饭",
                    "麻辣水煮牛肉+米饭",
                    "三文鱼刺身+饮料",
                    "鲍汁花菇+米饭",
                    "香辣虾仁+啤酒",
                    "麻辣小火锅+饮料",
                    "酸菜牛肉面+绿茶",
                    "荔枝雪糕+奶茶"
                ]
            },
            "data_category": "text"
        },
        "商品数量": {
            "generator_type": "integer_range",
            "params": {
                "min": 1,
                "max": 10
            },
            "data_category": "numeric"
        },
        "订单金额": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 10,
                "max_value": 200,
                "decimals": 2,
                "format_string": "{:.2f}"
            },
            "data_category": "numeric"
        },
        "配送费": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 1,
                "max_value": 10,
                "decimals": 2,
                "format_string": "{:.2f}"
            },
            "data_category": "numeric"
        },
        "优惠金额": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 0,
                "max_value": 30,
                "decimals": 2,
                "format_string": "{:.2f}"
            },
            "data_category": "numeric"
        },
        "实付金额": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 10,
                "max_value": 200,
                "decimals": 2,
                "format_string": "{:.2f}"
            },
            "data_category": "numeric"
        },
        "支付方式": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "支付宝",
                    "微信支付",
                    "信用卡",
                    "借记卡",
                    "PayPal",
                    "Apple Pay",
                    "银联支付",
                    "百度钱包",
                    "京东支付",
                    "银行卡转账",
                    "现金",
                    "扫码支付",
                    "USDT",
                    "比特币",
                    "Google Pay",
                    "Samsung Pay",
                    "微信小程序支付",
                    "京东白条",
                    "淘宝信用支付",
                    "分期付款",
                    "支付宝余额",
                    "京东支付宝",
                    "支付宝花呗",
                    "支付宝借呗",
                    "京东金融",
                    "微信红包支付",
                    "美团支付",
                    "携程支付",
                    "百度钱包扫码",
                    "QQ钱包",
                    "芒果支付",
                    "京东到家支付",
                    "银联云闪付",
                    "苏宁支付",
                    "有赞支付",
                    "阿里巴巴支付宝",
                    "银联支付二维码",
                    "云闪付快捷支付",
                    "云闪付二维码",
                    "联通支付",
                    "移动支付",
                    "电信支付",
                    "车主支付",
                    "小米支付",
                    "滴滴支付",
                    "快钱支付",
                    "车主卡支付",
                    "美团钱包",
                    "京东扫码支付",
                    "百度支付",
                    "农行支付",
                    "工行支付",
                    "中行支付",
                    "建行支付",
                    "交通银行支付",
                    "广发支付",
                    "兴业银行支付",
                    "平安支付",
                    "华夏银行支付",
                    "招商银行支付",
                    "光大银行支付",
                    "中信银行支付",
                    "浦发银行支付",
                    "汇丰银行支付",
                    "光大支付",
                    "招商支付",
                    "银联卡支付",
                    "代扣支付",
                    "分期付款计划",
                    "还款支付",
                    "商城优惠券支付",
                    "电商优惠支付",
                    "会员卡支付",
                    "购物卡支付",
                    "电子钱包支付",
                    "信用卡分期",
                    "POS机支付",
                    "跨境支付",
                    "代付支付",
                    "现金支付",
                    "二维码支付",
                    "便捷支付",
                    "虚拟货币支付",
                    "自提支付",
                    "移动支付二维码",
                    "扫码支付二维码",
                    "银行卡支付",
                    "账户余额支付"
                ]
            },
            "data_category": "text"
        },
        "配送员": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "配送地址": {
            "generator_type": "faker_address",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "订单状态": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "待付款",
                    "待接单",
                    "已接单",
                    "商家已接单",
                    "商家制作中",
                    "待配送",
                    "配送中",
                    "已送达",
                    "已完成",
                    "已取消",
                    "已退款",
                    "退款中",
                    "部分退款"
                ]
            },
            "data_category": "text"
        },
        "配送时长": {
            "generator_type": "integer_range",
            "params": {
                "min": 15,
                "max": 120
            },
            "data_category": "numeric"
        },
        "评分": {
            "generator_type": "integer_range",
            "params": {
                "min": 1,
                "max": 5
            },
            "data_category": "numeric"
        }
    },
    "text_columns_count": 7,
    "text_columns_names": [
        "用户姓名",
        "商家名称",
        "配送员",
        "配送地址",
        "订单状态",
        "订单内容",
        "支付方式",
    ],
    "numeric_columns_count": 11,
    "numeric_columns_names": [
        "订单编号",
        "用户ID",
        "联系电话",
        "商家ID",
        "商品数量",
        "订单金额",
        "配送费",
        "优惠金额",
        "实付金额",
        "配送时长",
        "评分"
    ],
    "date_columns_count": 1,
    "date_columns_names": [
        "下单时间"
    ],
    "other_columns_count": 2,
    "other_columns_names": [
        "订单内容",
        "支付方式"
    ],
    "all_columns": [
        "用户姓名",
        "商家名称",
        "配送员",
        "配送地址",
        "订单状态",
        "订单编号",
        "用户ID",
        "联系电话",
        "商家ID",
        "商品数量",
        "订单金额",
        "配送费",
        "优惠金额",
        "实付金额",
        "配送时长",
        "评分",
        "下单时间",
        "订单内容",
        "支付方式"
    ]
}

