api_request_logs = {
    "display_name": "API请求日志",
    "table_title_template": "API请求日志表",
    "columns": {
        "序号": {
            "generator_type": "index",
            "data_category": "numeric"
        },
        "请求时间": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2023,
                "end_year": 2024,
                "format": "%Y-%m-%d %H:%M:%S"
            },
            "data_category": "date"
        },
        "接口名称": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "用户认证",
                    "数据查询",
                    "文件上传",
                    "数据分析",
                    "消息推送",
                    "订单处理",
                    "支付接口",
                    "数据同步"
                ]
            },
            "data_category": "text"
        },
        "请求方法": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "GET",
                    "POST",
                    "PUT",
                    "DELETE"
                ]
            },
            "data_category": "text"
        },
        "响应状态": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "200 成功",
                    "400 参数错误",
                    "401 未授权",
                    "404 不存在",
                    "500 服务器错误"
                ]
            },
            "data_category": "text"
        },
        "耗时(ms)": {
            "generator_type": "integer_range",
            "params": {
                "min": 50,
                "max": 5000
            },
            "data_category": "numeric"
        },
        "请求IP": {
            "generator_type": "faker_ipv4",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "用户名": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "用户ID": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "USER#####",
                    "ADM###",
                    "SYS##"
                ]
            },
            "data_category": "other"
        }
    },
    "text_columns_count": 5,
    "text_columns_names": [
        "接口名称",
        "请求方法",
        "请求IP",
        "用户名",
        "响应状态"
    ],
    "numeric_columns_count": 2,
    "numeric_columns_names": [
        "耗时(ms)",
        "序号"
    ],
    "date_columns_count": 1,
    "date_columns_names": [
        "请求时间"
    ],
    "other_columns_count": 1,
    "other_columns_names": [
        "用户ID"
    ],
    "all_columns": [
        "序号",
        "接口名称",
        "请求方法",
        "请求IP",
        "用户名",
        "耗时(ms)",
        "用户ID",
        "请求时间",
        "响应状态"
    ]
}

