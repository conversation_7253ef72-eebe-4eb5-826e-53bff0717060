weather = {
    "display_name": "气象数据",
    "table_title_template": "气象监测数据表",
    "columns": {
        "城市": {
            "generator_type": "categorical",
            "params": {
                "values": {
                    "type": "common_data",
                    "key": "cities"
                }
            },
            "data_category": "text"
        },
        "日期": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2020,
                "end_year": 2023,
                "format": "%Y-%m-%d"
            },
            "data_category": "date"
        },
        "最高温度": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": -10,
                "max_value": 42,
                "format_string": "{:d}°C"
            },
            "data_category": "numeric"
        },
        "最低温度": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": -30,
                "max_value": 30,
                "format_string": "{:d}°C"
            },
            "data_category": "numeric"
        },
        "体感温度": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": -15,
                "max_value": 45,
                "format_string": "{:d}°C"
            },
            "data_category": "numeric"
        },
        "天气状况": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "晴",
                    "多云",
                    "阴",
                    "小雨",
                    "中雨",
                    "大雨",
                    "暴雨",
                    "阵雨",
                    "雷阵雨",
                    "小雪",
                    "中雪",
                    "大雪",
                    "暴雪",
                    "雨夹雪",
                    "冻雨",
                    "雾",
                    "霾",
                    "沙尘暴",
                    "浮尘",
                    "扬沙",
                    "强沙尘暴",
                    "大风",
                    "龙卷风",
                    "冰雹",
                    "热带风暴",
                    "飓风",
                    "台风",
                    "晴转多云",
                    "多云转晴",
                    "阴转小雨",
                    "小雨转中雨",
                    "中雨转大雨",
                    "阵雨转多云",
                    "小雪转中雪",
                    "雷阵雨伴有冰雹",
                    "小到中雨",
                    "中到大雨",
                    "大到暴雨",
                    "暴雨到大暴雨",
                    "大暴雨到特大暴雨",
                    "小到中雪",
                    "中到大雪",
                    "大到暴雪",
                    "浓雾",
                    "强浓雾",
                    "轻雾",
                    "薄雾",
                    "大雾",
                    "特强浓雾",
                    "霜",
                    "结冰",
                    "冰粒",
                    "微风",
                    "疾风",
                    "烈风",
                    "狂风",
                    "热带低压",
                    "强热带风暴",
                    "超强台风",
                    "轻度霾",
                    "中度霾",
                    "重度霾",
                    "严重霾",
                    "晴间多云",
                    "雨雪天气",
                    "阴有小雨",
                    "阵雪",
                    "浮冰",
                    "扬沙转多云"
                ]
            },
            "data_category": "text"
        },
        "降水量": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 0,
                "max_value": 200,
                "decimals": 1,
                "format_string": "{:.1f}mm"
            },
            "data_category": "numeric"
        },
        "湿度": {
            "generator_type": "percentage",
            "params": {
                "min_value": 10,
                "max_value": 99,
                "decimals": 0
            },
            "data_category": "numeric"
        },
        "风力": {
            "generator_type": "integer_range_with_unit",
            "params": {
                "min": 0,
                "max": 12,
                "unit": "级"
            },
            "data_category": "numeric"
        },
        "风向": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "东风",
                    "南风",
                    "西风",
                    "北风",
                    "东北风",
                    "东南风",
                    "西南风",
                    "西北风",
                    "微风",
                    "无持续风向"
                ]
            },
            "data_category": "text"
        },
        "气压": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 950,
                "max_value": 1050,
                "format_string": "{:d}hPa"
            },
            "data_category": "numeric"
        },
        "能见度": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 0.1,
                "max_value": 30,
                "decimals": 1,
                "format_string": "{:.1f}km"
            },
            "data_category": "numeric"
        },
        "备注": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "无",
                    "重要",
                    "紧急",
                    "待跟进",
                    "已完成",
                    "已取消"
                ]
            },
            "data_category": "text"
        }
    },
    "text_columns_count": 4,
    "text_columns_names": [
        "城市",
        "风向",
        "备注",
        "天气状况"
    ],
    "numeric_columns_count": 8,
    "numeric_columns_names": [
        "最高温度",
        "最低温度",
        "体感温度",
        "降水量",
        "湿度",
        "风力",
        "气压",
        "能见度"
    ],
    "date_columns_count": 1,
    "date_columns_names": [
        "日期"
    ],
    "other_columns_count": 0,
    "other_columns_names": [],
    "all_columns": [
        "城市",
        "风向",
        "备注",
        "最高温度",
        "最低温度",
        "体感温度",
        "降水量",
        "湿度",
        "风力",
        "气压",
        "能见度",
        "日期",
        "天气状况"
    ]
}

