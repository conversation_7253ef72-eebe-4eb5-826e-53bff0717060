cities = {
    "display_name": "城市信息表",
    "table_title_template": "城市基本数据表",
    "columns": {
        "序号": {
            "generator_type": "index",
            "data_category": "text"
        },
        "城市名称": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "北京",
                    "上海",
                    "广州",
                    "深圳",
                    "杭州",
                    "南京",
                    "武汉",
                    "成都",
                    "重庆",
                    "西安",
                    "长沙",
                    "苏州",
                    "天津",
                    "郑州",
                    "青岛",
                    "大连",
                    "宁波",
                    "厦门",
                    "福州",
                    "哈尔滨",
                    "济南",
                    "合肥",
                    "昆明",
                    "贵阳",
                    "南宁",
                    "南昌",
                    "太原",
                    "石家庄",
                    "沈阳",
                    "长春",
                    "兰州",
                    "海口"
                ]
            },
            "data_category": "text"
        },
        "所属省份": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "北京市",
                    "上海市",
                    "广东省",
                    "广东省",
                    "浙江省",
                    "江苏省",
                    "湖北省",
                    "四川省",
                    "重庆市",
                    "陕西省",
                    "湖南省",
                    "江苏省",
                    "天津市",
                    "河南省",
                    "山东省",
                    "辽宁省",
                    "浙江省",
                    "福建省",
                    "福建省",
                    "黑龙江省",
                    "山东省",
                    "安徽省",
                    "云南省",
                    "贵州省",
                    "广西壮族自治区",
                    "江西省",
                    "山西省",
                    "河北省",
                    "辽宁省",
                    "吉林省",
                    "甘肃省",
                    "海南省"
                ]
            },
            "data_category": "text"
        },
        "人口数量(万)": {
            "generator_type": "integer_range",
            "params": {
                "min": 100,
                "max": 2500
            },
            "data_category": "numeric"
        },
        "面积(平方公里)": {
            "generator_type": "integer_range",
            "params": {
                "min": 500,
                "max": 20000
            },
            "data_category": "numeric"
        },
        "GDP(亿元)": {
            "generator_type": "integer_range",
            "params": {
                "min": 1000,
                "max": 40000
            },
            "data_category": "numeric"
        },
        "成立时间": {
            "generator_type": "date_range",
            "params": {
                "start_year": 1900,
                "end_year": 1950,
                "format": "%Y年%m月%d日"
            },
            "data_category": "date"
        },
        "城市代码": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "CN-###",
                    "CITY###"
                ]
            },
            "data_category": "text"
        },
        "备注": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "无",
                    "重要",
                    "紧急",
                    "待跟进",
                    "已完成",
                    "已取消"
                ]
            },
            "data_category": "text"
        }
    },
    "text_columns_count": 3,
    "text_columns_names": [
        "城市名称",
        "所属省份",
        "备注"
    ],
    "numeric_columns_count": 4,
    "numeric_columns_names": [
        "人口数量(万)",
        "面积(平方公里)",
        "GDP(亿元)",
        "城市代码"
    ],
    "date_columns_count": 1,
    "date_columns_names": [
        "成立时间"
    ],
    "other_columns_count": 0,
    "other_columns_names": [],
    "all_columns": [
        "城市名称",
        "所属省份",
        "备注",
        "人口数量(万)",
        "面积(平方公里)",
        "GDP(亿元)",
        "城市代码",
        "成立时间"
    ]
}

major_cities = {
    "display_name": "主要城市数据",
    "table_title_template": "中国主要城市经济指标表",
    "columns": {
        "序号": {
            "generator_type": "index",
            "data_category": "text"
        },
        "城市": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "北京",
                    "上海",
                    "广州",
                    "深圳",
                    "杭州",
                    "南京",
                    "武汉",
                    "成都"
                ]
            },
            "data_category": "text"
        },
        "GDP排名": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                    "6",
                    "7",
                    "8"
                ]
            },
            "data_category": "text"
        },
        "人均GDP(万元)": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 8,
                "max_value": 20,
                "decimals": 2,
                "format_string": "{:.2f}"
            },
            "data_category": "numeric"
        },
        "第一产业占比(%)": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 0.5,
                "max_value": 5,
                "decimals": 1,
                "format_string": "{:.1f}%"
            },
            "data_category": "numeric"
        },
        "第二产业占比(%)": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 20,
                "max_value": 45,
                "decimals": 1,
                "format_string": "{:.1f}%"
            },
            "data_category": "numeric"
        },
        "第三产业占比(%)": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 50,
                "max_value": 80,
                "decimals": 1,
                "format_string": "{:.1f}%"
            },
            "data_category": "numeric"
        },
        "统计年份": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "2023年",
                    "2022年",
                    "2021年"
                ]
            },
            "data_category": "text"
        },
        "备注": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "无",
                    "重要",
                    "紧急",
                    "待跟进",
                    "已完成",
                    "已取消"
                ]
            },
            "data_category": "text"
        },
        "状态": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "活跃",
                    "挂起",
                    "完成",
                    "取消",
                    "延期",
                    "进行中"
                ]
            },
            "data_category": "text"
        }
    },
    "text_columns_count": 3,
    "text_columns_names": [
        "城市",
        "备注",
        "状态"
    ],
    "numeric_columns_count": 5,
    "numeric_columns_names": [
        "GDP排名",
        "人均GDP(万元)",
        "第一产业占比(%)",
        "第二产业占比(%)",
        "第三产业占比(%)"
    ],
    "date_columns_count": 1,
    "date_columns_names": [
        "统计年份"
    ],
    "other_columns_count": 0,
    "other_columns_names": [],
    "all_columns": [
        "城市",
        "备注",
        "状态",
        "GDP排名",
        "人均GDP(万元)",
        "第一产业占比(%)",
        "第二产业占比(%)",
        "第三产业占比(%)",
        "统计年份"
    ]
}

