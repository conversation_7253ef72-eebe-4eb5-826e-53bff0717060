{"cells": [{"cell_type": "code", "execution_count": 1, "id": "2fd7f016-b34a-4e89-a920-160e7e4526da", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["{'common_data': {'cities': ['北京',\n", "   '上海',\n", "   '广州',\n", "   '深圳',\n", "   '成都',\n", "   '武汉',\n", "   '杭州',\n", "   '重庆',\n", "   '西安',\n", "   '南京',\n", "   '天津',\n", "   '苏州',\n", "   '青岛',\n", "   '郑州',\n", "   '长沙',\n", "   '合肥',\n", "   '无锡',\n", "   '宁波',\n", "   '福州',\n", "   '厦门'],\n", "  'major_cities': ['北京',\n", "   '上海',\n", "   '广州',\n", "   '深圳',\n", "   '成都',\n", "   '重庆',\n", "   '武汉',\n", "   '杭州',\n", "   '南京',\n", "   '西安']},\n", " 'themes': {'transportation': {'display_name': '交通工具',\n", "   'table_title_template': '交通工具信息表',\n", "   'columns': {'类型': {'generator_type': 'categorical',\n", "     'data_category': 'text',\n", "     'params': {'values': ['轿车',\n", "       'SUV',\n", "       'MPV',\n", "       '跑车',\n", "       '皮卡',\n", "       '卡车',\n", "       '公交车',\n", "       '越野车',\n", "       '敞篷车',\n", "       '混合动力车']}},\n", "    '品牌': {'generator_type': 'faker_company',\n", "     'data_category': 'text',\n", "     'params': {'locale': 'zh_CN'}},\n", "    '型号': {'generator_type': 'alphanumeric_pattern',\n", "     'data_category': 'text',\n", "     'params': {'patterns': ['X###', 'XY-###', 'Model ##', 'Series-#']}},\n", "    '生产商': {'generator_type': 'faker_company',\n", "     'data_category': 'text',\n", "     'params': {'locale': 'zh_CN'}},\n", "    '生产国': {'generator_type': 'categorical',\n", "     'data_category': 'text',\n", "     'params': {'values': ['中国',\n", "       '美国',\n", "       '日本',\n", "       '德国',\n", "       '韩国',\n", "       '法国',\n", "       '英国',\n", "       '意大利',\n", "       '瑞典',\n", "       '西班牙']}},\n", "    '上市时间': {'generator_type': 'date_range',\n", "     'data_category': 'date',\n", "     'params': {'start_year': 2000, 'end_year': 2024, 'format': '%Y-%m-%d'}},\n", "    '价格区间': {'generator_type': 'numerical_range_formatted',\n", "     'data_category': 'numeric',\n", "     'params': {'min_value': 50000,\n", "      'max_value': 1000000,\n", "      'format_string': '{:,}元'}},\n", "    '座位数': {'generator_type': 'integer_range',\n", "     'data_category': 'numeric',\n", "     'params': {'min': 2, 'max': 9}},\n", "    '载重量': {'generator_type': 'numerical_range_formatted',\n", "     'data_category': 'numeric',\n", "     'params': {'min_value': 0.5,\n", "      'max_value': 50.0,\n", "      'decimals': 1,\n", "      'format_string': '{:.1f}吨'}},\n", "    '尺寸规格': {'generator_type': 'dimension_format',\n", "     'data_category': 'text',\n", "     'params': {'min_length': 3000,\n", "      'max_length': 6000,\n", "      'min_width': 1500,\n", "      'max_width': 2200,\n", "      'min_height': 1200,\n", "      'max_height': 2000,\n", "      'format_string': '{:d}×{:d}×{:d}mm'}},\n", "    '动力类型': {'generator_type': 'categorical',\n", "     'data_category': 'text',\n", "     'params': {'values': ['汽油', '柴油', '电动', '混合动力', '氢能源', '天然气', '生物燃料']}},\n", "    '最高速度': {'generator_type': 'numerical_range_formatted',\n", "     'data_category': 'numeric',\n", "     'params': {'min_value': 100,\n", "      'max_value': 350,\n", "      'format_string': '{:d}km/h'}},\n", "    '油耗': {'generator_type': 'numerical_range_formatted',\n", "     'data_category': 'numeric',\n", "     'params': {'min_value': 4.5,\n", "      'max_value': 15.0,\n", "      'decimals': 1,\n", "      'format_string': '{:.1f}L/100km'}}}},\n", "  'personnel': {'display_name': '人员信息',\n", "   'table_title_template': '人员基本信息表',\n", "   'columns': {'姓名': {'generator_type': 'faker_name',\n", "     'data_category': 'text',\n", "     'params': {'locale': 'zh_CN'}},\n", "    '年龄': {'generator_type': 'integer_range',\n", "     'data_category': 'numeric',\n", "     'params': {'min': 18, 'max': 65}},\n", "    '性别': {'generator_type': 'categorical',\n", "     'data_category': 'text',\n", "     'params': {'values': ['男', '女', '未知', '保密']}},\n", "    '部门': {'generator_type': 'categorical',\n", "     'data_category': 'text',\n", "     'params': {'values': ['研发部',\n", "       '市场部',\n", "       '销售部',\n", "       '行政部',\n", "       '财务部',\n", "       '人力资源部',\n", "       '法务部']}},\n", "    '职位': {'generator_type': 'faker_job',\n", "     'data_category': 'text',\n", "     'params': {'locale': 'zh_CN'}},\n", "    '入职日期': {'generator_type': 'date_range',\n", "     'data_category': 'date',\n", "     'params': {'start_year': 2010, 'end_year': 2024, 'format': '%Y-%m-%d'}},\n", "    '工号': {'generator_type': 'alphanumeric_pattern',\n", "     'data_category': 'text',\n", "     'params': {'patterns': ['EMP####', 'WK-####', 'P####']}},\n", "    '联系电话': {'generator_type': 'faker_phone_number',\n", "     'data_category': 'text',\n", "     'params': {'locale': 'zh_CN'}},\n", "    '电子邮箱': {'generator_type': 'faker_email',\n", "     'data_category': 'text',\n", "     'params': {'locale': 'zh_CN'}},\n", "    '学历': {'generator_type': 'categorical',\n", "     'data_category': 'text',\n", "     'params': {'values': ['高中', '中专', '大专', '本科', '硕士', '博士', 'MBA']}}}}}}"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["{\n", "  \"common_data\": {\n", "    \"cities\": [\n", "      \"北京\", \"上海\", \"广州\", \"深圳\", \"成都\", \"武汉\", \"杭州\", \"重庆\", \"西安\", \"南京\", \n", "      \"天津\", \"苏州\", \"青岛\", \"郑州\", \"长沙\", \"合肥\", \"无锡\", \"宁波\", \"福州\", \"厦门\"\n", "    ],\n", "    \"major_cities\": [\n", "      \"北京\", \"上海\", \"广州\", \"深圳\", \"成都\", \"重庆\", \"武汉\", \"杭州\", \"南京\", \"西安\"\n", "    ]\n", "  },\n", "  \"themes\": {\n", "    \"transportation\": {\n", "      \"display_name\": \"交通工具\",\n", "      \"table_title_template\": \"交通工具信息表\",\n", "      \"columns\": {\n", "        \"类型\": {\n", "          \"generator_type\": \"categorical\",\n", "          \"data_category\": \"text\",\n", "          \"params\": {\n", "            \"values\": [\"轿车\", \"SUV\", \"MPV\", \"跑车\", \"皮卡\", \"卡车\", \"公交车\", \"越野车\", \"敞篷车\", \"混合动力车\"]\n", "          }\n", "        },\n", "        \"品牌\": {\n", "          \"generator_type\": \"faker_company\",\n", "          \"data_category\": \"text\",\n", "          \"params\": {\n", "            \"locale\": \"zh_CN\"\n", "          }\n", "        },\n", "        \"型号\": {\n", "          \"generator_type\": \"alphanumeric_pattern\",\n", "          \"data_category\": \"text\",\n", "          \"params\": {\n", "            \"patterns\": [\"X###\", \"XY-###\", \"Model ##\", \"Series-#\"]\n", "          }\n", "        },\n", "        \"生产商\": {\n", "          \"generator_type\": \"faker_company\",\n", "          \"data_category\": \"text\",\n", "          \"params\": {\n", "            \"locale\": \"zh_CN\"\n", "          }\n", "        },\n", "        \"生产国\": {\n", "          \"generator_type\": \"categorical\",\n", "          \"data_category\": \"text\",\n", "          \"params\": {\n", "            \"values\": [\"中国\", \"美国\", \"日本\", \"德国\", \"韩国\", \"法国\", \"英国\", \"意大利\", \"瑞典\", \"西班牙\"]\n", "          }\n", "        },\n", "        \"上市时间\": {\n", "          \"generator_type\": \"date_range\",\n", "          \"data_category\": \"date\",\n", "          \"params\": {\n", "            \"start_year\": 2000,\n", "            \"end_year\": 2024,\n", "            \"format\": \"%Y-%m-%d\"\n", "          }\n", "        },\n", "        \"价格区间\": {\n", "          \"generator_type\": \"numerical_range_formatted\",\n", "          \"data_category\": \"numeric\",\n", "          \"params\": {\n", "            \"min_value\": 50000,\n", "            \"max_value\": 1000000,\n", "            \"format_string\": \"{:,}元\"\n", "          }\n", "        },\n", "        \"座位数\": {\n", "          \"generator_type\": \"integer_range\",\n", "          \"data_category\": \"numeric\",\n", "          \"params\": {\n", "            \"min\": 2,\n", "            \"max\": 9\n", "          }\n", "        },\n", "        \"载重量\": {\n", "          \"generator_type\": \"numerical_range_formatted\",\n", "          \"data_category\": \"numeric\",\n", "          \"params\": {\n", "            \"min_value\": 0.5,\n", "            \"max_value\": 50.0,\n", "            \"decimals\": 1,\n", "            \"format_string\": \"{:.1f}吨\"\n", "          }\n", "        },\n", "        \"尺寸规格\": {\n", "          \"generator_type\": \"dimension_format\",\n", "          \"data_category\": \"text\",\n", "          \"params\": {\n", "            \"min_length\": 3000,\n", "            \"max_length\": 6000,\n", "            \"min_width\": 1500,\n", "            \"max_width\": 2200,\n", "            \"min_height\": 1200,\n", "            \"max_height\": 2000,\n", "            \"format_string\": \"{:d}×{:d}×{:d}mm\"\n", "          }\n", "        },\n", "        \"动力类型\": {\n", "          \"generator_type\": \"categorical\",\n", "          \"data_category\": \"text\",\n", "          \"params\": {\n", "            \"values\": [\"汽油\", \"柴油\", \"电动\", \"混合动力\", \"氢能源\", \"天然气\", \"生物燃料\"]\n", "          }\n", "        },\n", "        \"最高速度\": {\n", "          \"generator_type\": \"numerical_range_formatted\",\n", "          \"data_category\": \"numeric\",\n", "          \"params\": {\n", "            \"min_value\": 100,\n", "            \"max_value\": 350,\n", "            \"format_string\": \"{:d}km/h\"\n", "          }\n", "        },\n", "        \"油耗\": {\n", "          \"generator_type\": \"numerical_range_formatted\",\n", "          \"data_category\": \"numeric\",\n", "          \"params\": {\n", "            \"min_value\": 4.5,\n", "            \"max_value\": 15.0,\n", "            \"decimals\": 1,\n", "            \"format_string\": \"{:.1f}L/100km\"\n", "          }\n", "        }\n", "      }\n", "    },\n", "    \"personnel\": {\n", "      \"display_name\": \"人员信息\",\n", "      \"table_title_template\": \"人员基本信息表\",\n", "      \"columns\": {\n", "        \"姓名\": {\n", "          \"generator_type\": \"faker_name\",\n", "          \"data_category\": \"text\",\n", "          \"params\": {\n", "            \"locale\": \"zh_CN\"\n", "          }\n", "        },\n", "        \"年龄\": {\n", "          \"generator_type\": \"integer_range\",\n", "          \"data_category\": \"numeric\",\n", "          \"params\": {\n", "            \"min\": 18,\n", "            \"max\": 65\n", "          }\n", "        },\n", "        \"性别\": {\n", "          \"generator_type\": \"categorical\",\n", "          \"data_category\": \"text\",\n", "          \"params\": {\n", "            \"values\": [\"男\", \"女\", \"未知\", \"保密\"]\n", "          }\n", "        },\n", "        \"部门\": {\n", "          \"generator_type\": \"categorical\",\n", "          \"data_category\": \"text\",\n", "          \"params\": {\n", "            \"values\": [\"研发部\", \"市场部\", \"销售部\", \"行政部\", \"财务部\", \"人力资源部\", \"法务部\"]\n", "          }\n", "        },\n", "        \"职位\": {\n", "          \"generator_type\": \"faker_job\",\n", "          \"data_category\": \"text\",\n", "          \"params\": {\n", "            \"locale\": \"zh_CN\"\n", "          }\n", "        },\n", "        \"入职日期\": {\n", "          \"generator_type\": \"date_range\",\n", "          \"data_category\": \"date\",\n", "          \"params\": {\n", "            \"start_year\": 2010,\n", "            \"end_year\": 2024,\n", "            \"format\": \"%Y-%m-%d\"\n", "          }\n", "        },\n", "        \"工号\": {\n", "          \"generator_type\": \"alphanumeric_pattern\",\n", "          \"data_category\": \"text\",\n", "          \"params\": {\n", "            \"patterns\": [\"EMP####\", \"WK-####\", \"P####\"]\n", "          }\n", "        },\n", "        \"联系电话\": {\n", "          \"generator_type\": \"faker_phone_number\",\n", "          \"data_category\": \"text\",\n", "          \"params\": {\n", "            \"locale\": \"zh_CN\"\n", "          }\n", "        },\n", "        \"电子邮箱\": {\n", "          \"generator_type\": \"faker_email\",\n", "          \"data_category\": \"text\",\n", "          \"params\": {\n", "            \"locale\": \"zh_CN\"\n", "          }\n", "        },\n", "        \"学历\": {\n", "          \"generator_type\": \"categorical\",\n", "          \"data_category\": \"text\",\n", "          \"params\": {\n", "            \"values\": [\"高中\", \"中专\", \"大专\", \"本科\", \"硕士\", \"博士\", \"MBA\"]\n", "          }\n", "        }\n", "      }\n", "    }\n", "  }\n", "} "]}, {"cell_type": "code", "execution_count": 5, "id": "61507738-faa9-43be-bbc8-927dcc5cc16f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON><PERSON> started\n", "Generating tables...\n", "Starting generate_all_tables\n", "开始生成表格图像，类型: 三线表, 文本表, 无线表, 一维表, 二维表\n", "\n", "开始生成三线表，数量: 5张\n", "计划生成 5 张三线表，分 1 批处理\n", "开始生成第 1/1 批，共 5 张三线表...\n", "生成三线表 1，主题: personnel\n", "选择行数: 15\n", "生成数据...\n", "渲染三线表图像...\n", "图像已保存: 表格\\三线表\\人员信息三线表_1.jpg\n", "生成三线表 2，主题: transportation\n", "选择行数: 4\n", "生成数据...\n", "渲染三线表图像...\n", "图像已保存: 表格\\三线表\\交通工具三线表_2.jpg\n", "生成三线表 3，主题: transportation\n", "选择行数: 15\n", "生成数据...\n", "渲染三线表图像...\n", "图像已保存: 表格\\三线表\\交通工具三线表_3.jpg\n", "生成三线表 4，主题: personnel\n", "选择行数: 8\n", "生成数据...\n", "渲染三线表图像...\n", "图像已保存: 表格\\三线表\\人员信息三线表_4.jpg\n", "生成三线表 5，主题: personnel\n", "选择行数: 11\n", "生成数据...\n", "渲染三线表图像...\n", "图像已保存: 表格\\三线表\\人员信息三线表_5.jpg\n", "批次 1: 已生成 5/5 张三线表\n", "完成第 1/1 批三线表生成\n", "完成三线表生成，共 5 张\n", "\n", "开始生成文本表，数量: 5张\n", "计划生成 5 张文本表，分 1 批处理\n", "开始生成第 1/1 批，共 5 张文本表...\n", "生成文本表 1，主题: personnel\n", "选择行数: 12\n", "生成数据...\n", "渲染文本表图像...\n", "图像已保存: 表格\\二维表\\人员信息文本表_1.jpg\n", "生成文本表 2，主题: transportation\n", "选择行数: 13\n", "生成数据...\n", "渲染文本表图像...\n", "图像已保存: 表格\\二维表\\交通工具文本表_2.jpg\n", "生成文本表 3，主题: transportation\n", "选择行数: 6\n", "生成数据...\n", "渲染文本表图像...\n", "图像已保存: 表格\\二维表\\交通工具文本表_3.jpg\n", "生成文本表 4，主题: personnel\n", "选择行数: 9\n", "生成数据...\n", "渲染文本表图像...\n", "图像已保存: 表格\\二维表\\人员信息文本表_4.jpg\n", "生成文本表 5，主题: transportation\n", "选择行数: 8\n", "生成数据...\n", "渲染文本表图像...\n", "图像已保存: 表格\\二维表\\交通工具文本表_5.jpg\n", "批次 1: 已生成 5/5 张文本表\n", "完成第 1/1 批文本表生成\n", "完成文本表生成，共 5 张\n", "\n", "开始生成无线表，数量: 5张\n", "计划生成 5 张无线表，分 1 批处理\n", "开始生成第 1/1 批，共 5 张无线表...\n", "生成无线表 1，主题: personnel\n", "选择行数: 9\n", "生成数据...\n", "渲染无线表图像...\n", "图像已保存: 表格\\无线表\\人员信息无线表_1.jpg\n", "生成无线表 2，主题: transportation\n", "选择行数: 6\n", "生成数据...\n", "渲染无线表图像...\n", "图像已保存: 表格\\无线表\\交通工具无线表_2.jpg\n", "生成无线表 3，主题: personnel\n", "选择行数: 12\n", "生成数据...\n", "渲染无线表图像...\n", "图像已保存: 表格\\无线表\\人员信息无线表_3.jpg\n", "生成无线表 4，主题: personnel\n", "选择行数: 12\n", "生成数据...\n", "渲染无线表图像...\n", "图像已保存: 表格\\无线表\\人员信息无线表_4.jpg\n", "生成无线表 5，主题: personnel\n", "选择行数: 15\n", "生成数据...\n", "渲染无线表图像...\n", "图像已保存: 表格\\无线表\\人员信息无线表_5.jpg\n", "批次 1: 已生成 5/5 张无线表\n", "完成第 1/1 批无线表生成\n", "完成无线表生成，共 5 张\n", "\n", "开始生成一维表，数量: 5张\n", "计划生成 5 张一维表，分 1 批处理\n", "开始生成第 1/1 批，共 5 张一维表...\n", "生成一维表 1，主题: transportation\n", "选择行数: 7\n", "生成数据...\n", "渲染一维表图像...\n", "图像已保存: 表格\\一维表\\交通工具一维表_1.jpg\n", "生成一维表 2，主题: transportation\n", "选择行数: 15\n", "生成数据...\n", "渲染一维表图像...\n", "图像已保存: 表格\\一维表\\交通工具一维表_2.jpg\n", "生成一维表 3，主题: personnel\n", "选择行数: 14\n", "生成数据...\n", "渲染一维表图像...\n", "图像已保存: 表格\\一维表\\人员信息一维表_3.jpg\n", "生成一维表 4，主题: transportation\n", "选择行数: 8\n", "生成数据...\n", "渲染一维表图像...\n", "图像已保存: 表格\\一维表\\交通工具一维表_4.jpg\n", "生成一维表 5，主题: personnel\n", "选择行数: 7\n", "生成数据...\n", "渲染一维表图像...\n", "图像已保存: 表格\\一维表\\人员信息一维表_5.jpg\n", "批次 1: 已生成 5/5 张一维表\n", "完成第 1/1 批一维表生成\n", "完成一维表生成，共 5 张\n", "\n", "开始生成二维表，数量: 5张\n", "计划生成 5 张二维表，分 1 批处理\n", "开始生成第 1/1 批，共 5 张二维表...\n", "生成二维表 1，主题: transportation\n", "选择行数: 5\n", "生成数据...\n", "渲染二维表图像...\n", "图像已保存: 表格\\文本表\\交通工具文本表_1.jpg\n", "生成二维表 2，主题: transportation\n", "选择行数: 9\n", "生成数据...\n", "渲染二维表图像...\n", "图像已保存: 表格\\文本表\\交通工具文本表_2.jpg\n", "生成二维表 3，主题: personnel\n", "选择行数: 11\n", "生成数据...\n", "渲染二维表图像...\n", "图像已保存: 表格\\文本表\\人员信息文本表_3.jpg\n", "生成二维表 4，主题: transportation\n", "选择行数: 9\n", "生成数据...\n", "渲染二维表图像...\n", "图像已保存: 表格\\文本表\\交通工具文本表_4.jpg\n", "生成二维表 5，主题: transportation\n", "选择行数: 13\n", "生成数据...\n", "渲染二维表图像...\n", "图像已保存: 表格\\文本表\\交通工具文本表_5.jpg\n", "批次 1: 已生成 5/5 张二维表\n", "完成第 1/1 批二维表生成\n", "完成二维表生成，共 5 张\n", "总共生成了 25 张图片\n", "总共生成了 25 张表格图片\n", "Successfully generated 25 tables\n", "Tables saved in folder: 表格\n", "<PERSON>ript completed\n"]}], "source": ["import os\n", "import random\n", "import json\n", "from PIL import Image, ImageDraw, ImageFont\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import matplotlib\n", "from IPython.display import Image as IPImage, display\n", "from faker import Faker\n", "import datetime\n", "import numpy as np\n", "from faker.providers import DynamicProvider\n", "from faker.providers import internet, company, address, date_time, person, phone_number, ssn\n", "from faker.providers import color, credit_card, currency, file, job, lorem, misc, profile\n", "from faker.providers.person.zh_CN import Provider as CNPersonProvider\n", "import string\n", "import re\n", "\n", "# ====================== 全局配置部分 ======================\n", "# 输出配置\n", "OUTPUT_FOLDER = \"表格\"  # 主输出文件夹\n", "OUTPUT_THREE_LINE_FOLDER = os.path.join(OUTPUT_FOLDER, \"三线表\")  # 三线表输出文件夹\n", "OUTPUT_TEXT_TABLE_FOLDER = os.path.join(OUTPUT_FOLDER, \"二维表\")  # 二维表输出文件夹\n", "OUTPUT_NO_LINE_FOLDER = os.path.join(OUTPUT_FOLDER, \"无线表\")  # 无线表输出文件夹\n", "OUTPUT_ONE_DIM_FOLDER = os.path.join(OUTPUT_FOLDER, \"一维表\")  # 一维表输出文件夹\n", "OUTPUT_TWO_DIM_FOLDER = os.path.join(OUTPUT_FOLDER, \"文本表\")  # 文本表输出文件夹\n", "NUM_IMAGES = 5 # 生成图片数量\n", "\n", "# 表格类型\n", "TABLE_TYPES = [\"三线表\", \"文本表\", \"无线表\", \"一维表\", \"二维表\"]  # 可用的表格类型\n", "\n", "# 表格配置\n", "MIN_COLUMNS =2  # 最少列数（包括序号列）\n", "MAX_COLUMNS = 6  # 最多列数（包括序号列）\n", "MIN_ROWS = 2  # 最少数据行数\n", "MAX_ROWS = 15  # 最多数据行数\n", "\n", "# 样式配置\n", "TITLE_FONTSIZE = 14\n", "CELL_FONTSIZE = 12\n", "ROW_HEIGHT = 0.5\n", "DPI = 300\n", "\n", "# 中文字体配置\n", "matplotlib.rcParams['font.sans-serif'] = ['SimHei']\n", "matplotlib.rcParams['axes.unicode_minus'] = False\n", "\n", "# JSON配置文件路径\n", "CONFIG_FILE = \"table_config.json\"\n", "\n", "# ====================== 模型定义部分 ======================\n", "\n", "class ColumnDefinition:\n", "    \"\"\"列定义结构，包含列名、数据类型和生成参数\"\"\"\n", "    \n", "    def __init__(self, name, data_type=None, generator_params=None, data_category=None):\n", "        \"\"\"初始化列定义\n", "        Args:\n", "            name: 列名\n", "            data_type: 数据生成类型\n", "            generator_params: 生成器参数\n", "            data_category: 数据类别（text, numeric, date等）\n", "        \"\"\"\n", "        self.name = name\n", "        self.data_type = data_type  # 生成器类型\n", "        self.generator_params = generator_params or {}\n", "        self.data_category = data_category  # 数据类别\n", "    \n", "    def __str__(self):\n", "        \"\"\"返回列定义的字符串表示\"\"\"\n", "        return f\"列名: {self.name}, 类型: {self.data_type}, 类别: {self.data_category}\"\n", "\n", "class TableSchema:\n", "    \"\"\"表格模式类，用于定义表格的结构和内容生成规则\"\"\"\n", "    \n", "    def __init__(self, theme=None):\n", "        \"\"\"初始化表格模式，可选指定主题\"\"\"\n", "        # 加载JSON配置\n", "        self.config = self._load_config()\n", "        \n", "        # 获取可用主题列表\n", "        self.available_themes = list(self.config[\"themes\"].keys())\n", "        \n", "        # 如果未指定主题或指定的主题不在配置中，随机选择一个\n", "        if theme is None or theme not in self.available_themes:\n", "            self.theme = random.choice(self.available_themes)\n", "        else:\n", "            self.theme = theme\n", "            \n", "        self.column_definitions = []\n", "        self._define_schema()\n", "    \n", "    def _load_config(self):\n", "        \"\"\"加载JSON配置文件\"\"\"\n", "        try:\n", "            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:\n", "                return json.load(f)\n", "        except (FileNot<PERSON><PERSON>nd<PERSON><PERSON>r, json.JSONDecodeError) as e:\n", "            print(f\"Error loading config file: {e}\")\n", "            # 如果配置文件不存在或解析错误，返回一个空的配置\n", "            return {\"themes\": {}}\n", "    \n", "    def _define_schema(self):\n", "        \"\"\"根据当前主题定义表格模式，包括列结构和生成配置\"\"\"\n", "        if not self.theme:\n", "            # 如果没有指定主题，随机选择一个\n", "            self._select_random_theme()\n", "        \n", "        # 获取该主题的所有列配置\n", "        theme_config = self.config[\"themes\"].get(self.theme, {})\n", "        columns_config = theme_config.get(\"columns\", {})\n", "        \n", "        # 首先添加序号列\n", "        columns = [ColumnDefinition(\"序号\", \"index\", {}, \"text\")]\n", "        \n", "        # 收集所有其他可用列\n", "        available_columns = []\n", "        for col_name, col_config in columns_config.items():\n", "            # 跳过序号列，避免重复\n", "            if col_name == \"序号\":\n", "                continue\n", "                \n", "            generator_type = col_config.get(\"generator_type\")\n", "            params = col_config.get(\"params\", {})\n", "            data_category = col_config.get(\"data_category\", \"text\")  # 默认为文本类型\n", "            \n", "            available_columns.append(ColumnDefinition(\n", "                col_name, \n", "                generator_type, \n", "                params,\n", "                data_category\n", "            ))\n", "        \n", "        # 确保列数在MIN_COLUMNS和MAX_COLUMNS之间\n", "        # MIN_COLUMNS包含序号列，所以非序号列的最小数量是MIN_COLUMNS-1\n", "        num_columns_to_choose = random.randint(max(MIN_COLUMNS - 1, 0), min(MAX_COLUMNS - 1, len(available_columns)))\n", "        \n", "        # 如果可用列少于需要选择的列数，使用所有可用列\n", "        if len(available_columns) <= num_columns_to_choose:\n", "            selected_columns = available_columns\n", "        else:\n", "            # 否则随机选择指定数量的列\n", "            selected_columns = random.sample(available_columns, num_columns_to_choose)\n", "        \n", "        # 将选中的列添加到列定义中\n", "        columns.extend(selected_columns)\n", "        \n", "        # 保存列定义列表\n", "        self.column_definitions = columns\n", "    \n", "    def get_columns(self):\n", "        \"\"\"获取列名列表\"\"\"\n", "        return [col.name for col in self.column_definitions]\n", "    \n", "    def get_theme(self):\n", "        \"\"\"获取表格主题\"\"\"\n", "        return self.theme\n", "    \n", "    def get_theme_display_name(self):\n", "        \"\"\"获取主题显示名称\"\"\"\n", "        if self.theme in self.config[\"themes\"]:\n", "            return self.config[\"themes\"][self.theme].get(\"display_name\", self.theme)\n", "        return self.theme\n", "    \n", "    def get_table_title_template(self):\n", "        \"\"\"获取表格标题模板\"\"\"\n", "        if self.theme in self.config[\"themes\"]:\n", "            return self.config[\"themes\"][self.theme].get(\"table_title_template\", \"数据信息表\")\n", "        return \"数据信息表\"\n", "    \n", "    def get_column_definitions(self):\n", "        \"\"\"获取列定义列表\"\"\"\n", "        return self.column_definitions.copy()\n", "\n", "# ====================== 实用工具函数 ======================\n", "\n", "def get_faker():\n", "    \"\"\"获取已配置的Faker实例\"\"\"\n", "    fake = Faker(['zh_C<PERSON>', 'en_US', 'ja_<PERSON>'])  # 增加多语言支持\n", "    # 增加种子确保可重复性，但使用不同种子以增加多样性\n", "    Faker.seed(random.randint(0, 999999))\n", "    return fake\n", "\n", "# ====================== 数据生成器 ======================\n", "\n", "class DataGenerator:\n", "    \"\"\"基础数据生成器接口\"\"\"\n", "    \n", "    def generate(self, column_def, num_rows, theme):\n", "        \"\"\"生成数据的接口方法\n", "        \n", "        Args:\n", "            column_def: 列定义对象\n", "            num_rows: 需要生成的行数\n", "            theme: 表格主题\n", "            \n", "        Returns:\n", "            包含生成数据的列表\n", "        \"\"\"\n", "        pass\n", "\n", "class EnhancedDataGenerators(DataGenerator):\n", "    \"\"\"增强版数据生成器，基于JSON配置生成更合理的数据\"\"\"\n", "    \n", "    def __init__(self):\n", "        # 使用不同语言的Faker实例\n", "        self.fake_cn = Faker('zh_CN')\n", "        self.fake_en = Faker('en_US')\n", "        self.fake_jp = Faker('ja_<PERSON>')\n", "        self.fake = self.fake_cn  # 默认使用中文\n", "        \n", "        # 初始化随机数生成器状态，确保每次生成的数据都有差异\n", "        random.seed(datetime.datetime.now().microsecond)\n", "        np.random.seed(datetime.datetime.now().microsecond)\n", "    \n", "    # ====================== 基础生成方法 ======================\n", "    \n", "    def generate_generic_values(self, n, values_pool, weighted=False, weights=None):\n", "        \"\"\"从给定的值池中生成随机值\"\"\"\n", "        # 确保values_pool是一个序列，不是字典\n", "        if isinstance(values_pool, dict):\n", "            values_pool = list(values_pool.values())\n", "        \n", "        # 确保values_pool不为空\n", "        if not values_pool:\n", "            values_pool = [\"无数据\"]\n", "            \n", "        if weighted and weights:\n", "            return [random.choices(values_pool, weights=weights)[0] for _ in range(n)]\n", "        else:\n", "            return [random.choice(values_pool) for _ in range(n)]\n", "    \n", "    def generate_random_dates(self, n, start_year=1900, end_year=None, format=\"%Y-%m-%d\"):\n", "        \"\"\"生成指定范围内的随机日期\"\"\"\n", "        if end_year is None:\n", "            end_year = datetime.datetime.now().year\n", "        \n", "        return [self.fake.date_between(\n", "            start_date=datetime.date(start_year, 1, 1),\n", "            end_date=datetime.date(end_year, 12, 31)\n", "        ).strftime(format) for _ in range(n)]\n", "    \n", "    def generate_random_numbers(self, n, min_val, max_val, decimals=0):\n", "        \"\"\"生成随机数字\"\"\"\n", "        if decimals == 0:\n", "            return [random.randint(min_val, max_val) for _ in range(n)]\n", "        else:\n", "            return [round(random.uniform(min_val, max_val), decimals) for _ in range(n)]\n", "    \n", "    def generate_alphanumeric_pattern(self, n, patterns):\n", "        \"\"\"根据模式生成字母数字组合\"\"\"\n", "        result = []\n", "        for _ in range(n):\n", "            pattern = random.choice(patterns)\n", "            # 替换模式中的 # 为数字, X/x 为大/小写字母\n", "            output = \"\"\n", "            for char in pattern:\n", "                if char == '#':\n", "                    output += str(random.randint(0, 9))\n", "                elif char == 'X':\n", "                    output += random.choice(string.ascii_uppercase)\n", "                elif char == 'x':\n", "                    output += random.choice(string.ascii_lowercase)\n", "                elif char == '?':\n", "                    output += random.choice(string.ascii_letters)\n", "                else:\n", "                    output += char\n", "            result.append(output)\n", "        return result\n", "    \n", "    # ====================== 配置驱动的生成方法 ======================\n", "    \n", "    def generate(self, column_def, num_rows, theme):\n", "        \"\"\"实现基类接口，基于列定义和生成器类型生成数据\"\"\"\n", "        if column_def.name == \"序号\" or column_def.data_type == \"index\":\n", "            return list(range(1, num_rows + 1))\n", "        \n", "        # 根据生成器类型分发到对应的数据生成方法\n", "        generator_type = column_def.data_type\n", "        params = column_def.generator_params\n", "        \n", "        # 各种生成器类型的处理\n", "        if generator_type == \"categorical\":\n", "            return self._generate_categorical(num_rows, params)\n", "        elif generator_type == \"categorical_with_pattern\":\n", "            return self._generate_categorical_with_pattern(num_rows, params)\n", "        elif generator_type == \"faker_name\":\n", "            return self._generate_faker_name(num_rows, params)\n", "        elif generator_type == \"faker_company\":\n", "            return self._generate_faker_company(num_rows, params)\n", "        elif generator_type == \"faker_address\":\n", "            return self._generate_faker_address(num_rows, params)\n", "        elif generator_type == \"faker_job\":\n", "            return self._generate_faker_job(num_rows, params)\n", "        elif generator_type == \"faker_phone_number\":\n", "            return self._generate_faker_phone_number(num_rows, params)\n", "        elif generator_type == \"faker_email\":\n", "            return self._generate_faker_email(num_rows, params)\n", "        elif generator_type == \"faker_product_name\":\n", "            return self._generate_faker_product_name(num_rows, params)\n", "        elif generator_type == \"date_range\":\n", "            return self._generate_date_range(num_rows, params)\n", "        elif generator_type == \"integer_range\":\n", "            return self._generate_integer_range(num_rows, params)\n", "        elif generator_type == \"integer_range_with_unit\":\n", "            return self._generate_integer_range_with_unit(num_rows, params)\n", "        elif generator_type == \"numerical_range_formatted\":\n", "            return self._generate_numerical_range_formatted(num_rows, params)\n", "        elif generator_type == \"percentage\":\n", "            return self._generate_percentage(num_rows, params)\n", "        elif generator_type == \"alphanumeric_pattern\":\n", "            return self._generate_alphanumeric_pattern(num_rows, params)\n", "        elif generator_type == \"dimension_format\":\n", "            return self._generate_dimension_format(num_rows, params)\n", "        elif generator_type == \"score_range\":\n", "            return self._generate_score_range(num_rows, params)\n", "        elif generator_type == \"calculated_sum\":\n", "            return self._generate_calculated_sum(num_rows, params)\n", "        elif generator_type == \"rank\":\n", "            return self._generate_rank(num_rows, params)\n", "        else:\n", "            # 如果没有匹配的生成器类型，生成默认数据\n", "            return [f\"数据-{i+1}\" for i in range(num_rows)]\n", "    \n", "    # ====================== 具体生成方法实现 ======================\n", "    \n", "    def _generate_categorical(self, n, params):\n", "        \"\"\"从分类值列表中生成数据\"\"\"\n", "        values = params.get(\"values\", [\"无数据\"])\n", "        # 确保values是一个列表而不是字典\n", "        if isinstance(values, dict):\n", "            values = list(values.values())  # 如果是字典，将值转换为列表\n", "        return self.generate_generic_values(n, values)\n", "    \n", "    def _generate_categorical_with_pattern(self, n, params):\n", "        \"\"\"根据前缀+后缀模式生成分类数据\"\"\"\n", "        prefixes = params.get(\"prefixes\", [\"默认\"])\n", "        suffixes = params.get(\"suffixes\", [\"项目\"])\n", "        \n", "        # 确保prefixes和suffixes是列表而不是字典\n", "        if isinstance(prefixes, dict):\n", "            prefixes = list(prefixes.values())\n", "        if isinstance(suffixes, dict):\n", "            suffixes = list(suffixes.values())\n", "            \n", "        # 确保不为空\n", "        if not prefixes:\n", "            prefixes = [\"默认\"]\n", "        if not suffixes:\n", "            suffixes = [\"项目\"]\n", "            \n", "        result = []\n", "        for _ in range(n):\n", "            prefix = random.choice(prefixes)\n", "            suffix = random.choice(suffixes)\n", "            result.append(f\"{prefix}{suffix}\")\n", "        return result\n", "    \n", "    def _generate_faker_name(self, n, params):\n", "        \"\"\"使用Faker生成姓名\"\"\"\n", "        locale = params.get(\"locale\", \"zh_CN\")\n", "        faker = Faker(locale)\n", "        return [faker.name() for _ in range(n)]\n", "    \n", "    def _generate_faker_company(self, n, params):\n", "        \"\"\"使用Faker生成公司名称\"\"\"\n", "        locale = params.get(\"locale\", \"zh_CN\")\n", "        faker = Faker(locale)\n", "        return [faker.company() for _ in range(n)]\n", "    \n", "    def _generate_faker_address(self, n, params):\n", "        \"\"\"使用Faker生成地址\"\"\"\n", "        locale = params.get(\"locale\", \"zh_CN\")\n", "        faker = Faker(locale)\n", "        return [faker.address() for _ in range(n)]\n", "    \n", "    def _generate_faker_job(self, n, params):\n", "        \"\"\"使用Faker生成职业\"\"\"\n", "        locale = params.get(\"locale\", \"zh_CN\")\n", "        faker = Faker(locale)\n", "        return [faker.job() for _ in range(n)]\n", "    \n", "    def _generate_faker_phone_number(self, n, params):\n", "        \"\"\"使用Faker生成电话号码\"\"\"\n", "        locale = params.get(\"locale\", \"zh_CN\")\n", "        faker = Faker(locale)\n", "        return [faker.phone_number() for _ in range(n)]\n", "    \n", "    def _generate_faker_email(self, n, params):\n", "        \"\"\"使用Faker生成电子邮箱\"\"\"\n", "        locale = params.get(\"locale\", \"zh_CN\")\n", "        faker = Faker(locale)\n", "        return [faker.email() for _ in range(n)]\n", "    \n", "    def _generate_faker_product_name(self, n, params):\n", "        \"\"\"使用自定义逻辑生成产品名称\"\"\"\n", "        locale = params.get(\"locale\", \"zh_CN\")\n", "        faker = Faker(locale)\n", "        product_types = [\"智能手机\", \"笔记本电脑\", \"平板电脑\", \"智能手表\", \"无线耳机\", \"智能音箱\"]\n", "        brands = [\"苹果\", \"华为\", \"小米\", \"三星\", \"索尼\", \"戴尔\", \"联想\"]\n", "        models = [\"Pro\", \"Max\", \"Plus\", \"Lite\", \"Ultra\", \"Mini\", \"SE\", \"Air\"]\n", "        \n", "        result = []\n", "        for _ in range(n):\n", "            brand = random.choice(brands)\n", "            product = random.choice(product_types)\n", "            model = random.choice(models) if random.random() > 0.3 else \"\"\n", "            year = str(random.randint(2020, 2024)) if random.random() > 0.7 else \"\"\n", "            result.append(f\"{brand} {product}{' ' + model if model else ''}{' ' + year if year else ''}\")\n", "        return result\n", "    \n", "    def _generate_date_range(self, n, params):\n", "        \"\"\"生成日期范围\"\"\"\n", "        start_year = params.get(\"start_year\", 2000)\n", "        end_year = params.get(\"end_year\", datetime.datetime.now().year)\n", "        date_format = params.get(\"format\", \"%Y-%m-%d\")\n", "        return self.generate_random_dates(n, start_year, end_year, date_format)\n", "    \n", "    def _generate_integer_range(self, n, params):\n", "        \"\"\"生成整数范围\"\"\"\n", "        min_val = params.get(\"min\", 0)\n", "        max_val = params.get(\"max\", 100)\n", "        return self.generate_random_numbers(n, min_val, max_val, decimals=0)\n", "    \n", "    def _generate_integer_range_with_unit(self, n, params):\n", "        \"\"\"生成带单位的整数范围\"\"\"\n", "        min_val = params.get(\"min\", 0)\n", "        max_val = params.get(\"max\", 100)\n", "        unit = params.get(\"unit\", \"\")\n", "        numbers = self.generate_random_numbers(n, min_val, max_val, decimals=0)\n", "        return [f\"{num}{unit}\" for num in numbers]\n", "    \n", "    def _generate_numerical_range_formatted(self, n, params):\n", "        \"\"\"生成格式化的数值范围\"\"\"\n", "        min_value = params.get(\"min_value\", 0)\n", "        max_value = params.get(\"max_value\", 1000)\n", "        decimals = params.get(\"decimals\", 0)\n", "        format_string = params.get(\"format_string\", \"{}\")\n", "        \n", "        numbers = self.generate_random_numbers(n, min_value, max_value, decimals)\n", "        \n", "        # 修复格式化处理\n", "        result = []\n", "        for num in numbers:\n", "            try:\n", "                # 尝试使用位置参数格式化（支持 {:,}、{:.2f} 等形式）\n", "                formatted = format_string.format(num)\n", "                result.append(formatted)\n", "            except (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>):\n", "                try:\n", "                    # 尝试使用命名参数格式化（支持 {value} 形式）\n", "                    formatted = format_string.format(value=num)\n", "                    result.append(formatted)\n", "                except:\n", "                    # 兜底方案：直接拼接\n", "                    result.append(f\"{num}{format_string}\")\n", "        \n", "        return result\n", "    \n", "    def _generate_percentage(self, n, params):\n", "        \"\"\"生成百分比\"\"\"\n", "        min_value = params.get(\"min_value\", 0)\n", "        max_value = params.get(\"max_value\", 100)\n", "        decimals = params.get(\"decimals\", 0)\n", "        \n", "        numbers = self.generate_random_numbers(n, min_value, max_value, decimals)\n", "        return [f\"{num}%\" for num in numbers]\n", "    \n", "    def _generate_alphanumeric_pattern(self, n, params):\n", "        \"\"\"生成字母数字模式\"\"\"\n", "        patterns = params.get(\"patterns\", [\"A-###\"])\n", "        return self.generate_alphanumeric_pattern(n, patterns)\n", "    \n", "    def _generate_dimension_format(self, n, params):\n", "        \"\"\"生成尺寸格式\"\"\"\n", "        min_length = params.get(\"min_length\", 1000)\n", "        max_length = params.get(\"max_length\", 5000)\n", "        min_width = params.get(\"min_width\", 500)\n", "        max_width = params.get(\"max_width\", 2000)\n", "        min_height = params.get(\"min_height\", 300)\n", "        max_height = params.get(\"max_height\", 1500)\n", "        format_string = params.get(\"format_string\", \"{}×{}×{}mm\")\n", "        \n", "        result = []\n", "        for _ in range(n):\n", "            length = random.randint(min_length, max_length)\n", "            width = random.randint(min_width, max_width)\n", "            height = random.randint(min_height, max_height)\n", "            result.append(format_string.format(length, width, height))\n", "        return result\n", "    \n", "    def _generate_score_range(self, n, params):\n", "        \"\"\"生成考试成绩范围\"\"\"\n", "        min_val = params.get(\"min\", 60)\n", "        max_val = params.get(\"max\", 100)\n", "        return self.generate_random_numbers(n, min_val, max_val, decimals=0)\n", "    \n", "    def _generate_calculated_sum(self, n, params):\n", "        \"\"\"暂时返回随机总分，实际使用时需要根据其他列计算\"\"\"\n", "        # 这个方法需要在TableGenerator中处理，因为它依赖于其他列的值\n", "        min_val = params.get(\"min\", 300)\n", "        max_val = params.get(\"max\", 600)\n", "        return self.generate_random_numbers(n, min_val, max_val, decimals=0)\n", "    \n", "    def _generate_rank(self, n, params):\n", "        \"\"\"生成排名，实际使用时需要根据指定列排序\"\"\"\n", "        # 这个方法需要在TableGenerator中处理，因为它依赖于其他列的值\n", "        return list(range(1, n + 1))\n", "\n", "# ====================== 表格渲染 ======================\n", "\n", "class TableRenderer:\n", "    \"\"\"表格渲染器，将数据渲染为表格图像\"\"\"\n", "    \n", "    def __init__(self, output_folder=OUTPUT_FOLDER, dpi=DPI, \n", "                 title_fontsize=TITLE_FONTSIZE, cell_fontsize=CELL_FONTSIZE, \n", "                 row_height=ROW_HEIGHT):\n", "        \"\"\"初始化表格渲染器\"\"\"\n", "        self.output_folder = output_folder\n", "        self.dpi = dpi\n", "        self.title_fontsize = title_fontsize\n", "        self.cell_fontsize = cell_fontsize\n", "        self.row_height = row_height\n", "        \n", "        # 确保输出文件夹存在\n", "        if not os.path.exists(output_folder):\n", "            os.makedirs(output_folder)\n", "    \n", "    def calculate_column_widths(self, columns, data):\n", "        \"\"\"计算列宽度，根据内容长度动态调整，并处理长文本\"\"\"\n", "        col_widths = []\n", "        \n", "        # 为每列计算最大内容宽度\n", "        for idx, col in enumerate(columns):\n", "            # 列标题长度\n", "            max_width = len(str(col)) * 0.25  # 增加文本宽度系数\n", "            \n", "            # 数据内容长度\n", "            if not data.empty:\n", "                col_data = data.iloc[:, idx].astype(str)\n", "                data_max_len = col_data.str.len().max() * 0.25  # 增加文本宽度系数\n", "                max_width = max(max_width, data_max_len)\n", "            \n", "            # 确保最小宽度并限制最大宽度\n", "            max_width = max(1.5, min(max_width, 8.0))  # 设置列宽上限，防止过宽\n", "            col_widths.append(max_width)\n", "        \n", "        return col_widths\n", "    \n", "    def wrap_text(self, text, max_width):\n", "        \"\"\"将长文本按照最大宽度进行换行处理\"\"\"\n", "        if len(text) <= max_width:\n", "            return text\n", "        \n", "        # 简单的文本换行处理\n", "        words = text.replace('，', ',').replace('。', '.').replace('；', ';').split()\n", "        lines = []\n", "        current_line = []\n", "        current_length = 0\n", "        \n", "        for word in words:\n", "            # 如果当前单词加上当前行已有内容会超出最大宽度，则开始新行\n", "            if current_length + len(word) + 1 > max_width:\n", "                if current_line:  # 如果当前行不为空，则保存当前行\n", "                    lines.append(' '.join(current_line))\n", "                    current_line = []\n", "                    current_length = 0\n", "                \n", "                # 如果单个词超过最大宽度，则分割该词\n", "                if len(word) > max_width:\n", "                    for i in range(0, len(word), max_width):\n", "                        chunk = word[i:i + max_width]\n", "                        lines.append(chunk)\n", "                else:\n", "                    current_line = [word]\n", "                    current_length = len(word)\n", "            else:\n", "                current_line.append(word)\n", "                current_length += len(word) + 1  # +1 是为了空格\n", "        \n", "        if current_line:  # 添加最后一行\n", "            lines.append(' '.join(current_line))\n", "        \n", "        return '\\n'.join(lines)\n", "    \n", "    def render_three_line_table(self, df, img_index, theme):\n", "        \"\"\"将数据渲染为三线表格图像并保存\"\"\"\n", "        # 设置matplotlib不使用GUI后端\n", "        matplotlib.use('Agg')\n", "        \n", "        # 处理数据：转换长文本为多行文本\n", "        df_processed = df.copy()\n", "        max_text_width = 20  # 最大文本宽度，超过则换行\n", "        \n", "        for col in df_processed.columns:\n", "            df_processed[col] = df_processed[col].astype(str).apply(\n", "                lambda x: self.wrap_text(x, max_text_width)\n", "            )\n", "        \n", "        # 准备数据\n", "        columns = df_processed.columns.tolist()\n", "        data_rows = df_processed.values.tolist()\n", "        table_data = [columns] + data_rows\n", "        \n", "        # 计算列宽度，考虑实际数据\n", "        cell_widths = self.calculate_column_widths(columns, df_processed)\n", "        \n", "        # 创建图像\n", "        fig = plt.figure(figsize=(sum(cell_widths) + 1, self.row_height * (len(table_data) + 2)))\n", "        ax = plt.gca()\n", "        ax.axis('off')\n", "        \n", "        # 创建表格\n", "        table = ax.table(\n", "            cellText=data_rows,\n", "            colLabels=columns,\n", "            cellLoc='center',\n", "            loc='center',\n", "            bbox=[0.0, 0.0, 1.0, 1.0]\n", "        )\n", "        \n", "        # 调整表格样式\n", "        table.auto_set_font_size(False)\n", "        table.set_fontsize(self.cell_fontsize)\n", "        \n", "        # 设置列宽\n", "        table_width = sum(cell_widths)\n", "        for i, width in enumerate(cell_widths):\n", "            table.auto_set_column_width([i])\n", "            for cell in [key for key in table._cells if key[1] == i]:\n", "                table._cells[cell].set_width(width / table_width)\n", "                \n", "        # 三线表特有样式：只保留顶线、表头底线和底线\n", "        for pos, cell in table._cells.items():\n", "            cell.set_height(self.row_height / len(table_data))\n", "            \n", "            # 默认不显示边框\n", "            cell.set_edgecolor('none')\n", "            \n", "            # 设置标题行样式\n", "            if pos[0] == 0:  # 标题行\n", "                cell.set_text_props(weight='bold')\n", "                cell.set_facecolor('#f5f5f5')\n", "                \n", "                # 顶线和表头下方横线\n", "                edge_chars = 'T'  # 顶线\n", "                if pos[1] == 0:  # 第一列的左边线\n", "                    edge_chars += 'L'\n", "                if pos[1] == len(columns) - 1:  # 最后一列的右边线\n", "                    edge_chars += 'R'\n", "                    \n", "                # 设置底边框（表头下方横线）\n", "                edge_chars += 'B'\n", "                \n", "                cell.visible_edges = edge_chars\n", "                    \n", "            elif pos[0] == len(data_rows):  # 最后一行\n", "                # 设置底线\n", "                edge_chars = 'B'  # 底线\n", "                if pos[1] == 0:  # 第一列的左边线\n", "                    edge_chars += 'L'\n", "                if pos[1] == len(columns) - 1:  # 最后一列的右边线\n", "                    edge_chars += 'R'\n", "                    \n", "                cell.visible_edges = edge_chars\n", "                \n", "            else:  # 普通数据行，没有横线\n", "                edge_chars = ''\n", "                if pos[1] == 0:  # 第一列的左边线\n", "                    edge_chars += 'L'\n", "                if pos[1] == len(columns) - 1:  # 最后一列的右边线\n", "                    edge_chars += 'R'\n", "                    \n", "                cell.visible_edges = edge_chars\n", "                \n", "            # 设置边框线宽\n", "            if pos[0] in [0, 1] or pos[0] == len(table_data):\n", "                cell.set_linewidth(1.5)  # 顶线、表头下横线和底线加粗\n", "            else:\n", "                cell.set_linewidth(0.75)\n", "        \n", "        # 获取并设置表格标题\n", "        schema = TableSchema(theme)\n", "        table_title = f\"{schema.get_table_title_template()} {img_index+1}\"\n", "        ax.set_title(table_title, fontsize=self.title_fontsize, pad=20)\n", "        \n", "        # 创建输出文件路径\n", "        display_name = schema.get_theme_display_name()\n", "        output_path = os.path.join(OUTPUT_THREE_LINE_FOLDER, f\"{display_name}三线表_{img_index+1}.jpg\")\n", "        \n", "        # 确保输出文件夹存在\n", "        if not os.path.exists(OUTPUT_THREE_LINE_FOLDER):\n", "            os.makedirs(OUTPUT_THREE_LINE_FOLDER)\n", "        \n", "        # 保存图片\n", "        plt.tight_layout()\n", "        plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight')\n", "        plt.close()\n", "        \n", "        return output_path\n", "\n", "    def render_text_table(self, df, img_index, theme):\n", "        \"\"\"将数据渲染为文本表格图像并保存（带全部边框）\"\"\"\n", "        # 设置matplotlib不使用GUI后端\n", "        matplotlib.use('Agg')\n", "        \n", "        # 处理数据：转换长文本为多行文本\n", "        df_processed = df.copy()\n", "        max_text_width = 20  # 最大文本宽度，超过则换行\n", "        \n", "        for col in df_processed.columns:\n", "            df_processed[col] = df_processed[col].astype(str).apply(\n", "                lambda x: self.wrap_text(x, max_text_width)\n", "            )\n", "        \n", "        # 准备数据\n", "        columns = df_processed.columns.tolist()\n", "        data_rows = df_processed.values.tolist()\n", "        table_data = [columns] + data_rows\n", "        \n", "        # 计算列宽度，考虑实际数据\n", "        cell_widths = self.calculate_column_widths(columns, df_processed)\n", "        \n", "        # 创建图像\n", "        fig = plt.figure(figsize=(sum(cell_widths) + 1, self.row_height * (len(table_data) + 2)))\n", "        ax = plt.gca()\n", "        ax.axis('off')\n", "        \n", "        # 创建表格\n", "        table = ax.table(\n", "            cellText=data_rows,\n", "            colLabels=columns,\n", "            cellLoc='center',\n", "            loc='center',\n", "            bbox=[0.0, 0.0, 1.0, 1.0]\n", "        )\n", "        \n", "        # 调整表格样式\n", "        table.auto_set_font_size(False)\n", "        table.set_fontsize(self.cell_fontsize)\n", "        \n", "        # 设置列宽\n", "        table_width = sum(cell_widths)\n", "        for i, width in enumerate(cell_widths):\n", "            table.auto_set_column_width([i])\n", "            for cell in [key for key in table._cells if key[1] == i]:\n", "                table._cells[cell].set_width(width / table_width)\n", "                \n", "        # 设置行高并添加边框\n", "        for pos, cell in table._cells.items():\n", "            cell.set_height(self.row_height / len(table_data))\n", "            cell.set_edgecolor('black')\n", "            \n", "            # 设置标题行和底部粗线\n", "            if pos[0] == 0:  # 标题行\n", "                cell.set_text_props(weight='bold')\n", "                cell.set_linewidth(1.5)\n", "                cell.set_facecolor('#f2f2f2')\n", "            else:  # 数据行\n", "                cell.set_linewidth(0.75)\n", "                \n", "            # 顶部和底部边框加粗\n", "            if pos[0] in [0, 1] or pos[0] == len(table_data) - 1:\n", "                cell.set_linewidth(1.5)\n", "        \n", "        # 获取并设置表格标题\n", "        schema = TableSchema(theme)\n", "        table_title = f\"{schema.get_table_title_template()} {img_index+1}\"\n", "        ax.set_title(table_title, fontsize=self.title_fontsize, pad=20)\n", "        \n", "        # 创建输出文件路径\n", "        display_name = schema.get_theme_display_name()\n", "        output_path = os.path.join(OUTPUT_TEXT_TABLE_FOLDER, f\"{display_name}文本表_{img_index+1}.jpg\")\n", "        \n", "        # 确保输出文件夹存在\n", "        if not os.path.exists(OUTPUT_TEXT_TABLE_FOLDER):\n", "            os.makedirs(OUTPUT_TEXT_TABLE_FOLDER)\n", "        \n", "        # 保存图片\n", "        plt.tight_layout()\n", "        plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight')\n", "        plt.close()\n", "        \n", "        return output_path\n", "\n", "    def render_no_line_table(self, df, img_index, theme):\n", "        \"\"\"将数据渲染为无线表格图像并保存（只保留表头下方横线）\"\"\"\n", "        # 设置matplotlib不使用GUI后端\n", "        matplotlib.use('Agg')\n", "        \n", "        # 准备数据\n", "        columns = df.columns.tolist()\n", "        data_rows = df.values.tolist()\n", "        table_data = [columns] + data_rows\n", "        \n", "        # 计算列宽度，考虑实际数据\n", "        cell_widths = self.calculate_column_widths(columns, df)\n", "        \n", "        # 计算表格尺寸\n", "        ncols = len(columns)\n", "        nrows = len(table_data)\n", "        total_width = sum(cell_widths)\n", "        total_height = self.row_height * (nrows + 1.5)  # 额外空间用于标题\n", "        \n", "        # 创建图像\n", "        plt.figure(figsize=(total_width + 1, total_height))\n", "        ax = plt.gca()\n", "        ax.axis('off')\n", "        \n", "        # 获取表格标题\n", "        schema = TableSchema(theme)\n", "        table_title = f\"{schema.get_table_title_template()} {img_index+1}\"\n", "        plt.title(table_title, fontsize=self.title_fontsize, pad=10)\n", "        \n", "        # 计算表格区域\n", "        x_start = 0\n", "        x_end = total_width\n", "        y_head_sep = nrows - 1\n", "        \n", "        # 放置单元格文本\n", "        for row in range(nrows):\n", "            for col in range(ncols):\n", "                value = table_data[row][col]\n", "                plt.text(sum(cell_widths[:col]) + cell_widths[col] / 2, nrows - row - 0.5, str(value),\n", "                       ha='center', va='center', fontsize=self.cell_fontsize)\n", "        \n", "        # 只画表头底线\n", "        plt.plot([x_start, x_end], [y_head_sep, y_head_sep], color='black', linewidth=1)  # 表头底线\n", "        \n", "        # 设置坐标轴范围\n", "        plt.xlim(-0.5, total_width + 0.5)\n", "        plt.ylim(-0.5, nrows + 0.5)\n", "        \n", "        # 创建输出文件路径\n", "        display_name = schema.get_theme_display_name()\n", "        output_path = os.path.join(OUTPUT_NO_LINE_FOLDER, f\"{display_name}无线表_{img_index+1}.jpg\")\n", "        \n", "        # 确保输出文件夹存在\n", "        if not os.path.exists(OUTPUT_NO_LINE_FOLDER):\n", "            os.makedirs(OUTPUT_NO_LINE_FOLDER)\n", "        \n", "        # 保存图片\n", "        plt.subplots_adjust(left=0.05, right=0.95, top=0.9, bottom=0.05)\n", "        plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight')\n", "        plt.close()\n", "        \n", "        return output_path\n", "\n", "    def render_one_dim_table(self, df, img_index, theme):\n", "        \"\"\"将数据渲染为一维表格图像（只含序号和一个数据列）\"\"\"\n", "        # 设置matplotlib不使用GUI后端\n", "        matplotlib.use('Agg')\n", "        \n", "        # 确保只有2列：序号列和一个数据列\n", "        if \"序号\" not in df.columns:\n", "            # 如果没有序号列，添加一个（不太可能出现，但以防万一）\n", "            df[\"序号\"] = range(1, len(df) + 1)\n", "            \n", "        # 确保序号列是第一列\n", "        columns = [\"序号\"]\n", "        \n", "        # 选择非序号列中的一列\n", "        data_columns = [col for col in df.columns if col != \"序号\"]\n", "        if data_columns:\n", "            selected_col = random.choice(data_columns)\n", "            columns.append(selected_col)\n", "            # 只保留序号列和选中的数据列\n", "            df = df[columns].copy()\n", "        else:\n", "            # 极端情况：只有序号列\n", "            # 创建一个新的数据列\n", "            new_col = \"数据\"\n", "            df[new_col] = [f\"数据-{i}\" for i in range(1, len(df) + 1)]\n", "            columns.append(new_col)\n", "        \n", "        # 处理数据：转换长文本为多行文本\n", "        df_processed = df.copy()\n", "        max_text_width = 20  # 最大文本宽度，超过则换行\n", "        \n", "        for col in df_processed.columns:\n", "            df_processed[col] = df_processed[col].astype(str).apply(\n", "                lambda x: self.wrap_text(x, max_text_width)\n", "            )\n", "        \n", "        # 准备数据\n", "        columns = df_processed.columns.tolist()\n", "        data_rows = df_processed.values.tolist()\n", "        table_data = [columns] + data_rows\n", "        \n", "        # 计算列宽度，考虑实际数据\n", "        cell_widths = self.calculate_column_widths(columns, df_processed)\n", "        \n", "        # 创建图像\n", "        fig = plt.figure(figsize=(sum(cell_widths) + 1, self.row_height * (len(table_data) + 2)))\n", "        ax = plt.gca()\n", "        ax.axis('off')\n", "        \n", "        # 创建表格\n", "        table = ax.table(\n", "            cellText=data_rows,\n", "            colLabels=columns,\n", "            cellLoc='center',\n", "            loc='center',\n", "            bbox=[0.0, 0.0, 1.0, 1.0]\n", "        )\n", "        \n", "        # 调整表格样式\n", "        table.auto_set_font_size(False)\n", "        table.set_fontsize(self.cell_fontsize)\n", "        \n", "        # 设置列宽\n", "        table_width = sum(cell_widths)\n", "        for i, width in enumerate(cell_widths):\n", "            table.auto_set_column_width([i])\n", "            for cell in [key for key in table._cells if key[1] == i]:\n", "                table._cells[cell].set_width(width / table_width)\n", "                \n", "        # 设置行高并添加边框\n", "        for pos, cell in table._cells.items():\n", "            cell.set_height(self.row_height / len(table_data))\n", "            cell.set_edgecolor('black')\n", "            \n", "            # 设置标题行和第一列的样式\n", "            if pos[0] == 0:  # 标题行\n", "                cell.set_text_props(weight='bold', color='black')\n", "                cell.set_facecolor('#f2f2f2')\n", "            else:  # 数据行\n", "                cell.set_text_props(color='black')\n", "                if pos[1] == 0:  # 第一列（序号）\n", "                    cell.set_text_props(weight='bold')\n", "                cell.set_facecolor('white')\n", "                \n", "            # 设置边框线宽\n", "            cell.set_linewidth(0.75)\n", "            # 顶部和底部边框加粗\n", "            if pos[0] in [0, 1] or pos[0] == len(table_data):\n", "                cell.set_linewidth(1.5)\n", "        \n", "        # 获取并设置表格标题\n", "        schema = TableSchema(theme)\n", "        table_title = f\"{schema.get_table_title_template()} {img_index+1}\"\n", "        ax.set_title(table_title, fontsize=self.title_fontsize, pad=20)\n", "        \n", "        # 创建输出文件路径\n", "        display_name = schema.get_theme_display_name()\n", "        output_path = os.path.join(OUTPUT_ONE_DIM_FOLDER, f\"{display_name}一维表_{img_index+1}.jpg\")\n", "        \n", "        # 确保输出文件夹存在\n", "        if not os.path.exists(OUTPUT_ONE_DIM_FOLDER):\n", "            os.makedirs(OUTPUT_ONE_DIM_FOLDER)\n", "        \n", "        # 保存图片\n", "        plt.tight_layout()\n", "        plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight')\n", "        plt.close()\n", "        \n", "        return output_path\n", "\n", "    def render_two_dim_table(self, df, img_index, theme):\n", "        \"\"\"将数据渲染为二维表格图像（专注于文本类型列，并将非文本列转换为文本格式）\"\"\"\n", "        # 设置matplotlib不使用GUI后端\n", "        matplotlib.use('Agg')\n", "        \n", "        # 获取表格结构定义\n", "        schema = TableSchema(theme)\n", "        column_definitions = schema.get_column_definitions()\n", "        \n", "        # 创建列名到列定义的映射，用于快速查找\n", "        col_def_map = {col_def.name: col_def for col_def in column_definitions}\n", "        \n", "        # 分类所有列：文本列和非文本列\n", "        text_columns = []  # 纯文本类型的列\n", "        non_text_columns = []  # 非文本类型的列（数值、日期等）\n", "        \n", "        for col in df.columns:\n", "            # 序号列单独处理\n", "            if col == \"序号\":\n", "                continue\n", "                \n", "            # 检查列是否在定义中，且是文本类型\n", "            if col in col_def_map and col_def_map[col].data_category == \"text\":\n", "                text_columns.append(col)\n", "            elif col in df.columns:  # 所有其他非文本列\n", "                non_text_columns.append(col)\n", "        \n", "        # 1. 按照全局MIN_COLUMNS/MAX_COLUMNS设置确定最终列数\n", "        # 减1是因为序号列要单独计算\n", "        target_col_count = random.randint(min(MIN_COLUMNS - 1, len(df.columns) - 1), \n", "                                        min(MAX_COLUMNS - 1, len(df.columns) - 1))\n", "        \n", "        # 2. 优先选择文本列\n", "        final_columns = []\n", "        if len(text_columns) > target_col_count:\n", "            # 如果文本列过多，随机选择\n", "            final_columns = random.sample(text_columns, target_col_count)\n", "        else:\n", "            # 所有文本列都保留\n", "            final_columns = text_columns.copy()\n", "            \n", "            # 3. 文本列不足时，将非文本列转换为文本格式\n", "            remaining_slots = target_col_count - len(final_columns)\n", "            if remaining_slots > 0 and non_text_columns:\n", "                # 确定需要添加的非文本列数量\n", "                cols_to_add = min(remaining_slots, len(non_text_columns))\n", "                selected_non_text = random.sample(non_text_columns, cols_to_add)\n", "                \n", "                # 为每个选中的非文本列添加适当的文本前缀或后缀\n", "                for col in selected_non_text:\n", "                    df_processed = df.copy()\n", "                    \n", "                    # 根据列类型添加合适的前缀或后缀\n", "                    if col in col_def_map:\n", "                        data_category = col_def_map[col].data_category\n", "                        \n", "                        if data_category == \"numeric\":\n", "                            # 为数值列添加适当的后缀\n", "                            suffixes = [\"个\", \"元\", \"件\", \"次\", \"分\", \"米\", \"千克\", \"%\", \"人\"]\n", "                            suffix = random.choice(suffixes)\n", "                            df[col] = df[col].astype(str) + suffix\n", "                        \n", "                        elif data_category == \"date\":\n", "                            # 为日期列添加前缀\n", "                            prefixes = [\"日期: \", \"时间: \", \"记录于: \", \"登记: \"]\n", "                            prefix = random.choice(prefixes)\n", "                            df[col] = prefix + df[col].astype(str)\n", "                        \n", "                        else:\n", "                            # 其他类型列\n", "                            misc_prefixes = [\"信息: \", \"数据: \", \"记录: \"]\n", "                            prefix = random.choice(misc_prefixes)\n", "                            df[col] = prefix + df[col].astype(str)\n", "                    else:\n", "                        # 未知类型列，直接转为字符串\n", "                        df[col] = df[col].astype(str)\n", "                    \n", "                    final_columns.append(col)\n", "        \n", "        # 4. 如果经过上述处理后，列数仍然不足，添加备注列\n", "        while len(final_columns) < target_col_count:\n", "            note_col = f\"备注{len(final_columns) + 1}\"\n", "            df[note_col] = [f\"补充信息{i+1}\" for i in range(len(df))]\n", "            final_columns.append(note_col)\n", "        \n", "        # 确保有序号列\n", "        if \"序号\" not in df.columns:\n", "            df[\"序号\"] = range(1, len(df) + 1)\n", "        \n", "        # 准备最终的列顺序，确保序号在第一位\n", "        final_columns = [\"序号\"] + final_columns\n", "            \n", "        # 只保留筛选后的列\n", "        df_filtered = df[final_columns].copy()\n", "        \n", "        # 处理数据：转换长文本为多行文本\n", "        max_text_width = 20  # 最大文本宽度，超过则换行\n", "        for col in df_filtered.columns:\n", "            df_filtered[col] = df_filtered[col].astype(str).apply(\n", "                lambda x: self.wrap_text(x, max_text_width)\n", "            )\n", "        \n", "        # 准备数据\n", "        columns = df_filtered.columns.tolist()\n", "        data_rows = df_filtered.values.tolist()\n", "        table_data = [columns] + data_rows\n", "        \n", "        # 计算列宽度，考虑实际数据\n", "        cell_widths = self.calculate_column_widths(columns, df_filtered)\n", "        \n", "        # 创建图像\n", "        fig = plt.figure(figsize=(sum(cell_widths) + 1, self.row_height * (len(table_data) + 2)))\n", "        ax = plt.gca()\n", "        ax.axis('off')\n", "        \n", "        # 创建表格\n", "        table = ax.table(\n", "            cellText=data_rows,\n", "            colLabels=columns,\n", "            cellLoc='center',\n", "            loc='center',\n", "            bbox=[0.0, 0.0, 1.0, 1.0]\n", "        )\n", "        \n", "        # 调整表格样式\n", "        table.auto_set_font_size(False)\n", "        table.set_fontsize(self.cell_fontsize)\n", "        \n", "        # 设置列宽\n", "        table_width = sum(cell_widths)\n", "        for i, width in enumerate(cell_widths):\n", "            table.auto_set_column_width([i])\n", "            for cell in [key for key in table._cells if key[1] == i]:\n", "                table._cells[cell].set_width(width / table_width)\n", "                \n", "        # 设置行高并添加边框\n", "        for pos, cell in table._cells.items():\n", "            cell.set_height(self.row_height / len(table_data))\n", "            cell.set_edgecolor('black')\n", "            \n", "            # 设置标题行和第一列的样式\n", "            if pos[0] == 0:  # 标题行\n", "                cell.set_text_props(weight='bold', color='black')\n", "                cell.set_facecolor('#e6f3ff')  # 使用不同的背景色区分于文本表\n", "            else:  # 数据行\n", "                cell.set_text_props(color='black')\n", "                if pos[1] == 0:  # 第一列（序号）\n", "                    cell.set_text_props(weight='bold')\n", "                cell.set_facecolor('white')\n", "                \n", "            # 设置边框线宽\n", "            cell.set_linewidth(0.75)\n", "            # 顶部和底部边框加粗\n", "            if pos[0] in [0, 1] or pos[0] == len(table_data):\n", "                cell.set_linewidth(1.5)\n", "        \n", "        # 获取并设置表格标题\n", "        table_title = f\"{schema.get_table_title_template()} {img_index+1}\"\n", "        ax.set_title(table_title, fontsize=self.title_fontsize, pad=20)\n", "        \n", "        # 创建输出文件路径\n", "        display_name = schema.get_theme_display_name()\n", "        output_path = os.path.join(OUTPUT_TWO_DIM_FOLDER, f\"{display_name}文本表_{img_index+1}.jpg\")\n", "        \n", "        # 确保输出文件夹存在\n", "        if not os.path.exists(OUTPUT_TWO_DIM_FOLDER):\n", "            os.makedirs(OUTPUT_TWO_DIM_FOLDER)\n", "        \n", "        # 保存图片\n", "        plt.tight_layout()\n", "        plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight')\n", "        plt.close()\n", "        \n", "        return output_path\n", "\n", "# ====================== 表格生成协调部分 ======================\n", "\n", "class TableGenerator:\n", "    \"\"\"表格生成器，协调整个表格生成过程\"\"\"\n", "    \n", "    def __init__(self, output_folder=OUTPUT_FOLDER):\n", "        \"\"\"初始化表格生成器\"\"\"\n", "        self.output_folder = output_folder\n", "        self.data_generator = EnhancedDataGenerators()\n", "        self.renderer = TableRenderer(output_folder)\n", "        \n", "        # 确保主输出文件夹和子文件夹存在\n", "        if not os.path.exists(self.output_folder):\n", "            os.makedirs(self.output_folder)\n", "        if not os.path.exists(OUTPUT_THREE_LINE_FOLDER):\n", "            os.makedirs(OUTPUT_THREE_LINE_FOLDER)\n", "        if not os.path.exists(OUTPUT_TEXT_TABLE_FOLDER):\n", "            os.makedirs(OUTPUT_TEXT_TABLE_FOLDER)\n", "        if not os.path.exists(OUTPUT_NO_LINE_FOLDER):\n", "            os.makedirs(OUTPUT_NO_LINE_FOLDER)\n", "        if not os.path.exists(OUTPUT_ONE_DIM_FOLDER):\n", "            os.makedirs(OUTPUT_ONE_DIM_FOLDER)\n", "        if not os.path.exists(OUTPUT_TWO_DIM_FOLDER):\n", "            os.makedirs(OUTPUT_TWO_DIM_FOLDER)\n", "    \n", "    def generate_table_data(self, schema, num_rows):\n", "        \"\"\"根据表格模式和行数生成表格数据\"\"\"\n", "        data = {}\n", "        theme = schema.get_theme()\n", "        column_defs = schema.get_column_definitions()\n", "        \n", "        # 为每列生成数据\n", "        for col_def in column_defs:\n", "            data[col_def.name] = self.data_generator.generate(col_def, num_rows, theme)\n", "        \n", "        df = pd.DataFrame(data)\n", "        \n", "        # 处理特殊的计算列，如总分和排名\n", "        for col_def in column_defs:\n", "            if col_def.data_type == \"calculated_sum\":\n", "                source_columns = col_def.generator_params.get(\"columns\", [])\n", "                if all(col in df.columns for col in source_columns):\n", "                    df[col_def.name] = df[source_columns].sum(axis=1)\n", "            elif col_def.data_type == \"rank\":\n", "                based_on = col_def.generator_params.get(\"based_on\", \"\")\n", "                if based_on in df.columns:\n", "                    # 根据指定列降序排名\n", "                    df[col_def.name] = df[based_on].rank(method='min', ascending=False).astype(int)\n", "        \n", "        return df\n", "    \n", "    def render_table(self, df, img_index, theme, table_type=\"三线表\"):\n", "        \"\"\"根据表格类型渲染相应的表格\"\"\"\n", "        if table_type == \"文本表\":\n", "            return self.renderer.render_text_table(df, img_index, theme)\n", "        elif table_type == \"无线表\":\n", "            return self.renderer.render_no_line_table(df, img_index, theme)\n", "        elif table_type == \"一维表\":\n", "            return self.renderer.render_one_dim_table(df, img_index, theme)\n", "        elif table_type == \"二维表\":\n", "            return self.renderer.render_two_dim_table(df, img_index, theme)\n", "        else:  # 默认为三线表\n", "            return self.renderer.render_three_line_table(df, img_index, theme)\n", "    \n", "    def generate_tables(self, num_images=NUM_IMAGES, batch_size=100, table_types=None):\n", "        \"\"\"生成指定数量的表格图像\n", "        \n", "        Args:\n", "            num_images: 每种表格类型生成的图片数量\n", "            batch_size: 批处理大小\n", "            table_types: 要生成的表格类型列表，如果为None则生成所有类型\n", "        \"\"\"\n", "        # 如果未指定表格类型，则使用所有支持的类型\n", "        if table_types is None:\n", "            table_types = TABLE_TYPES\n", "        elif isinstance(table_types, str):\n", "            table_types = [table_types]  # 如果是单个字符串，转换为列表\n", "            \n", "        print(f\"开始生成表格图像，类型: {', '.join(table_types)}\")\n", "        \n", "        generated_images = []\n", "        \n", "        for table_type in table_types:\n", "            print(f\"\\n开始生成{table_type}，数量: {num_images}张\")\n", "            \n", "            # 加载配置，获取可用主题\n", "            schema = TableSchema()\n", "            available_themes = schema.available_themes\n", "            \n", "            # 批量处理\n", "            total_batches = (num_images + batch_size - 1) // batch_size  # 向上取整\n", "            print(f\"计划生成 {num_images} 张{table_type}，分 {total_batches} 批处理\")\n", "            \n", "            for batch in range(total_batches):\n", "                start_idx = batch * batch_size\n", "                end_idx = min(start_idx + batch_size, num_images)\n", "                current_batch_size = end_idx - start_idx\n", "                \n", "                print(f\"开始生成第 {batch+1}/{total_batches} 批，共 {current_batch_size} 张{table_type}...\")\n", "                \n", "                # 为这一批次选择主题和创建表格模式\n", "                schemas = []\n", "                for _ in range(current_batch_size):\n", "                    # 使用不同的随机种子来增加多样性\n", "                    random.seed(datetime.datetime.now().microsecond + _)\n", "                    if available_themes:\n", "                        theme = random.choice(available_themes)\n", "                        schemas.append(TableSchema(theme))\n", "                    else:\n", "                        schemas.append(TableSchema())  # 使用默认主题\n", "                \n", "                for i in range(current_batch_size):\n", "                    idx = start_idx + i\n", "                    schema = schemas[i]\n", "                    theme = schema.get_theme()\n", "                    print(f\"生成{table_type} {idx+1}，主题: {theme}\")\n", "                    \n", "                    # 随机选择行数\n", "                    num_rows = random.randint(MIN_ROWS, MAX_ROWS)\n", "                    print(f\"选择行数: {num_rows}\")\n", "                    \n", "                    # 生成数据\n", "                    print(\"生成数据...\")\n", "                    df = self.generate_table_data(schema, num_rows)\n", "                    \n", "                    # 渲染表格图像\n", "                    print(f\"渲染{table_type}图像...\")\n", "                    output_path = self.render_table(df, idx, theme, table_type)\n", "                    print(f\"图像已保存: {output_path}\")\n", "                    \n", "                    generated_images.append(output_path)\n", "                    \n", "                    # 每生成10张图片输出一次进度\n", "                    if (i + 1) % 10 == 0 or (i + 1) == current_batch_size:\n", "                        print(f\"批次 {batch+1}: 已生成 {i+1}/{current_batch_size} 张{table_type}\")\n", "                \n", "                print(f\"完成第 {batch+1}/{total_batches} 批{table_type}生成\")\n", "            \n", "            print(f\"完成{table_type}生成，共 {num_images} 张\")\n", "        \n", "        print(f\"总共生成了 {len(generated_images)} 张图片\")\n", "        return generated_images\n", "    \n", "    def generate_one_table(self, theme=None, num_rows=None, table_type=\"三线表\"):\n", "        \"\"\"生成单个表格，可用于测试\"\"\"\n", "        schema = TableSchema(theme)\n", "        \n", "        if num_rows is None:\n", "            num_rows = random.randint(MIN_ROWS, MAX_ROWS)\n", "        \n", "        df = self.generate_table_data(schema, num_rows)\n", "        output_path = self.render_table(df, 0, schema.get_theme(), table_type)\n", "        \n", "        return output_path, df\n", "\n", "# ====================== 主执行函数 ======================\n", "\n", "def generate_three_line_tables(num_images=3, batch_size=100):\n", "    \"\"\"生成指定数量的三线表图像，支持批量处理\"\"\"\n", "    print(\"Starting generate_three_line_tables\")\n", "    \n", "    # 使用TableGenerator类来处理表格生成\n", "    generator = TableGenerator(OUTPUT_FOLDER)\n", "    generated_images = generator.generate_tables(num_images, batch_size, table_types=[\"三线表\"])\n", "    \n", "    print(f\"总共生成了 {len(generated_images)} 张三线表图片\")\n", "    return generated_images\n", "\n", "def generate_text_tables(num_images=3, batch_size=100):\n", "    \"\"\"生成指定数量的文本表图像，支持批量处理\"\"\"\n", "    print(\"Starting generate_text_tables\")\n", "    \n", "    # 使用TableGenerator类来处理表格生成\n", "    generator = TableGenerator(OUTPUT_FOLDER)\n", "    generated_images = generator.generate_tables(num_images, batch_size, table_types=[\"文本表\"])\n", "    \n", "    print(f\"总共生成了 {len(generated_images)} 张文本表图片\")\n", "    return generated_images\n", "\n", "def generate_no_line_tables(num_images=3, batch_size=100):\n", "    \"\"\"生成指定数量的无线表图像，支持批量处理\"\"\"\n", "    print(\"Starting generate_no_line_tables\")\n", "    \n", "    # 使用TableGenerator类来处理表格生成\n", "    generator = TableGenerator(OUTPUT_FOLDER)\n", "    generated_images = generator.generate_tables(num_images, batch_size, table_types=[\"无线表\"])\n", "    \n", "    print(f\"总共生成了 {len(generated_images)} 张无线表图片\")\n", "    return generated_images\n", "\n", "def generate_one_dim_tables(num_images=3, batch_size=100):\n", "    \"\"\"生成指定数量的一维表图像，支持批量处理\"\"\"\n", "    print(\"Starting generate_one_dim_tables\")\n", "    \n", "    # 使用TableGenerator类来处理表格生成\n", "    generator = TableGenerator(OUTPUT_FOLDER)\n", "    generated_images = generator.generate_tables(num_images, batch_size, table_types=[\"一维表\"])\n", "    \n", "    print(f\"总共生成了 {len(generated_images)} 张一维表图片\")\n", "    return generated_images\n", "\n", "def generate_two_dim_tables(num_images=3, batch_size=100):\n", "    \"\"\"生成指定数量的二维表图像，支持批量处理\"\"\"\n", "    print(\"Starting generate_two_dim_tables\")\n", "    \n", "    # 使用TableGenerator类来处理表格生成\n", "    generator = TableGenerator(OUTPUT_FOLDER)\n", "    generated_images = generator.generate_tables(num_images, batch_size, table_types=[\"二维表\"])\n", "    \n", "    print(f\"总共生成了 {len(generated_images)} 张二维表图片\")\n", "    return generated_images\n", "\n", "def generate_all_tables(num_images=3, batch_size=100, table_types=None):\n", "    \"\"\"生成指定数量的各类表格图像，支持批量处理\n", "    \n", "    Args:\n", "        num_images: 每种表格类型生成的图片数量\n", "        batch_size: 批处理大小\n", "        table_types: 要生成的表格类型列表，如果为None则生成所有类型\n", "    \"\"\"\n", "    print(\"Starting generate_all_tables\")\n", "    \n", "    # 使用TableGenerator类来处理表格生成\n", "    generator = TableGenerator(OUTPUT_FOLDER)\n", "    generated_images = generator.generate_tables(num_images, batch_size, table_types=table_types)\n", "    \n", "    print(f\"总共生成了 {len(generated_images)} 张表格图片\")\n", "    return generated_images\n", "\n", "# ====================== 脚本执行入口 ======================\n", "\n", "if __name__ == \"__main__\":\n", "    print(\"<PERSON><PERSON><PERSON> started\")\n", "    \n", "    # 设置批处理大小，减少内存使用\n", "    BATCH_SIZE = 5  # 减少批次大小便于调试\n", "    \n", "    print(\"Generating tables...\")\n", "    \n", "    try:\n", "        # 生成所有类型的表格\n", "        generated_images = generate_all_tables(NUM_IMAGES, BATCH_SIZE)\n", "        \n", "        print(f\"Successfully generated {len(generated_images)} tables\")\n", "        print(f\"Tables saved in folder: {OUTPUT_FOLDER}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error occurred: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "    \n", "    print(\"<PERSON><PERSON><PERSON> completed\") "]}, {"cell_type": "code", "execution_count": null, "id": "e2d59234-91e2-43a6-998b-25ee3efb96e6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}