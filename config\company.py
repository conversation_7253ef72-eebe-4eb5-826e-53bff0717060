company = {
    "display_name": "公司信息",
    "table_title_template": "公司基本信息表",
    "columns": {
        "公司名称": {
            "generator_type": "faker_company",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "行业": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "互联网/IT",
                    "金融/银行",
                    "医疗/健康",
                    "教育/培训",
                    "制造业",
                    "零售/电商",
                    "农业/食品",
                    "能源/矿产",
                    "建筑/房地产",
                    "传媒/广告",
                    "文化/艺术",
                    "物流/运输",
                    "旅游/餐饮",
                    "咨询/法律",
                    "汽车/交通",
                    "通信/电子",
                    "服装/奢侈品",
                    "软件开发",
                    "云计算/大数据",
                    "人工智能"
                ]
            }
        },
        "成立时间": {
            "generator_type": "date_range",
            "data_category": "date",
            "params": {
                "start_year": 1990,
                "end_year": 2022,
                "format": "%Y-%m-%d"
            }
        },
        "员工数量": {
            "generator_type": "categorical",
            "data_category": "other",
            "params": {
                "values": [
                    "10-50人",
                    "50-100人",
                    "100-500人",
                    "500-1000人",
                    "1000-5000人",
                    "5000-10000人",
                    "10000人以上"
                ]
            }
        },
        "注册资本": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 100,
                "max_value": 10000,
                "format_string": "{:,}万"
            }
        },
        "公司地址": {
            "generator_type": "faker_address",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "经营范围": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "软件开发、技术咨询、系统集成",
                    "互联网信息服务、数据处理",
                    "金融投资、资产管理、风险评估",
                    "医疗器械、生物技术、药品研发",
                    "教育培训、文化传播、学术交流",
                    "生产制造、工业设计、质量检测",
                    "商品零售、网上贸易、进出口业务",
                    "农产品加工、食品生产、饮料制造",
                    "能源开发、电力设备、环保技术",
                    "房地产开发、建筑设计、装饰工程",
                    "广告设计、媒体运营、出版发行",
                    "文艺创作、艺术展览、文化活动"
                ]
            }
        },
        "联系电话": {
            "generator_type": "faker_phone_number",
            "data_category": "other",
            "params": {
                "locale": "zh_CN"
            }
        },
        "电子邮箱": {
            "generator_type": "faker_email",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "法定代表人": {
            "generator_type": "faker_name",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "企业类型": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "有限责任公司",
                    "股份有限公司",
                    "合伙企业",
                    "独资企业",
                    "外资企业",
                    "国有企业",
                    "集体企业",
                    "私营企业"
                ]
            }
        },
        "上市状态": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "未上市",
                    "已上市",
                    "新三板",
                    "科创板",
                    "主板",
                    "创业板",
                    "退市",
                    "筹备上市",
                    "香港上市",
                    "美国上市"
                ]
            }
        },
        "年营业额": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 100,
                "max_value": 100000,
                "format_string": "{:,}万"
            }
        },
        "公司网站": {
            "generator_type": "categorical_with_pattern",
            "data_category": "text",
            "params": {
                "prefixes": [
                    "www.",
                    ""
                ],
                "suffixes": [
                    ".com",
                    ".cn",
                    ".net",
                    ".com.cn",
                    ".org",
                    ".tech",
                    ".co"
                ]
            }
        }
    },
    "text_columns_count": 6,
    "text_columns_names": [
        "公司名称",
        "公司地址",
        "经营范围",
        "电子邮箱",
        "法定代表人",
        "企业类型"
    ],
    "numeric_columns_count": 2,
    "numeric_columns_names": [
        "公司网站"
    ],
    "date_columns_count": 5,
    "date_columns_names": [
        "成立时间",
        "注册资本",
        "上市状态",
        "年营业额"
    ],
    "other_columns_count": 1,
    "other_columns_names": [
        "行业",
        "员工数量",
        "联系电话",

    ],
    "all_columns": [
        "公司名称",
        "公司地址",
        "经营范围",
        "电子邮箱",
        "法定代表人",
        "企业类型",
        "联系电话",
        "公司网站",
        "成立时间",
        "员工数量",
        "注册资本",
        "上市状态",
        "年营业额",
        "行业"
    ]
}

