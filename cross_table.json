{"cross_tables": {"人口统计交叉表": {"display_name": "人口统计交叉表", "table_title_template": "人口统计交叉分析表", "row_hierarchy": [{"name": "年龄段", "values": ["<18", ">=18 && <60", ">=60 && <80", ">=80"], "display_name": "年龄"}, {"name": "性别", "values": ["男", "女"], "display_name": "性别"}], "column_hierarchy": [{"name": "住房情况", "values": ["有房", "无房"], "display_name": "住房情况", "repeat": 2}, {"name": "婚姻状况", "values": ["已婚", "未婚"], "display_name": "婚姻状况"}, {"name": "身体状况", "values": ["强壮", "不强壮"], "display_name": "身体状况"}], "cell_data_rules": {"data_type": "text", "empty_probability": 0.3, "special_chars_probability": 0.2, "special_chars": ["a", "b", "c", "d", "e", "f", "g", "h", "i", "N", "M", "L", "K"], "min_value": 1, "max_value": 100}, "style_settings": {"header_background_color": "#f5f5f5", "header_font_weight": "bold", "data_font_size": 10, "cell_padding": 4, "border_style": "1px solid black", "text_align": "center"}}, "销售业绩交叉表": {"display_name": "销售业绩交叉表", "table_title_template": "销售业绩分析表", "row_hierarchy": [{"name": "产品类别", "values": ["电子产品", "家用电器", "生活用品", "食品饮料"], "display_name": "产品类别"}, {"name": "销售渠道", "values": ["线上", "线下"], "display_name": "销售渠道"}], "column_hierarchy": [{"name": "地区", "values": ["华北", "华东", "华南", "西部"], "display_name": "地区"}, {"name": "客户类型", "values": ["新客户", "老客户"], "display_name": "客户类型"}], "cell_data_rules": {"data_type": "numeric", "empty_probability": 0.1, "numeric_format": "{:.2f}万", "min_value": 10, "max_value": 500}, "style_settings": {"header_background_color": "#e6f3ff", "header_font_weight": "bold", "data_font_size": 10, "cell_padding": 4, "border_style": "1px solid black", "text_align": "center"}}, "学生成绩交叉表": {"display_name": "学生成绩交叉表", "table_title_template": "学生成绩分析表", "row_hierarchy": [{"name": "年级", "values": ["一年级", "二年级", "三年级"], "display_name": "年级"}, {"name": "班级", "values": ["1班", "2班"], "display_name": "班级"}], "column_hierarchy": [{"name": "学科", "values": ["语文", "数学", "英语"], "display_name": "学科"}, {"name": "考试类型", "values": ["期中", "期末"], "display_name": "考试类型"}], "cell_data_rules": {"data_type": "numeric", "empty_probability": 0.05, "numeric_format": "{:.1f}", "min_value": 60, "max_value": 100}, "style_settings": {"header_background_color": "#f0f8ff", "header_font_weight": "bold", "data_font_size": 10, "cell_padding": 4, "border_style": "1px solid black", "text_align": "center"}}}}