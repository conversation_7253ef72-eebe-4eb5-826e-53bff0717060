import imgkit
import os
import json
import random
import itertools
import time
import copy
from datetime import datetime
import re

# 全局配置
OUTPUT_FOLDER = os.path.join("表格", "交叉表")
MAX_ROWS_LEVELS = 2  # 行维度层级数量
MAX_COLS_LEVELS = 3  # 列维度层级数量
MAX_ROWS_VALUES = 5  # 每个行维度最多的取值数量
MAX_COLS_VALUES = 4  # 每个列维度最多的取值数量
MAX_TOTAL_COLUMNS = 15  # 最大列数限制

def load_table_config():
    """加载表格配置文件"""
    try:
        print("尝试加载table_config.json...")
        with open('table_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        print(f"成功加载table_config.json，文件大小: {os.path.getsize('table_config.json')/1024:.1f} KB")
        return config
    except FileNotFoundError:
        print("错误: 找不到table_config.json文件")
        return None
    except json.JSONDecodeError:
        print("错误: table_config.json格式不正确，无法解析JSON")
        return None
    except Exception as e:
        print(f"加载table_config.json出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def load_cross_table_config():
    """加载交叉表配置文件（如果存在）"""
    try:
        with open('cross_table.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config
    except:
        return {"cross_tables": {}}

def extract_categories_from_config(table_config):
    """从table_config中提取分类变量供交叉表使用，增强变量类型支持"""
    print("开始提取分类变量...")
    categories = {}
    
    # 检查table_config是否为空
    if not table_config or "themes" not in table_config:
        print("Warning: table_config为空或不包含themes")
        return categories

    # 遍历所有主题
    for theme_key, theme_data in table_config.get("themes", {}).items():
        theme_display = theme_data.get("display_name", theme_key)
        
        # 遍历该主题下的所有列
        for col_name, col_config in theme_data.get("columns", {}).items():
            # 支持更多类型的分类变量
            if col_config.get("data_category") in ["text", None, "date"]:
                generator_type = col_config.get("generator_type")
                params = col_config.get("params", {})
                
                # 1. 经典分类数据
                if generator_type == "categorical":
                    values = params.get("values", [])
                    if values and len(values) >= 2:  # 至少需要两个值才能用于交叉表
                        # 确保值是可哈希的
                        if isinstance(values, list):
                            # 限制最大值数量
                            if len(values) > MAX_COLS_VALUES:
                                values = values[:MAX_COLS_VALUES]
                                
                        category_key = f"{theme_key}_{col_name}"
                        categories[category_key] = {
                            "name": col_name,
                            "display_name": col_name,
                            "theme": theme_display,
                            "values": values
                        }
                
                # 2. 添加对范围型数据的支持
                elif generator_type in ["numerical_range", "range", "date_range"]:
                    # 构建范围型数据的离散值
                    discrete_values = []
                    
                    if generator_type == "date_range":
                        # 为日期范围创建一些代表性区间
                        year_ranges = [
                            "2019年以前", "2019-2020年", "2021-2022年", "2023年以后"
                        ]
                        discrete_values = year_ranges
                    else:
                        # 为数值范围创建一些代表性区间
                        min_val = params.get("min_value", params.get("min", 0))
                        max_val = params.get("max_value", params.get("max", 100))
                        step = (max_val - min_val) / 4  # 分成4个区间
                        
                        for i in range(4):
                            start = min_val + i * step
                            end = min_val + (i+1) * step
                            if i == 3:  # 最后一个区间包含末尾
                                range_text = f"{start:.0f}及以上"
                            else:
                                range_text = f"{start:.0f}-{end:.0f}"
                            discrete_values.append(range_text)
                    
                    if discrete_values:
                        category_key = f"{theme_key}_{col_name}"
                        categories[category_key] = {
                            "name": col_name,
                            "display_name": col_name,
                            "theme": theme_display,
                            "values": discrete_values
                        }
                
                # 3. 支持选项型和特征型数据
                elif generator_type in ["boolean", "feature", "option"]:
                    discrete_values = []
                    if "values" in params:
                        discrete_values = params.get("values")
                    else:
                        # 默认布尔值选项
                        discrete_values = ["是", "否"] if "values" not in params else params["values"]
                    
                    if discrete_values and len(discrete_values) >= 2:
                        # 限制值的数量
                        if len(discrete_values) > MAX_COLS_VALUES:
                            discrete_values = discrete_values[:MAX_COLS_VALUES]
                            
                        category_key = f"{theme_key}_{col_name}"
                        categories[category_key] = {
                            "name": col_name, 
                            "display_name": col_name,
                            "theme": theme_display,
                            "values": discrete_values
                        }
                        
                # 4. 处理common_data引用
                elif "common_data" in str(params):
                    # 获取公共数据
                    common_data_key = None
                    if isinstance(params.get("values"), dict) and "type" in params["values"] and params["values"]["type"] == "common_data":
                        common_data_key = params["values"].get("key")
                    
                    if not common_data_key and isinstance(params.get("key"), str):
                        common_data_key = params.get("key")
                        
                    if common_data_key:
                        common_values = table_config.get("common_data", {}).get(common_data_key, [])
                        if common_values and len(common_values) >= 2:
                            # 如果公共数据太多，随机选择一部分
                            sample_values = common_values
                            if len(common_values) > MAX_COLS_VALUES:
                                sample_values = random.sample(common_values, MAX_COLS_VALUES)
                                
                            category_key = f"{theme_key}_{col_name}"
                            categories[category_key] = {
                                "name": col_name,
                                "display_name": col_name,
                                "theme": theme_display,
                                "values": sample_values
                            }
                
                # 5. 针对类别+模板型字段
                elif generator_type == "categorical_with_pattern":
                    prefixes = params.get("prefixes", [])
                    suffixes = params.get("suffixes", [])
                    
                    # 处理prefixes可能是common_data引用的情况
                    if isinstance(prefixes, dict) and prefixes.get("type") == "common_data":
                        prefix_key = prefixes.get("key")
                        if prefix_key:
                            prefixes = table_config.get("common_data", {}).get(prefix_key, [])
                    
                    # 从前缀或后缀生成一些组合值
                    combined_values = []
                    
                    if prefixes and suffixes:
                        # 从前缀和后缀中分别随机选取一些值
                        prefix_samples = random.sample(prefixes, min(3, len(prefixes)))
                        suffix_samples = random.sample(suffixes, min(3, len(suffixes)))
                        
                        # 生成一些组合
                        for prefix in prefix_samples:
                            for suffix in suffix_samples:
                                combined_values.append(f"{prefix}{suffix}")
                    
                    if combined_values and len(combined_values) >= 2:
                        # 限制组合值的数量
                        if len(combined_values) > MAX_COLS_VALUES:
                            combined_values = combined_values[:MAX_COLS_VALUES]
                            
                        category_key = f"{theme_key}_{col_name}"
                        categories[category_key] = {
                            "name": col_name,
                            "display_name": col_name,
                            "theme": theme_display,
                            "values": combined_values
                        }
    
    return categories

def extract_metrics_from_config(table_config):
    """从table_config中提取数值变量供交叉表单元格使用"""
    metrics = {}
    
    # 遍历所有主题
    for theme_key, theme_data in table_config.get("themes", {}).items():
        theme_display = theme_data.get("display_name", theme_key)
        
        # 遍历该主题下的所有列
        for col_name, col_config in theme_data.get("columns", {}).items():
            # 只关注数值型数据
            if col_config.get("data_category") == "numeric":
                params = col_config.get("params", {})
                
                # 提取数值格式和范围
                min_value = params.get("min", params.get("min_value", 0))
                max_value = params.get("max", params.get("max_value", 100))
                
                if "format_string" in params:
                    format_string = params.get("format_string")
                else:
                    decimals = params.get("decimals", 0)
                    format_string = "{:." + str(decimals) + "f}" if decimals > 0 else "{:d}"
                
                metric_key = f"{theme_key}_{col_name}"
                metrics[metric_key] = {
                    "name": col_name,
                    "display_name": col_name,
                    "theme": theme_display,
                    "min_value": min_value,
                    "max_value": max_value,
                    "format_string": format_string
                }
    
    return metrics

def check_and_adjust_columns(column_hierarchy):
    """检查并调整列层次结构，确保总列数不超过最大限制"""
    # 计算当前配置下的总列数
    total_cols = 1
    for level in column_hierarchy:
        level_values = level.get("values", [])
        # 考虑repeat属性
        if "repeat" in level:
            total_cols *= len(level_values) * level["repeat"]
        else:
            total_cols *= len(level_values) if level_values else 1
    
    # 如果总列数超过限制，需要调整
    if total_cols > MAX_TOTAL_COLUMNS:
        print(f"列数({total_cols})超过限制({MAX_TOTAL_COLUMNS})，进行调整")
        
        # 调整策略：从最后一级开始，逐级减少值的数量
        for i in range(len(column_hierarchy) - 1, -1, -1):
            level = column_hierarchy[i]
            
            # 删除repeat属性（如果存在）
            if "repeat" in level and level["repeat"] > 1:
                level.pop("repeat")
                
                # 重新计算当前总列数
                total_cols = 1
                for l in column_hierarchy:
                    repeat = l.get("repeat", 1)
                    total_cols *= len(l.get("values", [])) * repeat if l.get("values") else 1
                
                if total_cols <= MAX_TOTAL_COLUMNS:
                    break
            
            # 减少值的数量
            values = level.get("values", [])
            if len(values) > 2:
                # 计算需要减少到的值数量
                current = len(values)
                while current > 2 and total_cols > MAX_TOTAL_COLUMNS:
                    current -= 1
                    
                    # 预估如果减少到current个值，总列数会是多少
                    reduction_factor = len(values) / current
                    new_total = total_cols / reduction_factor
                    
                    if new_total <= MAX_TOTAL_COLUMNS:
                        # 保留前current个值
                        level["values"] = values[:current]
                        
                        # 重新计算总列数
                        total_cols = 1
                        for l in column_hierarchy:
                            repeat = l.get("repeat", 1)
                            total_cols *= len(l.get("values", [])) * repeat if l.get("values") else 1
                        
                        break
    
    return column_hierarchy, total_cols

def generate_dynamic_cross_table_config(categories, metrics):
    """动态生成交叉表配置，提高随机性和多样性"""
    cross_tables = {}
    
    # 将类别变量分组
    theme_categories = {}
    for key, data in categories.items():
        theme = data["theme"]
        if theme not in theme_categories:
            theme_categories[theme] = []
        theme_categories[theme].append(data)
    
    # 按主题数量生成交叉表配置
    themes_count = len(theme_categories)
    target_tables_per_theme = max(3, 20 // themes_count)  # 每个主题生成多个表
    
    # 为每个主题生成交叉表配置
    for theme, theme_cats in theme_categories.items():
        # 如果类别变量不足，尝试配对其他主题的变量
        if len(theme_cats) < 3:  # 至少需要3个类别变量
            # 尝试从其他主题借用变量
            other_categories = []
            for other_theme, other_cats in theme_categories.items():
                if other_theme != theme:
                    other_categories.extend(other_cats)
            
            # 随机选择一些其他主题的变量
            if other_categories:
                additional_cats = random.sample(other_categories, min(3, len(other_categories)))
                theme_cats.extend(additional_cats)
        
        if len(theme_cats) < 3:
            continue  # 仍然不足，跳过
        
        # 尝试为每个主题生成多个不同的交叉表
        for _ in range(target_tables_per_theme):
            # 随机变化行列数量
            random.shuffle(theme_cats)
            
            # 随机确定行列维度数量
            rows_count = random.randint(1, min(MAX_ROWS_LEVELS, len(theme_cats)-1))
            cols_count = min(MAX_COLS_LEVELS, len(theme_cats)-rows_count)
            
            # 确保至少有1行1列
            if rows_count < 1 or cols_count < 1:
                continue
                
            # 随机选择行和列维度
            row_categories = theme_cats[:rows_count]
            col_categories = theme_cats[rows_count:rows_count+cols_count]
            
            # 随机选择度量变量（单元格数据）
            theme_metrics = [m for m in metrics.values() if m["theme"] == theme]
            if not theme_metrics:
                # 如果当前主题没有度量变量，从所有度量变量中随机选择
                theme_metrics = random.sample(list(metrics.values()), min(3, len(metrics)))
            
            metric = random.choice(theme_metrics) if theme_metrics else None
            
            # 生成交叉表配置
            table_key = f"{theme}_交叉表_{random.randint(1000, 9999)}"
            
            # 特殊处理人口统计相关主题，确保正确显示
            display_name = f"{theme}交叉表"
            title_template = f"{theme}交叉分析表"
            
            # 对人员信息主题特殊处理
            if "人员" in theme or "personnel" in theme.lower():
                display_name = "人口统计交叉表"
                title_template = "人口统计交叉分析表"
            
            # 行层次结构 - 增加随机性
            row_hierarchy = []
            for cat in row_categories:
                values = cat["values"].copy()  # 使用副本避免修改原始数据
                # 随机决定是否对值进行排序或随机抽样
                if random.random() < 0.3:  # 30%概率排序
                    if all(isinstance(v, str) for v in values):
                        values = sorted(values)
                    
                # 随机决定是否只保留部分值
                if len(values) > 3 and random.random() < 0.5:  # 50%概率抽样
                    sample_size = random.randint(2, min(len(values), MAX_ROWS_VALUES))
                    values = random.sample(values, sample_size)
                
                row_hierarchy.append({
                    "name": cat["name"],
                    "values": values,
                    "display_name": cat["display_name"]
                })
            
            # 列层次结构 - 增加随机性
            column_hierarchy = []
            for cat in col_categories:
                values = cat["values"].copy()  # 使用副本避免修改原始数据
                
                # 随机决定是否对值进行排序或随机抽样
                if random.random() < 0.3:  # 30%概率排序
                    if all(isinstance(v, str) for v in values):
                        values = sorted(values)
                
                # 随机决定是否只保留部分值或添加repeat属性
                if len(values) > 2 and random.random() < 0.5:  # 50%概率抽样或重复
                    if random.random() < 0.7:  # 70%概率抽样
                        sample_size = random.randint(2, min(len(values), MAX_COLS_VALUES))
                        values = random.sample(values, sample_size)
                    else:  # 30%概率添加repeat属性
                        repeat = random.randint(2, 3)
                        column_hierarchy.append({
                            "name": cat["name"],
                            "values": values[:2],  # 限制为前2个值
                            "display_name": cat["display_name"],
                            "repeat": repeat
                        })
                        continue
                
                column_hierarchy.append({
                    "name": cat["name"],
                    "values": values,
                    "display_name": cat["display_name"]
                })
            
            # 检查并调整列数量，确保不超过最大限制
            column_hierarchy, total_cols = check_and_adjust_columns(column_hierarchy)
            
            # 随机调整单元格数据规则
            empty_prob = random.uniform(0.1, 0.4)
            special_chars_prob = random.uniform(0.05, 0.25)
            
            # 根据度量变量随机选择数据类型
            if metric and random.random() < 0.7:  # 70%概率使用数值型
                data_type = "numeric"
                min_val = metric["min_value"]
                max_val = metric["max_value"]
                format_string = metric["format_string"]
            else:
                data_type = "text"
                min_val = 1
                max_val = 100
                format_string = "{:d}"
            
            # Determine available supplementary names
            used_display_names = set()
            for cat_item in row_categories:
                used_display_names.add(cat_item["display_name"])
            for cat_item in col_categories:
                used_display_names.add(cat_item["display_name"])

            # theme_categories is the input list of all category dicts for this theme
            all_possible_theme_cat_display_names = [cat["display_name"] for cat in theme_cats]
            available_sup_names = [name for name in all_possible_theme_cat_display_names if name not in used_display_names]
            random.shuffle(available_sup_names)

            cross_tables[table_key] = {
                "display_name": f"{theme}交叉表",
                "table_title_template": title_template,
                "row_hierarchy": row_hierarchy,
                "column_hierarchy": column_hierarchy,
                "total_columns": total_cols,  # 记录总列数
                "cell_data_rules": {
                    "data_type": data_type,
                    "empty_probability": empty_prob,
                    "special_chars_probability": special_chars_prob,
                    "special_chars": ["a", "b", "c", "d", "e", "f", "g", "h", "i", "N", "M", "L", "K"],
                    "min_value": min_val,
                    "max_value": max_val,
                    "numeric_format": format_string
                },
                "style_settings": {
                    "header_background_color": random.choice([
                        "#f5f5f5", "#e6f3ff", "#f0f8ff", "#e9f7ef", "#fdebd0", 
                        "#ebdef0", "#d6eaf8", "#fae5d3", "#f2f3f4", "#eaeded"
                    ]),
                    "header_font_weight": "bold",
                    "data_font_size": 10,
                    "cell_padding": 4,
                    "border_style": "1px solid black",
                    "text_align": "center"
                },
                "available_sup_names": available_sup_names # Store the available names
            }
    
    return {"cross_tables": cross_tables}

def create_variations(base_config):
    """基于基础配置创建变种交叉表配置，增加更多变化"""
    cross_tables = base_config["cross_tables"]
    variations = {"cross_tables": {}}
    
    for key, table_config in cross_tables.items():
        # 创建变种数量随机化
        variation_count = random.randint(3, 8)
        
        for i in range(variation_count):
            new_key = f"{key}_var{i}"
            new_config = copy.deepcopy(table_config)
            
            # 1. 随机调整样式
            new_config["style_settings"]["header_background_color"] = random.choice([
                "#f5f5f5", "#e6f3ff", "#f0f8ff", "#e9f7ef", "#fdebd0", 
                "#ebdef0", "#d6eaf8", "#fae5d3", "#f2f3f4", "#eaeded"
            ])
            
            # 随机调整字体大小
            new_config["style_settings"]["data_font_size"] = random.choice([9, 10, 11])
            
            # 随机调整边框样式
            border_width = random.choice(["1px", "2px"])
            border_style = random.choice(["solid", "dotted", "dashed"])
            border_color = random.choice(["black", "#333333", "#666666"])
            new_config["style_settings"]["border_style"] = f"{border_width} {border_style} {border_color}"
            
            # 2. 随机调整数据规则
            new_config["cell_data_rules"]["empty_probability"] = random.uniform(0.1, 0.4)
            new_config["cell_data_rules"]["special_chars_probability"] = random.uniform(0.05, 0.25)
            
            # 随机调整数值范围（如果是数值类型）
            if new_config["cell_data_rules"]["data_type"] == "numeric":
                base_min = new_config["cell_data_rules"]["min_value"]
                base_max = new_config["cell_data_rules"]["max_value"]
                
                # 调整范围但保持原有量级
                range_size = base_max - base_min
                new_min = max(0, base_min + random.uniform(-0.2, 0.2) * range_size)
                new_max = base_max + random.uniform(-0.2, 0.2) * range_size
                
                if new_max > new_min:
                    new_config["cell_data_rules"]["min_value"] = new_min
                    new_config["cell_data_rules"]["max_value"] = new_max
            
            # 3. 随机调整行列值（有一定概率）
            if random.random() < 0.3:  # 30%概率调整行值
                for level in new_config["row_hierarchy"]:
                    values = level["values"]
                    if len(values) > 2:
                        # 随机排序或筛选值
                        if random.random() < 0.5:
                            random.shuffle(values)
                        else:
                            sample_size = random.randint(2, len(values))
                            level["values"] = random.sample(values, sample_size)
            
            if random.random() < 0.3:  # 30%概率调整列值
                column_changed = False
                for level in new_config["column_hierarchy"]:
                    if "repeat" in level:  # 如果有重复设置，有一定概率修改重复次数
                        if random.random() < 0.5:
                            level["repeat"] = random.randint(2, 3)
                            column_changed = True
                    else:
                        values = level["values"]
                        if len(values) > 2:
                            # 随机排序或筛选值
                            if random.random() < 0.5:
                                random.shuffle(values)
                            else:
                                sample_size = random.randint(2, len(values))
                                level["values"] = random.sample(values, sample_size)
                                column_changed = True
                
                # 如果列结构发生变化，重新检查并调整列数
                if column_changed:
                    new_config["column_hierarchy"], total_cols = check_and_adjust_columns(new_config["column_hierarchy"])
                    new_config["total_columns"] = total_cols
            
            variations["cross_tables"][new_key] = new_config
    
    return variations

def identify_table_theme(title_text):
    """识别表格主题类型"""
    title_lower = title_text.lower()
    
    # 人口统计相关
    if any(keyword in title_text for keyword in ["人口", "人员", "居民", "公民", "人群", "住户", "家庭", "住房", "婚姻"]):
        return "population"
    
    # 交通工具相关
    if any(keyword in title_text for keyword in ["交通", "运输", "车辆", "汽车", "机动车", "公路", "航空", "铁路", "船舶"]):
        return "transport"
    
    # 设备相关
    if any(keyword in title_text for keyword in ["设备", "机械", "仪器", "器械", "工具", "机器", "装置", "硬件"]):
        return "equipment"
    
    # 产品相关
    if any(keyword in title_text for keyword in ["产品", "商品", "货物", "制品", "物品", "消费品", "生产", "制造"]):
        return "product"
    
    # 教育相关
    if any(keyword in title_text for keyword in ["教育", "学校", "学生", "课程", "教学", "学习", "成绩", "升学", "学历"]):
        return "education"
    
    # 金融相关
    if any(keyword in title_text for keyword in ["金融", "财务", "财政", "经济", "银行", "投资", "理财", "证券", "基金", "股票", "债券", "行业", "比例", "增长率"]):
        return "finance"
    
    # 医疗相关
    if any(keyword in title_text for keyword in ["医疗", "健康", "医院", "疾病", "患者", "医师", "药品", "治疗", "诊断", "门诊", "住院"]):
        return "medical"
    
    # 人力资源相关
    if any(keyword in title_text for keyword in ["人力", "招聘", "员工", "职员", "工作", "岗位", "职位", "薪资", "福利", "绩效", "考核"]):
        return "hr"
    
    # 销售相关
    if any(keyword in title_text for keyword in ["销售", "市场", "营销", "客户", "消费", "购买", "零售", "批发", "促销", "渠道"]):
        return "sales"
    
    # 环境相关
    if any(keyword in title_text for keyword in ["环境", "生态", "污染", "气候", "能源", "资源", "保护", "绿色", "可持续"]):
        return "environment"
    
    # 技术相关
    if any(keyword in title_text for keyword in ["技术", "科技", "创新", "研发", "工程", "信息", "电子", "智能", "数字", "网络", "通信"]):
        return "technology"
        
    # 新增: 房地产相关
    if any(keyword in title_text for keyword in ["房地产", "物业", "房屋", "地产", "楼市", "楼盘", "住宅", "商铺", "地段", "物业"]):
        return "real_estate"
        
    # 新增: 旅游相关
    if any(keyword in title_text for keyword in ["旅游", "景点", "旅行", "出游", "度假", "观光", "酒店", "民宿", "景区", "游客"]):
        return "tourism"
        
    # 新增: 农业相关
    if any(keyword in title_text for keyword in ["农业", "农产品", "种植", "养殖", "农田", "农民", "粮食", "耕地", "作物", "畜牧"]):
        return "agriculture"
        
    # 新增: 体育相关
    if any(keyword in title_text for keyword in ["体育", "运动", "赛事", "比赛", "训练", "健身", "球队", "球员", "成绩", "竞技"]):
        return "sports"
        
    # 新增: 物流相关
    if any(keyword in title_text for keyword in ["物流", "快递", "仓储", "配送", "运输", "供应链", "货运", "邮政", "包裹", "配货"]):
        return "logistics"
        
    # 新增: 文化娱乐相关
    if any(keyword in title_text for keyword in ["文化", "娱乐", "艺术", "影视", "音乐", "戏剧", "展览", "演出", "传媒", "创作"]):
        return "culture"
        
    # 新增: 电子商务相关
    if any(keyword in title_text for keyword in ["电商", "网购", "线上销售", "平台", "直播", "网店", "网络营销", "社交电商", "跨境电商"]):
        return "e_commerce"
        
    # 新增: 安全相关
    if any(keyword in title_text for keyword in ["安全", "安防", "监控", "消防", "警务", "预警", "风险", "应急", "保安", "保卫"]):
        return "security"
        
    # 新增: 能源相关
    if any(keyword in title_text for keyword in ["能源", "电力", "石油", "天然气", "可再生", "风能", "太阳能", "核能", "发电", "供电"]):
        return "energy"
        
    # 新增: 政府行政相关
    if any(keyword in title_text for keyword in ["政府", "行政", "管理", "公共", "服务", "机关", "事业单位", "政策", "法规", "规划"]):
        return "government"
    
    # 默认为标准交叉表
    return "standard"

def create_random_cross_table_image(cross_table_config, output_path):
    """根据配置生成交叉表图片"""
    # 提取配置信息
    title_text = cross_table_config.get("table_title_template", "交叉分析表")
    
    # 提取行层次结构
    row_levels = cross_table_config.get("row_hierarchy", [])
    row_level_names = [level.get("display_name", "维度") for level in row_levels]
    row_level_values = [level.get("values", []) for level in row_levels]
    
    # 提取列层次结构
    col_levels = cross_table_config.get("column_hierarchy", [])
    col_level_names = [level.get("display_name", "维度") for level in col_levels]
    col_level_values = [level.get("values", []) for level in col_levels]
    
    # 提取单元格数据规则
    cell_rules = cross_table_config.get("cell_data_rules", {})
    data_type = cell_rules.get("data_type", "text")
    empty_probability = cell_rules.get("empty_probability", 0.3)
    min_value = cell_rules.get("min_value", 1)
    max_value = cell_rules.get("max_value", 100)
    
    # 处理特殊字符
    special_chars = cell_rules.get("special_chars", [])
    special_chars_probability = cell_rules.get("special_chars_probability", 0.2)
    
    # 提取样式设置
    style_settings = cross_table_config.get("style_settings", {})
    header_bg_color = style_settings.get("header_background_color", "#f5f5f5")
    header_font_weight = style_settings.get("header_font_weight", "bold")
    data_font_size = style_settings.get("data_font_size", 10)
    cell_padding = style_settings.get("cell_padding", 4)
    border_style = "1px solid black" # 强制使用实线边框
    text_align = style_settings.get("text_align", "center")
    
    # 扩展主题识别
    table_theme = identify_table_theme(title_text)
    
    # 定义单元格样式
    cell_style = f"border: {border_style}; text-align: {text_align}; vertical-align: middle; padding: {cell_padding}px; word-wrap: break-word; overflow: visible;"
    
    # 计算总列数并动态调整图片宽度
    if table_theme == "population":
        # 人口统计表使用固定结构
        total_cols = 14  # 固定14列确保完整结构
    elif table_theme in ["transport", "equipment", "product", "education", "finance", "medical", "environment", 
                         "hr", "sales", "technology", "real_estate", "tourism", "agriculture", "sports", 
                         "logistics", "culture", "e_commerce", "security", "energy", "government"]:
        # 专业领域表格使用固定数量的类别和时间段
        total_cols = 9  # 通常是3个类别 * 3个时间段
    else:
        # 常规表格计算列数
        total_cols = 1
        for level in col_level_values:
            total_cols *= len(level) if level else 1
        
    # 检查列数并确保不超过限制
    if total_cols > MAX_TOTAL_COLUMNS and table_theme == "standard":
        print(f"警告: 表格列数({total_cols})超过限制({MAX_TOTAL_COLUMNS})，将调整为完整层次结构")
        # 限制列数，但保持完整的层次结构
        total_cols = min(total_cols, MAX_TOTAL_COLUMNS)
    
    # 增加最小列宽，确保内容显示完整
    min_col_width = 120  # 增加列最小宽度，从100改为120
    base_width = 1200    # 增加基础宽度，从1000改为1200
    dynamic_width = max(base_width, (total_cols + 2) * min_col_width)
    
    # 更新imgkit选项
    options = {
        'format': 'jpg',
        'encoding': "UTF-8",
        'quiet': '',
        'enable-local-file-access': '',
        'width': dynamic_width,  # 动态宽度
        'quality': 95
    }
    
    # HTML模板添加更好的样式控制
    html = (
        '<!DOCTYPE html>\n'
        '<html>\n'
        '<head>\n'
        '<meta charset="UTF-8">\n'
        '<title>交叉表</title>\n'
        f'<style>\n'
        f'body {{ margin: 0; padding: 10px; }}\n'
        f'table {{ width: 100%; max-width: {dynamic_width-40}px; table-layout: fixed; border-collapse: collapse; }}\n'
        f'th, td {{ overflow: visible; word-wrap: break-word; font-size: 9pt; padding: {cell_padding}px; min-width: 100px; max-width: 150px; }}\n'
        f'th {{ font-weight: bold; }}\n'
        f'</style>\n'
        '</head>\n'
        '<body>\n'
        f'<table style="border-collapse: collapse; border: {border_style}; '
        f'font-family: SimHei, Arial, sans-serif;">\n'
    )
    
    # 根据主题选择不同的表格生成方式
    
    # 对于HR主题，随机使用时间范围表格
    if table_theme in ["hr", "personnel"] and random.random() < 0.3:
        # 使用时间范围表格
        html += generate_time_range_table_html(title_text, cell_style)
    else:
        # 使用通用表格生成逻辑，处理所有主题类型
        # 表头
        html += '<thead>'
        
        # 移除标题行 - 根据需求删除
        
        # 第一层级 (根据主题生成适当的表头)
        html += '<tr>'
        html += f'<th rowspan="2" style="{cell_style}">{row_level_names[0] if row_level_names else "维度1"}</th>'
        
        if len(row_level_names) > 1:
            html += f'<th rowspan="2" style="{cell_style}">{row_level_names[1] if len(row_level_names) > 1 else "维度2"}</th>'
        else:
            html += f'<th rowspan="2" style="{cell_style}">类别</th>'
        
        # 为不同主题生成适当的列标题
        if table_theme in ["population", "education", "personnel", "hr"]:
            # 人口/教育/人事相关主题使用人口统计风格的列标题
            headers = ["有房", "无房"] if table_theme == "population" else ["优秀", "标准"]
            cols_per_group = 4 # 每个主分组（如有房）跨越的最终列数
            
            # 此处HTML的构建是基于表格的第一行已经包含了行维度标题（如职业规划、类别，它们有rowspan="2"）
            # 所以接下来的<th>是列标题的开始
            
            num_main_groups = len(headers)
            specific_cols_covered_by_main_groups = num_main_groups * cols_per_group

            for header_text in headers: # 例如: "有房", "无房"
                html += f'<th colspan="{cols_per_group}" style="{cell_style}">{header_text}</th>'
            
            remaining_columns_at_top_level = total_cols - specific_cols_covered_by_main_groups
            if remaining_columns_at_top_level > 0:
                available_sup_names_from_config = cross_table_config.get("available_sup_names", [])
                sup_names_pool = list(available_sup_names_from_config) # Create a mutable copy
                random.shuffle(sup_names_pool) # Shuffle for variety
                for i in range(remaining_columns_at_top_level):
                    sup_name = f"补充列{i+1}" # Default fallback name
                    if sup_names_pool:
                        sup_name = sup_names_pool.pop()
                    html += f'<th rowspan="2" style="{cell_style}">{sup_name}</th>'

            html += '</tr><tr>' # 结束第一列表头行，开始第二列表头行
                                # 行维度标题 (rowspan="2") 和 "补充列" (rowspan="2") 会正确跨过此行
            
            # 第二列表头行 - 仅用于主分组的子标题 (例如: "已婚", "未婚")
            for _ in range(num_main_groups): # 遍历 "有房", "无房"
                subheaders_text = ["已婚", "未婚"] if table_theme == "population" else ["男性", "女性"]
                for sub_text in subheaders_text:
                    html += f'<th colspan="2" style="{cell_style}">{sub_text}</th>' # 每个子标题跨越2个最终列
        elif table_theme in ["transport", "product", "finance", "medical", "technology", "real_estate", 
                           "tourism", "agriculture", "sports", "environment", "sales", "logistics", 
                           "culture", "e_commerce", "security", "energy", "government", "equipment"]:
            # 其他专业领域使用三列三行结构
            # 生成三列标题
            time_periods = ["2022年", "2023年", "2024年"]
            for period in time_periods:
                html += f'<th colspan="3" style="{cell_style}">{period}</th>'
            
            html += '</tr><tr>'
            
            # 生成九个子标题
            metrics = ["销量", "金额", "占比"] if table_theme in ["sales", "finance", "product", "e_commerce"] else ["数量", "比例", "增长率"]
            for _ in range(3):  # 三个时间段
                for metric in metrics:
                    html += f'<th style="{cell_style}">{metric}</th>'
        else:
            # 其他通用主题使用简单的列标题
            if col_levels and col_level_values:
                # 使用已有的列层次
                total_cols_first_level = 0
                
                # 计算第一层级总列数，确保所有列都有标题
                if len(col_levels) > 1:
                    # 多层级情况
                    first_level_values = col_levels[0].get("values", [])
                    second_level_values = col_levels[1].get("values", [])
                    cols_per_first_value = len(second_level_values) if second_level_values else 1
                    
                    # 为第一层级的每个值生成标题
                    for value in first_level_values:
                        cols_this_value = cols_per_first_value
                        html += f'<th colspan="{cols_this_value}" style="{cell_style}">{value}</th>'
                        total_cols_first_level += cols_this_value
                else:
                    # 单层级情况，每个值占两列（数值和占比）
                    first_level_values = col_levels[0].get("values", [])
                    for value in first_level_values:
                        html += f'<th colspan="2" style="{cell_style}">{value}</th>'
                        total_cols_first_level += 2
                
                # 确保所有列都有标题
                if total_cols_first_level < total_cols:
                    html += f'<th colspan="{total_cols - total_cols_first_level}" style="{cell_style}">其他</th>'
                
                html += '</tr><tr>'
                
                # 第二层列头
                if len(col_levels) > 1:
                    # 使用多层级表头
                    for value1 in col_levels[0].get("values", []):
                        for value2 in col_levels[1].get("values", []):
                            # 确保value2不为空，如果为空则使用一个默认值
                            display_value = value2 if value2 and str(value2).strip() else "子分类"
                            html += f'<th style="{cell_style}">{display_value}</th>'
                else:
                    # 只有一层的情况，确保生成两个非空的列标题
                    for value in col_levels[0].get("values", []):
                        # 确保value不为空
                        display_value = value if value and str(value).strip() else "分类"
                        html += f'<th style="{cell_style}">数值</th>'
                        html += f'<th style="{cell_style}">占比</th>'
                
                # 确保第二层所有列都有标题
                generated_cols = 0
                if len(col_levels) > 1:
                    generated_cols = len(col_levels[0].get("values", [])) * len(col_levels[1].get("values", []))
                else:
                    generated_cols = len(col_levels[0].get("values", [])) * 2
                
                if generated_cols < total_cols:
                    for i in range(total_cols - generated_cols):
                        html += f'<th style="{cell_style}">值{i+1}</th>'
            else:
                # 没有列层次时创建默认列标题
                default_columns = ["类别1", "类别2", "类别3", "类别4", "类别5", "类别6", "类别7", "类别8"]
                # 确保至少有8个列标题，填充到实际需要的列数
                needed_cols = max(8, total_cols)
                
                # 第一行：分类标题
                html += f'<th colspan="{needed_cols}" style="{cell_style}">数据分类</th>'
                html += '</tr><tr>'
                
                # 第二行：具体列标题
                for i in range(needed_cols):
                    col_name = default_columns[i % len(default_columns)]
                    if i >= len(default_columns):
                        col_name += f"{i//len(default_columns)+1}"
                    html += f'<th style="{cell_style}">{col_name}</th>'
        
        html += '</tr></thead>'
        
        # 数据行
        html += '<tbody>'
        
        # 如果行层次值为空，生成一些默认值
        if not row_level_values or not row_level_values[0]:
            # 创建默认行数据
            default_rows = [
                ["分组A", "类型1"], ["分组A", "类型2"], ["分组A", "类型3"],
                ["分组B", "类型1"], ["分组B", "类型2"], ["分组B", "类型3"],
                ["分组C", "类型1"], ["分组C", "类型2"], ["分组C", "类型3"]
            ]
            
            current_first_level = None
            for row in default_rows:
                html += '<tr>'
                
                if row[0] != current_first_level:
                    # 计算当前第一级有多少行
                    same_first_level_count = sum(1 for r in default_rows if r[0] == row[0])
                    html += f'<td rowspan="{same_first_level_count}" style="{cell_style}">{row[0]}</td>'
                    current_first_level = row[0]
                
                html += f'<td style="{cell_style}">{row[1]}</td>'
                
                # 生成数据单元格
                cols_count = total_cols if total_cols else 8
                for _ in range(cols_count):
                    cell_value = generate_cell_value(data_type, min_value, max_value, 
                                                 empty_probability, special_chars_probability, 
                                                 special_chars, cell_rules)
                    html += f'<td style="{cell_style}">{cell_value}</td>'
                
                html += '</tr>'
        else:
            # 使用已有的行数据
            # 处理每一级的行层次结构
            if len(row_level_values) == 1:
                # 只有一级行层次
                for value in row_level_values[0]:
                    html += '<tr>'
                    html += f'<td style="{cell_style}">{value}</td>'
                    html += f'<td style="{cell_style}">-</td>'  # 第二列为空
                    
                    # 生成数据单元格
                    cols_count = total_cols if total_cols else 8
                    for _ in range(cols_count):
                        cell_value = generate_cell_value(data_type, min_value, max_value, 
                                                     empty_probability, special_chars_probability, 
                                                     special_chars, cell_rules)
                        html += f'<td style="{cell_style}">{cell_value}</td>'
                    
                    html += '</tr>'
            else:
                # 有两级或更多行层次
                current_first_level = None
                
                # 处理所有可能的行组合
                all_combinations = []
                for value1 in row_level_values[0]:
                    for value2 in row_level_values[1]:
                        all_combinations.append([value1, value2])
                
                for row in all_combinations:
                    html += '<tr>'
                    
                    if row[0] != current_first_level:
                        # 计算当前第一级有多少行
                        same_first_level_count = sum(1 for r in all_combinations if r[0] == row[0])
                        html += f'<td rowspan="{same_first_level_count}" style="{cell_style}">{row[0]}</td>'
                        current_first_level = row[0]
                    
                    html += f'<td style="{cell_style}">{row[1]}</td>'
                    
                    # 生成数据单元格
                    cols_count = total_cols if total_cols else 8
                    for _ in range(cols_count):
                        cell_value = generate_cell_value(data_type, min_value, max_value, 
                                                     empty_probability, special_chars_probability, 
                                                     special_chars, cell_rules)
                        html += f'<td style="{cell_style}">{cell_value}</td>'
                    
                    html += '</tr>'
        
        html += '</tbody>'
    
    html += '</table>\n</body>\n</html>'
    
    # 检查HTML是否存在没有标题的列
    # 检查空的th标签
    empty_th_pattern = re.compile(r'<th[^>]*>\s*</th>')
    if empty_th_pattern.search(html):
        print("检测到空的表头单元格，跳过生成")
        return False
    
    # 检查th标签内容是否为空或只有空格
    th_content_pattern = re.compile(r'<th[^>]*>([^<]*)</th>')
    for match in th_content_pattern.finditer(html):
        content = match.group(1).strip()
        if not content:
            print("检测到没有内容的表头单元格，跳过生成")
            return False
    
    # 检查是否存在colspan但没有内容的th
    colspan_empty_pattern = re.compile(r'<th[^>]*colspan="[^"]*"[^>]*>\s*</th>')
    if colspan_empty_pattern.search(html):
        print("检测到空的合并表头单元格，跳过生成")
        return False
        
    # 增强检查：检查只有单个字母的表头
    single_letter_pattern = re.compile(r'<th[^>]*>([a-zA-Z]|\s*[a-zA-Z]\s*)</th>')
    if single_letter_pattern.search(html):
        print("检测到只有单个字母的表头单元格，跳过生成")
        return False
        
    # 增强检查：检查特殊异常值（如数字、N、n等）作为表头
    special_values_pattern = re.compile(r'<th[^>]*>(N|n|M|m|K|k|\d+)</th>')
    if special_values_pattern.search(html):
        print("检测到特殊值作为表头单元格，跳过生成")
        return False
        
    # 增强检查：验证实际列数与理论列数一致
    # 计算理论上的列数应当与实际生成的列头数量匹配
    if table_theme not in ["population", "education", "transport"]:
        tr_pattern = re.compile(r'<tr>(.*?)</tr>', re.DOTALL)
        last_header_row = None
        for tr_match in tr_pattern.finditer(html):
            if '</thead>' in tr_match.group(1):
                last_header_row = tr_match.group(1)
                break
                
        if last_header_row:
            th_count = last_header_row.count('<th')
            # 检查列头数量是否异常
            if th_count == 0 or th_count < total_cols * 0.9:  # 允许10%的误差
                print(f"检测到列头数量异常: {th_count}列头, 应当有约{total_cols}列")
                return False
    
    # 生成图片
    try:
        imgkit.from_string(html, output_path, options=options)
        return True
    except Exception as e:
        print(f"生成图片时出错: {e}")
        return False

def generate_population_table_html(title_text, row_levels, row_level_values, cell_style, 
                                 header_bg_color, data_type, min_value, max_value, 
                                 empty_probability, special_chars_probability, 
                                 special_chars, cell_rules):
    """生成人口统计交叉表的HTML"""
    html = '<thead><tr>'
    html += f'<th rowspan="3" colspan="2" style="{cell_style}">{title_text}</th>'
    
    # 随机选择分类名称，增加多样性
    housing_options = [
        ["有房", "无房"],
        ["自有住房", "租房"],
        ["有住所", "无住所"],
        ["有产权", "无产权"],
        ["有不动产", "无不动产"],
        ["自建房", "商品房"],
        ["独栋住宅", "公寓住宅"],
        ["城市住房", "农村住房"],
        ["购房", "未购房"],
        ["有固定住所", "无固定住所"],
        ["自住", "非自住"],
        ["拥有住房", "租赁住房"],
        # 增加新的选项 - 添加更多多样性
        ["自有房产", "租借房产"],
        ["有居所", "无居所"],
        ["房产所有", "非房产所有"],
        ["住宅拥有者", "非住宅拥有者"],
        ["自置房产", "非自置房产"],
        ["产权房", "非产权房"],
        ["商品住房", "保障住房"],
        ["普通住宅", "安置住宅"],
        ["家庭住房", "集体住房"],
        ["本地住房", "异地住房"],
        ["市区住房", "近郊住房"],
        ["正规住宅", "临时住所"],
        ["有房一族", "无房一族"],
        ["产权明晰", "产权不明"],
        ["住宅自有", "住宅租用"],
        ["定居", "流动"],
        ["有家", "无家"],
        ["定居型", "漂泊型"],
        ["稳定住所", "不稳定住所"],
        ["自购房产", "非自购房产"],
        ["一手房", "二手房"],
        ["首套房", "非首套房"],
        ["大户型", "小户型"],
        ["独立住宅", "共有住宅"],
        ["单身公寓", "家庭住宅"],
        ["高层住宅", "低层住宅"],
        ["私有住房", "公有住房"],
        ["国有住房", "私有住房"],
        ["经济适用房", "商品住房"],
        ["房改房", "市场房"],
        ["按揭房", "全款房"],
        ["核心区住房", "边缘区住房"],
        ["居住权", "无居住权"],
        ["住房自主", "住房依赖"],
        ["固定居所", "流动居所"],
        ["有宅基地", "无宅基地"],
        ["有不动产证", "无不动产证"],
        ["住房自主", "住房补贴"],
        ["住房自由", "住房约束"],
        ["安居型", "漂泊型"],
        ["本地居住", "异地居住"],
        ["有家可归", "无家可归"],
        ["常住人口", "流动人口"],
        ["定居者", "临时居住者"],
        ["住宅区", "非住宅区"],
        ["专属住所", "共享住所"],
        ["有宿舍", "无宿舍"],
        ["自建自住", "借住他人"],
        ["老城区", "新城区"],
        ["北方住宅", "南方住宅"],
        ["城区住宅", "郊区住宅"],
        ["有地房", "无地房"],
        ["院落式", "单元式"],
        ["住宅楼", "别墅区"],
        ["新房", "旧房"],
        ["住建部监管", "非住建部监管"],
        ["精装房", "毛坯房"],
        ["大产权", "小产权"],
        ["70年产权", "50年产权"],
        ["商住两用", "纯住宅"],
        ["标准间", "非标间"],
        ["封闭小区", "开放小区"],
        ["一类住宅", "二类住宅"],
        ["住宅性质", "非住宅性质"],
        ["市中心", "远郊区"],
        ["永久居留", "临时居留"],
        ["绿化住宅", "非绿化住宅"],
        ["水景住宅", "普通住宅"],
        ["花园洋房", "普通住宅"],
        ["联排别墅", "板楼住宅"],
        ["小高层", "超高层"],
        ["塔楼", "板楼"],
        ["内环线内", "外环线外"],
        ["生态住宅", "普通住宅"],
        ["阳光住宅", "普通住宅"],
        ["智能住宅", "传统住宅"],
        ["复式住宅", "单层住宅"],
        ["复合空间", "单一空间"],
        ["LOFT", "普通住宅"],
        ["一梯一户", "一梯多户"],
        ["小户型", "大平层"],
        ["老公房", "新建房"],
        ["平房", "楼房"],
        ["蜗居", "豪宅"],
        ["住改商", "纯住宅"],
        ["投资房产", "自住房产"],
        ["房东", "房客"],
        ["有担保房", "无担保房"],
        ["一人一房", "多人合租"],
        ["标准住房", "非标准住房"],
        ["定向安置", "市场购买"],
        ["单身公寓", "家庭公寓"],
        ["酒店式公寓", "住宅式公寓"],
        ["冬暖夏凉型", "四季恒温型"],
        ["优质学区房", "普通学区"],
        ["有前庭后院", "无庭院设计"],
        ["大社区大业主", "小社区小业主"],
        ["自然通风房", "空调依赖型"],
        ["阳台型", "无阳台型"],
        ["多功能厨房", "简易厨房"],
        ["充足储物空间", "无储物空间"],
        ["停车位充足", "无固定车位"],
        ["低密度居住", "高密度居住"],
        ["低楼层", "高楼层"],
        ["人车分流", "人车混行"],
        ["工况好", "维护差"],
        ["社区设施完善", "设施缺乏"],
        ["拎包入住", "需全面装修"],
        ["投资潜力大", "无投资价值"],
        ["学区房产", "非学区房"],
        ["全明户型", "暗卫暗厨"],
        ["采光好", "采光差"],
        ["无遮挡", "视野受限"],
        ["景观好", "无景观"],
        ["中央位置", "边缘位置"],
        ["交通便利", "交通不便"],
        ["购物方便", "生活不便"],
        ["配套齐全", "配套单一"],
        ["治安好", "安全隐忧"],
        ["噪音小", "噪音大"],
        ["空气好", "空气差"]
    ]
    
    marriage_options = [
        ["已婚", "未婚"],
        ["已婚", "单身"],
        ["有配偶", "无配偶"],
        ["家庭", "个人"],
        ["已成家", "未成家"],
        ["已婚人士", "单身人士"],
        ["结婚", "离异"],
        ["双身份", "单身份"],
        ["组建家庭", "未组建家庭"],
        ["已组建家庭", "未组建家庭"],
        ["有家庭", "无家庭"],
        ["已结婚", "未结婚"],
        # 增加新的选项 - 添加更多多样性
        ["有伴侣", "无伴侣"],
        ["已婚状态", "未婚状态"],
        ["有家室", "单身贵族"],
        ["伴侣关系", "独立个体"],
        ["夫妻", "独居"],
        ["已婚群体", "未婚群体"],
        ["家庭式", "个人式"],
        ["婚内", "婚外"],
        ["伦理关系", "社会关系"],
        ["婚姻状况-已婚", "婚姻状况-未婚"],
        ["亲密关系-有", "亲密关系-无"],
        ["法定婚姻", "非婚状态"],
        ["婚姻存续", "婚姻解除"],
        ["民政登记", "未登记"],
        ["领证", "未领证"],
        ["已婚身份", "未婚身份"],
        ["家庭成员", "个体成员"],
        ["有家属", "无家属"],
        ["婚配", "单一"],
        ["共同生活", "独立生活"],
        ["世代传承", "个人发展"],
        ["共同财产", "个人财产"],
        ["户主", "个人"],
        ["家庭户", "单身户"],
        ["集体户", "个人户"],
        ["夫妻生活", "单身生活"],
        ["共同纳税", "个人纳税"],
        ["婚内财产", "婚前财产"],
        ["初婚", "再婚"],
        ["已育", "未育"],
        ["子女抚养", "独立无养"],
        ["婚姻关系", "非婚关系"],
        ["婚姻期", "独身期"],
        ["社会家庭", "个体独立"],
        ["两口之家", "单身贵族"],
        ["婚姻义务", "个体自由"],
        ["家庭责任", "个人责任"],
        ["家庭户口", "个人户口"],
        ["恋爱状态", "单身状态"],
        ["情侣关系", "独立个体"],
        ["未婚青年", "已婚青年"],
        ["婚龄人口", "未婚人口"],
        ["已婚成年人", "单身成年人"],
        ["婚内群体", "婚外群体"],
        ["伴侣家庭", "独身个体"],
        ["育龄家庭", "非育龄个体"],
        ["多人户", "单人户"],
        ["婚姻期", "单身期"],
        ["双人住户", "单人住户"],
        ["婚后状态", "婚前状态"],
        ["两口子", "光棍"],
        ["有主", "无主"],
        ["名花有主", "名花无主"],
        ["婚姻生活", "单身生活"],
        ["情感生活有伴", "情感生活无伴"],
        ["婚姻关系中", "非婚姻关系"],
        ["已婚群体", "未婚群体"],
        ["结婚家庭", "单身个体"],
        ["夫妻双方", "独立个体"],
        ["婚恋状态", "非婚恋状态"],
        ["婚内状态", "婚外状态"],
        ["经历过婚姻", "未经历婚姻"],
        ["法律婚姻", "事实婚姻"],
        ["初婚人士", "未婚人士"],
        ["再婚人士", "从未结婚"],
        ["有固定伴侣", "无固定伴侣"],
        ["双重税负", "单一税负"],
        ["夫妻共同体", "个人独立体"],
        ["共同生活状态", "独立生活状态"],
        ["家庭成员身份", "个人独立身份"],
        ["婚姻法约束", "个人法约束"],
        ["婚内权利", "个人权利"],
        ["家庭束缚", "个人自由"],
        ["家庭关系", "社会关系"],
        ["配偶关系", "非配偶关系"],
        ["婚约", "非婚"],
        ["法定配偶", "非法定配偶"],
        ["婚姻登记", "个人登记"],
        ["恋爱中", "单身中"],
        ["婚礼完成", "婚礼未完成"],
        ["婚约状态", "非婚约状态"],
        ["结婚登记", "未登记"],
        ["夫妻名义", "个人名义"],
        ["婚龄人口", "适婚人口"],
        ["婚后生活", "单身生活"],
        ["双人世界", "一人世界"],
        ["夫妻财产", "个人财产"],
        ["婚内财富", "婚前财富"],
        ["法定关系", "非法定关系"],
        ["婚内身份", "婚外身份"],
        ["幸福家庭", "幸福单身"],
        ["婚姻幸福", "单身幸福"],
        ["已婚已育", "未婚未育"],
        ["合法婚姻", "非法婚姻"],
        ["有配偶者", "无配偶者"],
        ["婚礼已办", "婚礼未办"],
        ["婚姻责任", "个人责任"],
        ["法律责任-家庭", "法律责任-个人"],
        ["婚姻维系", "个人发展"],
        ["家庭地位", "个人地位"],
        ["配偶依赖", "个人独立"],
        ["婚内生活", "独身生活"],
        ["伴侣模式", "独行模式"],
        ["伴侣居住", "独自居住"],
        ["双方决策", "单方决策"],
        ["共同决定", "个人决定"],
        ["家庭为先", "个人为先"],
        ["家庭共识", "个人选择"],
        ["家庭成员", "个人单位"],
        ["家庭系统", "个人系统"],
        ["家庭单元", "个人单元"],
        ["家庭社会", "个人社会"],
        ["婚姻圈", "单身圈"],
        ["夫妻称谓", "个人称谓"],
        ["婚内身份", "单身身份"],
        ["婚姻保障", "个人保障"],
        ["婚姻保险", "个人保险"],
        ["家庭保单", "个人保单"]
    ]
    
    health_options = [
        ["强壮", "不强壮"],
        ["健康", "亚健康"],
        ["健壮", "体弱"],
        ["身体好", "身体差"],
        ["体质佳", "体质弱"],
        ["健康状况良好", "健康状况一般"],
        ["无慢性病", "有慢性病"],
        ["身体健康", "身体不健康"],
        ["体魄强健", "体魄较弱"],
        ["体质健壮", "体质较弱"],
        ["良好体质", "一般体质"],
        ["健康指标良好", "健康指标一般"],
        # 增加新的选项 - 添加更多多样性
        ["无病史", "有病史"],
        ["体检合格", "体检不合格"],
        ["免疫力强", "免疫力弱"],
        ["高抵抗力", "低抵抗力"],
        ["身心健康", "身心亚健康"],
        ["精力充沛", "精力不足"],
        ["活力充足", "活力不足"],
        ["体能充沛", "体能不足"],
        ["生命力强", "生命力弱"],
        ["身体素质好", "身体素质差"],
        ["身体机能佳", "身体机能弱"],
        ["无病痛", "有病痛"],
        ["无不适", "有不适"],
        ["良好状态", "较差状态"],
        ["体魄健全", "体魄不全"],
        ["身体无恙", "身体多恙"],
        ["无疾病", "有疾病"],
        ["正常体重", "异常体重"],
        ["运动能力强", "运动能力弱"],
        ["身材匀称", "身材不匀称"],
        ["肌肉发达", "肌肉不发达"],
        ["精神状态好", "精神状态差"],
        ["心理健康", "心理亚健康"],
        ["心理状态佳", "心理状态差"],
        ["情绪稳定", "情绪不稳定"],
        ["压力承受力强", "压力承受力弱"],
        ["抗压能力强", "抗压能力弱"],
        ["健康生活方式", "不健康生活方式"],
        ["饮食规律", "饮食不规律"],
        ["作息规律", "作息不规律"],
        ["生活习惯好", "生活习惯差"],
        ["无不良嗜好", "有不良嗜好"],
        ["不吸烟", "吸烟"],
        ["不饮酒", "饮酒"],
        ["不熬夜", "熬夜"],
        ["有锻炼习惯", "无锻炼习惯"],
        ["经常运动", "很少运动"],
        ["体能佳", "体能差"],
        ["体格健全", "体格不健全"],
        ["高耐力", "低耐力"],
        ["健康身心", "亚健康身心"],
        ["活力四射", "无精打采"],
        ["精力旺盛", "精力不济"],
        ["身体柔韧", "身体僵硬"],
        ["均衡营养", "营养不均衡"],
        ["健康饮食", "不健康饮食"],
        ["药物不依赖", "药物依赖"],
        ["无药物史", "有药物史"],
        ["无过敏史", "有过敏史"],
        ["器官功能正常", "器官功能异常"],
        ["血压正常", "血压异常"],
        ["心率正常", "心率异常"],
        ["血糖正常", "血糖异常"],
        ["血脂正常", "血脂异常"],
        ["肝功能正常", "肝功能异常"],
        ["肾功能正常", "肾功能异常"],
        ["呼吸系统健康", "呼吸系统异常"],
        ["消化系统健康", "消化系统异常"],
        ["神经系统健康", "神经系统异常"],
        ["内分泌正常", "内分泌异常"],
        ["免疫系统正常", "免疫系统异常"],
        ["骨骼健康", "骨骼问题"],
        ["关节灵活", "关节僵硬"],
        ["视力正常", "视力异常"],
        ["听力正常", "听力异常"],
        ["嗅觉正常", "嗅觉异常"],
        ["味觉正常", "味觉异常"],
        ["触觉正常", "触觉异常"],
        ["平衡感好", "平衡感差"],
        ["协调性好", "协调性差"],
        ["反应速度快", "反应速度慢"],
        ["敏捷度高", "敏捷度低"],
        ["肌肉力量大", "肌肉力量小"],
        ["肌肉耐力好", "肌肉耐力差"],
        ["心肺功能好", "心肺功能差"],
        ["代谢正常", "代谢异常"],
        ["新陈代谢快", "新陈代谢慢"],
        ["消化能力强", "消化能力弱"],
        ["排泄正常", "排泄异常"],
        ["睡眠质量好", "睡眠质量差"],
        ["夜间睡眠好", "夜间睡眠差"],
        ["活动能力强", "活动能力弱"],
        ["生活自理能力强", "生活自理能力弱"],
        ["认知能力好", "认知能力差"],
        ["记忆力好", "记忆力差"],
        ["注意力集中", "注意力不集中"],
        ["精神焕发", "精神萎靡"],
        ["意志力强", "意志力弱"],
        ["自我管理能力强", "自我管理能力弱"],
        ["适应能力强", "适应能力弱"],
        ["应对压力能力强", "应对压力能力弱"],
        ["身体功能完善", "身体功能缺损"],
        ["身体状况良好", "身体状况较差"],
        ["充满活力", "缺乏活力"],
        ["有活力", "无活力"],
        ["神清气爽", "疲惫不堪"],
        ["生机勃勃", "无精打采"],
        ["生命体征稳定", "生命体征不稳"],
        ["心肺功能强", "心肺功能弱"],
        ["新陈代谢旺盛", "新陈代谢缓慢"],
        ["细胞活力高", "细胞活力低"],
        ["恢复能力强", "恢复能力弱"],
        ["抗疲劳能力强", "易疲劳"],
        ["高能量储备", "低能量储备"],
        ["营养状态佳", "营养状态差"],
        ["代谢平衡", "代谢失衡"],
        ["激素水平正常", "激素水平异常"],
        ["激素分泌均衡", "激素分泌失调"],
        ["免疫功能强", "免疫功能弱"],
        ["抗病能力强", "易感染"],
        ["伤口愈合快", "伤口愈合慢"],
        ["生物年龄轻", "生物年龄重"],
        ["生理节律稳定", "生理节律紊乱"],
        ["生物钟准确", "生物钟失调"],
        ["有氧能力佳", "有氧能力差"],
        ["肌肉弹性好", "肌肉松弛"],
        ["体脂率正常", "体脂率异常"],
        ["体型均衡", "体型失衡"],
        ["身体协调性好", "身体协调性差"],
        ["柔韧性好", "柔韧性差"],
        ["核心力量强", "核心力量弱"],
        ["肢体灵活", "肢体僵硬"],
        ["神经反应快", "神经反应慢"],
        ["肌肉协调好", "肌肉协调差"],
        ["血管弹性好", "血管硬化"],
        ["血液循环佳", "血液循环差"],
        ["组织灌注良好", "组织灌注不足"],
        ["无生活不能", "生活不能"],
        ["健康指数高", "健康指数低"],
        ["疾病风险低", "疾病风险高"],
        ["耐受力强", "耐受力弱"]
    ]
    
    # 随机决定是否显示小计列
    show_subtotal = random.random() < 0.7
    
    # 随机挑选类别名称
    housing_pair = random.choice(housing_options)
    marriage_pair = random.choice(marriage_options)
    health_pair = random.choice(health_options)
    
    # 随机决定是否交换顺序
    if random.random() < 0.3:
        housing_pair.reverse()
    if random.random() < 0.3:
        marriage_pair.reverse()
    if random.random() < 0.3:
        health_pair.reverse()
    
    # 计算列数
    cols_per_housing = 6 if show_subtotal else 4
    
    # 第一行：住房状况
    html += f'<th colspan="{cols_per_housing}" style="{cell_style}">{housing_pair[0]}</th>'
    html += f'<th colspan="{cols_per_housing}" style="{cell_style}">{housing_pair[1]}</th>'
    html += '</tr>'
    
    # 第二行：婚姻状况
    html += '<tr>'
    cols_per_marriage = 3 if show_subtotal else 2
    for _ in range(2):  # 有房和无房分别有已婚/未婚
        html += f'<th colspan="{cols_per_marriage}" style="{cell_style}">{marriage_pair[0]}</th>'
        html += f'<th colspan="{cols_per_marriage}" style="{cell_style}">{marriage_pair[1]}</th>'
    html += '</tr>'
    
    # 第三行：健康状况
    html += '<tr>'
    for _ in range(4):  # 4个组合
        html += f'<th style="{cell_style}">{health_pair[0]}</th>'
        html += f'<th style="{cell_style}">{health_pair[1]}</th>'
        if show_subtotal:
            html += f'<th style="{cell_style}">小计</th>'
    html += '</tr></thead>'
    
    # 数据行
    html += '<tbody>'
    current_first_level = None
    
    # 确保行层级非空
    if not row_level_values or len(row_level_values) < 2:
        # 创建默认行
        demographics = [
            {"label": "年龄段", "values": ["<18岁", "18-35岁", "36-50岁", "51-65岁", ">65岁"]},
            {"label": "教育水平", "values": ["初中及以下", "高中/中专", "大专/本科", "研究生及以上"]},
            {"label": "职业类型", "values": ["学生", "白领", "蓝领", "自由职业", "退休人员"]},
            {"label": "收入水平", "values": ["低收入", "中低收入", "中等收入", "中高收入", "高收入"]},
            {"label": "户籍类型", "values": ["城镇户籍", "农村户籍", "非本地户籍", "外籍"]},
            {"label": "家庭规模", "values": ["1人", "2人", "3-4人", "5人及以上"]},
            {"label": "居住年限", "values": ["<1年", "1-3年", "3-5年", "5-10年", ">10年"]},
            {"label": "社会保障", "values": ["有社保", "无社保", "部分保障"]},
            {"label": "就业状态", "values": ["全职", "兼职", "自雇", "无业", "退休"]},
            {"label": "居住区域", "values": ["市中心", "城市郊区", "县城", "乡镇", "农村"]}
        ]
        
        # 随机选择一个人口统计特征
        demo_feature = random.choice(demographics)
        row_data = []
        
        # 创建一些默认分组
        group_options = [
            ["分组A", "分组B", "分组C"],
            ["类型1", "类型2", "类型3"],
            ["北区", "南区", "中区"],
            ["一级", "二级", "三级"],
            ["高层次", "中层次", "基层"],
            ["一类人群", "二类人群", "三类人群"],
            ["主流", "非主流", "其他"],
            ["核心", "边缘", "特殊"],
            ["标准组", "特殊组", "其他组"],
            ["样本1", "样本2", "样本3"]
        ]
        
        groups = random.choice(group_options)
        
        for group in groups:
            for value in demo_feature["values"]:
                row_data.append({
                    "first_level": group,
                    "second_level": value
                })
    else:
        # 从行层级数据生成行
        row_data = []
        for i in range(len(row_level_values[0])):
            for j in range(len(row_level_values[1])):
                row_data.append({
                    "first_level": row_level_values[0][i],
                    "second_level": row_level_values[1][j]
                })
    
    # 生成行数据
    for row in row_data:
        html += '<tr>'
        
        # 第一级标题（可能需要合并单元格）
        if row["first_level"] != current_first_level:
            # 计算当前第一级有多少行
            same_first_level_count = sum(1 for r in row_data if r["first_level"] == row["first_level"])
            html += f'<td rowspan="{same_first_level_count}" style="{cell_style}">{row["first_level"]}</td>'
            current_first_level = row["first_level"]
        
        # 第二级标题
        html += f'<td style="{cell_style}">{row["second_level"]}</td>'
        
        # 计算总列数
        total_data_cols = 12 if show_subtotal else 8
        
        # 生成单元格数据
        for _ in range(total_data_cols):
            cell_value = generate_cell_value(data_type, min_value, max_value, 
                                          empty_probability, special_chars_probability, 
                                          special_chars, cell_rules)
            html += f'<td style="{cell_style}">{cell_value}</td>'
        
        html += '</tr>'
    
    return html

def generate_transport_table_html(title_text, row_levels, row_level_values, cell_style,
                               header_bg_color, data_type, min_value, max_value,
                               empty_probability, special_chars_probability,
                               special_chars, cell_rules):
    """生成交通工具交叉表的HTML"""
    html = '<thead><tr>'
    html += f'<th rowspan="2" colspan="2" style="{cell_style}">{title_text}</th>'
    
    # 种类选项
    category_options = [
        ["燃油", "电动", "混合动力"],
        ["自有", "租赁", "共享"],
        ["国产", "合资", "进口"],
        ["路面", "轨道", "水路"],
        ["城市", "城际", "长途"],
        ["客运", "货运", "专用"],
        ["低排放", "标准排放", "高排放"],
        ["经济型", "舒适型", "豪华型"],
        ["公共交通", "私家车", "商用车"],
        ["新能源", "传统能源", "其他能源"],
        ["小型", "中型", "大型"],
        ["高速", "中速", "低速"],
        # 增加新的选项 - 添加更多多样性
        ["短途", "中途", "长途"],
        ["常规车", "特种车", "工程车"],
        ["陆运", "海运", "空运"],
        ["定期", "不定期", "临时"],
        ["重载", "轻载", "超载"],
        ["有轨", "无轨", "混合"],
        ["快速", "慢速", "中速"],
        ["载客", "载货", "特殊用途"],
        ["汽油", "柴油", "天然气"],
        ["普通动力", "增程式", "插电式"],
        ["自驾", "代驾", "无人驾驶"],
        ["人力", "机动", "电力"],
        ["家用", "商用", "公用"],
        ["专车", "顺风车", "拼车"],
        ["豪华", "标准", "经济"],
        ["城区", "郊区", "跨区"],
        ["省内", "省际", "国际"],
        ["单向", "双向", "环线"],
        ["定线", "灵活线", "自由线"],
        ["高峰", "平峰", "低峰"],
        ["紧急", "常规", "特殊"],
        ["临时", "固定", "季节性"],
        ["直达", "普通", "快速"],
        ["环保", "节能", "高能"],
        ["超低排放", "低排放", "零排放"],
        ["豪华版", "舒适版", "标准版"],
        ["A级", "B级", "C级"],
        ["豪华车", "普通车", "经济车"],
        ["特快", "快速", "慢速"],
        ["高档", "中档", "低档"],
        ["大众", "商务", "豪华"],
        ["常规能源", "清洁能源", "新型能源"],
        ["短轴距", "中轴距", "长轴距"],
        ["高承载", "中承载", "低承载"],
        ["重型", "中型", "轻型"],
        ["单人用", "双人用", "多人用"],
        ["限速型", "高速型", "超速型"],
        ["节能型", "动力型", "性能型"],
        ["舒适性", "实用性", "性价比"],
        ["首创", "改进", "仿制"],
        ["原装进口", "国产合资", "纯国产"],
        ["特价", "中价", "高价"],
        ["高附加值", "中附加值", "低附加值"],
        ["政府采购", "企业采购", "个人采购"],
        ["团购", "个购", "批量"],
        ["专业化", "通用化", "定制化"],
        ["单一功能", "多功能", "综合功能"],
        ["智能型", "机械型", "混合型"],
        ["内燃型", "外燃型", "非燃型"],
        ["手动挡", "自动挡", "混合挡"],
        ["前驱", "后驱", "四驱"],
        ["乘用", "商用", "工程用"],
        ["城市道路", "高速公路", "越野路况"],
        ["山地", "平原", "丘陵"],
        ["水陆两用", "陆上专用", "水上专用"],
        ["轮式", "履带式", "轨道式"],
        ["自行式", "牵引式", "组合式"],
        ["整车", "分体式", "模块化"],
        ["固定式", "折叠式", "变形式"],
        ["标准尺寸", "超大尺寸", "迷你尺寸"],
        ["民用", "军用", "特种用途"],
        ["展示用", "使用中", "备用"],
        ["科研用", "教学用", "实际使用"],
        ["单一品牌", "多品牌", "无品牌"],
        ["原厂配件", "副厂配件", "自制配件"],
        ["有维保", "无维保", "部分维保"],
        ["高功率", "中功率", "低功率"],
        ["新型", "传统", "过渡型"],
        ["过户车", "新车", "库存车"],
        ["常温", "冷藏", "保温"],
        ["普通路况", "特殊路况", "极限路况"],
        ["喷气", "涡轮", "活塞"],
        ["电池供能", "燃料供能", "混合供能"],
        ["太阳能", "风能", "传统能源"],
        ["零部件国产", "零部件进口", "零部件混合"],
        ["全封闭", "半封闭", "开放式"],
        ["高底盘", "中底盘", "低底盘"],
        ["特种燃料", "常规燃料", "混合燃料"],
        ["固定路线", "灵活路线", "临时路线"],
        ["夜间行驶", "日间行驶", "全天候"],
        ["冬季用", "夏季用", "四季通用"],
        ["高海拔", "低海拔", "全海拔"],
        ["大气压", "低气压", "变气压"],
        ["耐高温", "耐低温", "温度适应"],
        ["左舵", "右舵", "可变舵"],
        ["简易型", "复杂型", "特种型"],
        ["工业化", "手工化", "半工业化"],
        ["集中控制", "分散控制", "混合控制"],
        ["单控制", "双控制", "多控制"]
    ]
    
    # 年份/区间选项
    period_options = [
        ["2019-2020年", "2021-2022年", "2023年以后"],
        ["2018-2020年", "2020-2022年", "2022年至今"],
        ["前期", "中期", "后期"],
        ["初始阶段", "发展阶段", "成熟阶段"],
        ["第一季度", "第二季度", "第三季度"],
        ["一季度", "二季度", "三季度"],
        ["上半年", "中间期", "下半年"],
        ["早期", "中期", "近期"],
        ["前三年", "中三年", "后三年"],
        ["试运行期", "正常期", "高峰期"],
        # 增加新的选项 - 添加更多多样性
        ["2017年前", "2017-2020年", "2020年后"],
        ["2010-2015年", "2015-2020年", "2020年以后"],
        ["1-3月", "4-6月", "7-12月"],
        ["春季", "夏季", "秋冬季"],
        ["节假日", "工作日", "周末"],
        ["早高峰", "平峰", "晚高峰"],
        ["白天", "夜间", "通宵"],
        ["每日", "每周", "每月"],
        ["短期", "中期", "长期"],
        ["临时", "周期性", "常年"],
        ["常规", "特殊", "紧急"],
        ["1-5年", "6-10年", "10年以上"],
        ["出厂初期", "使用中期", "报废前期"],
        ["试运行", "正常运行", "高峰运行"],
        ["前30%", "中间40%", "后30%"],
        ["初级阶段", "中级阶段", "高级阶段"],
        ["年初", "年中", "年末"],
        ["季初", "季中", "季末"],
        ["上旬", "中旬", "下旬"],
        ["前一周期", "当前周期", "下一周期"],
        ["启动阶段", "运行阶段", "终止阶段"],
        ["规划期", "实施期", "验收期"],
        ["一阶段", "二阶段", "三阶段"],
        ["临时期", "过渡期", "稳定期"],
        ["冬季", "春夏", "秋季"],
        ["雨季", "旱季", "正常季"],
        ["暑期", "学期", "寒假"],
        ["淡季", "平季", "旺季"],
        ["第一代", "第二代", "第三代"],
        ["上半月", "中半月", "下半月"],
        ["老旧时期", "更新时期", "现代时期"],
        ["拥挤时段", "平常时段", "空闲时段"],
        ["1号-10号", "11号-20号", "21号-31号"],
        ["单日", "双日", "全日"],
        ["星期一", "星期三", "星期五"],
        ["工作日早", "工作日中", "工作日晚"],
        ["短途期", "中途期", "长途期"],
        ["每日高峰", "每周高峰", "每月高峰"],
        ["周一至周三", "周四至周五", "周末"],
        ["上午", "下午", "晚上"],
        ["早班", "中班", "晚班"],
        ["寒冷季节", "温暖季节", "炎热季节"],
        ["A计划期", "B计划期", "C计划期"],
        ["研发期", "生产期", "销售期"],
        ["集中期", "分散期", "混合期"],
        ["日间", "夜间", "全天"],
        ["晴天", "雨天", "极端天气"],
        ["上升期", "平稳期", "下降期"],
        ["紧缺期", "平衡期", "过剩期"],
        ["1-4小时", "4-8小时", "8-12小时"],
        ["定点", "不定点", "周期性"],
        ["按月", "按季", "按年"],
        ["每日定时", "每周定时", "每月定时"],
        ["特殊日", "普通日", "重要日"],
        ["常规时段", "特殊时段", "紧急时段"],
        ["冷期", "热期", "过渡期"],
        ["起步期", "加速期", "稳定期"],
        ["导入期", "成长期", "成熟期"],
        ["内测", "公测", "正式"],
        ["试验中", "试用中", "正式使用"],
        ["测试版", "预发布版", "正式版"],
        ["临时作业", "定期作业", "长期作业"],
        ["特殊工况", "常规工况", "应急工况"],
        ["实验阶段", "应用阶段", "推广阶段"],
        ["日常时期", "特殊时期", "危急时期"],
        ["最初版本", "改进版本", "最终版本"],
        ["初期投入", "运行期", "升级期"],
        ["第一季", "第二季", "第三季"],
        ["灯光管制", "中间时段", "自由通行"],
        ["限行期", "非限行期", "特殊日"],
        ["高峰管制", "平峰管制", "无管制"],
        ["封闭时段", "半封闭", "开放时段"],
        ["春运", "夏运", "日常"],
        ["节前", "节中", "节后"],
        ["开放日", "常规日", "维护日"],
        ["冬春", "夏", "秋"],
        ["检修期", "营运期", "备用期"],
        ["首轮", "次轮", "末轮"],
        ["过渡阶段", "稳定阶段", "优化阶段"],
        ["特殊季节", "常规季节", "极端季节"],
        ["年轻期", "成熟期", "老化期"],
        ["热点期", "常规期", "冷门期"],
        ["集中式", "分散式", "混合式"],
        ["人工控制", "半自动控制", "全自动控制"],
        ["临时计划", "常规计划", "长期计划"],
        ["初次使用", "中期使用", "长期使用"],
        ["热身期", "完全期", "冷却期"],
        ["专项时期", "常规时期", "休整时期"],
        ["优先级高", "优先级中", "优先级低"],
        ["频繁期", "常规期", "稀少期"],
        ["超负荷", "满负荷", "半负荷"],
        ["全速", "巡航", "低速"],
        ["极速状态", "正常状态", "节能状态"],
        ["启动中", "运行中", "关闭中"]
    ]
    
    # 创建一些默认行
    
    # 交通工具类型选项
    vehicle_type_options = [
        ["轿车", "SUV", "卡车", "客车"],
        ["小轿车", "中型车", "大型车", "专用车"],
        ["公交车", "出租车", "私家车", "网约车"],
        ["地铁", "公交", "火车", "飞机"],
        ["陆运", "水运", "空运", "其他运输"],
        ["A类车", "B类车", "C类车", "D类车"],
        ["微型车", "紧凑型", "中型车", "大型车"],
        ["客车", "货车", "特种车", "两轮车"],
        ["班车", "通勤车", "校车", "旅游车"],
        ["个人交通", "公共交通", "物流运输", "专业运输"],
        # 增加新的选项 - 添加更多多样性
        ["轿车", "掀背车", "旅行车", "跑车"],
        ["MPV", "SUV", "CUV", "跨界车"],
        ["微面", "皮卡", "厢式车", "封闭货车"],
        ["城市公交", "长途客车", "旅游大巴", "校车"],
        ["重型卡车", "中型卡车", "轻型卡车", "微型卡车"],
        ["摩托车", "电动车", "自行车", "平衡车"],
        ["轨道交通", "公路交通", "水路交通", "航空交通"],
        ["高铁", "动车", "普速列车", "市郊列车"],
        ["地铁", "轻轨", "有轨电车", "磁悬浮"],
        ["大型客船", "中型客船", "小型客船", "游艇"],
        ["双层巴士", "铰接式公交", "中巴", "小巴"],
        ["直升机", "小型飞机", "中型客机", "大型客机"],
        ["专用牵引车", "专用作业车", "专用载货车", "专用乘用车"],
        ["农用车", "工程车", "军用车", "特种车"],
        ["豪华轿车", "中高级轿车", "中级轿车", "经济型轿车"],
        ["新能源汽车", "混合动力车", "燃油车", "氢能源车"],
        ["老年代步车", "残疾人专用车", "警车", "救护车"],
        ["邮政车", "环卫车", "消防车", "工程抢险车"],
        ["机场摆渡车", "高尔夫球车", "观光车", "园区车"],
        ["缆车", "索道", "滑道", "轨道游乐"],
        ["铲车", "推土机", "挖掘机", "起重机"],
        ["拖拉机", "收割机", "播种机", "田间管理机"],
        ["运钞车", "军用装甲车", "反恐特警车", "移动指挥车"],
        ["A00级微型车", "A0级小型车", "A级紧凑型", "B级中型车"],
        ["C级中大型车", "D级大型车", "跑车", "越野车"],
        ["轻型客车", "中型客车", "大型客车", "特大型客车"],
        ["小排量摩托", "中排量摩托", "大排量摩托", "越野摩托"],
        ["城市轿车", "商务车", "越野车", "赛车"],
        ["教练车", "试驾车", "陪练车", "比赛用车"],
        ["单车道车辆", "多车道车辆", "特殊道路车辆", "全地形车辆"],
        ["单座车", "双座车", "多座车", "商务座车"],
        ["移动商铺车", "流动餐车", "宣传车", "展示车"],
        ["城际客车", "长途客车", "旅游客车", "通勤客车"],
        ["普通客船", "快速客船", "豪华客船", "渡轮"],
        ["公务船", "执法船", "救援船", "科考船"],
        ["固定翼飞机", "旋翼飞机", "滑翔机", "水上飞机"],
        ["热气球", "飞艇", "滑翔伞", "动力伞"],
        ["载人火箭", "航天飞机", "太空舱", "天地往返"],
        ["电动车", "汽油车", "柴油车", "混动车"],
        ["手动挡车", "自动挡车", "无级变速", "序列变速"],
        ["前驱车", "后驱车", "四驱车", "全时四驱"],
        ["汽油发动机", "柴油发动机", "新能源发动机", "混合动力"],
        ["排量小于1.0L", "排量1.0-1.6L", "排量1.6-2.0L", "排量大于2.0L"],
        ["两门车", "三门车", "四门车", "五门车"],
        ["轿车底盘", "SUV底盘", "跨界底盘", "多用途底盘"],
        ["1-4人座", "5-8人座", "9-19人座", "20人以上"],
        ["有轨车辆", "无轨车辆", "气垫车辆", "磁悬浮车辆"],
        ["普通货船", "集装箱船", "滚装船", "散货船"],
        ["油轮", "液化气船", "化学品船", "特种船"],
        ["内河船", "江海船", "远洋船", "特种船"],
        ["铁轨动力", "外部动力", "内部动力", "混合动力"],
        ["特种列车", "普通列车", "快速列车", "高速列车"],
        ["长途客机", "中程客机", "短途客机", "通用航空"],
        ["传统出租", "网约专车", "网约快车", "共享汽车"],
        ["共享单车", "共享电单车", "共享汽车", "共享特种车"],
        ["内燃机车", "电力机车", "蒸汽机车", "混合动力机车"],
        ["塔台操控", "地面操控", "远程操控", "自动驾驶"],
        ["机械控制", "电子控制", "液压控制", "混合控制"],
        ["自然吸气", "涡轮增压", "机械增压", "电动增压"],
        ["运煤车", "运油车", "混凝土车", "垃圾车"],
        ["厢式货车", "平板货车", "罐式货车", "自卸货车"],
        ["牵引车", "半挂车", "全挂车", "多挂车"],
        ["保温车", "冷藏车", "医药车", "危险品车"],
        ["牲畜运输", "蔬果运输", "粮食运输", "液体运输"],
        ["城市物流", "城际物流", "跨境物流", "多式联运"],
        ["单桥车", "双桥车", "多桥车", "全挂车"],
        ["可堆叠集装箱", "非堆叠集装箱", "专用集装箱", "散装货物"],
        ["民航客机", "军用飞机", "通用航空", "实验航空"],
        ["液体运输", "固体运输", "气体运输", "混合运输"],
        ["单引擎", "双引擎", "四引擎", "多引擎"],
        ["推进式", "牵引式", "复合式", "阻力式"],
        ["海上航行", "江河航行", "湖泊航行", "运河航行"],
        ["特大型", "大型", "中型", "小型"],
        ["过街天桥", "地下通道", "交叉路口", "停车设施"],
        ["手动操作", "自动操作", "混合操作", "智能操作"],
        ["普通油", "高级油", "特种油", "专用油"],
        ["定点供应", "常规供应", "临时供应", "紧急供应"],
        ["维修中", "检测中", "保养中", "使用中"],
        ["出库", "在库", "待料", "报废"],
        ["在役", "退役", "备用", "封存"],
        ["常备军", "预备役", "退役", "未服役"],
        ["在航", "靠港", "维护", "封存"]
    ]
    
    # 子分类选项
    subcat_options = [
        ["区域A", "区域B", "区域C"],
        ["北区", "南区", "东区"],
        ["城区", "郊区", "远郊"],
        ["主线", "支线", "专线"],
        ["高峰期", "平峰期", "低峰期"],
        ["工作日", "周末", "假日"],
        ["一线城市", "二线城市", "三线城市"],
        ["核心区", "次核心", "外围区"],
        ["繁忙路段", "普通路段", "低流量路段"],
        ["常规", "紧急", "特殊"],
        # 增加新的选项 - 添加更多多样性
        ["内环", "中环", "外环"],
        ["干线", "支线", "联络线"],
        ["商业区", "住宅区", "工业区"],
        ["东北区", "西南区", "中部区"],
        ["上行", "下行", "双向"],
        ["高速", "国道", "省道"],
        ["城际", "市内", "跨区域"],
        ["地面", "高架", "地下"],
        ["桥梁", "隧道", "平路"],
        ["收费站", "服务区", "加油站"],
        ["红线区", "黄线区", "绿线区"],
        ["管制区", "限行区", "自由区"],
        ["试点区", "推广区", "全覆盖区"],
        ["出口", "入口", "中途"],
        ["站内", "站外", "过境"],
        ["T1航站楼", "T2航站楼", "T3航站楼"],
        ["国内线", "国际线", "地区线"],
        ["客运站", "货运站", "中转站"],
        ["港区", "码头", "锚地"],
        ["始发站", "终点站", "中间站"],
        ["直达", "中转", "环线"],
        ["特快", "快速", "普通"],
        ["专用通道", "混合通道", "应急通道"],
        ["高峰", "平峰", "低谷"],
        ["市区中心", "城乡结合部", "远郊区县"],
        ["旅游景区", "商业中心", "工业园区"],
        ["校园内", "校园周边", "学生公寓"],
        ["医院内", "医院周边", "医疗区"],
        ["居民小区", "商业街区", "文化街区"],
        ["行政区", "生活区", "工作区"],
        ["前排", "中排", "后排"],
        ["发车区", "候车区", "临时停靠区"],
        ["一楼", "二楼", "三楼"],
        ["A通道", "B通道", "C通道"],
        ["北向南", "东向西", "环形"],
        ["白天段", "夜间段", "全天段"],
        ["可视区", "半可视区", "不可视区"],
        ["无障碍区", "障碍区", "混合区"],
        ["慢行系统", "快行系统", "综合系统"],
        ["行人区", "非机动车区", "机动车区"],
        ["混合交通区", "分区交通区", "限制交通区"],
        ["军事区", "民用区", "混合区"],
        ["公共区", "专用区", "临时区"],
        ["主城区", "副城区", "卫星城"],
        ["集中控制", "分散控制", "无控制"],
        ["机动", "非机动", "混合"],
        ["定点", "不定点", "循环"],
        ["有站台", "无站台", "临时站"],
        ["首末站", "中间站", "换乘站"],
        ["东西向", "南北向", "斜向"],
        ["平行线", "交叉线", "环形线"],
        ["同向", "反向", "双向"],
        ["上坡", "下坡", "平路"],
        ["转弯", "直行", "环岛"],
        ["加速区", "匀速区", "减速区"],
        ["独立区", "共享区", "混合区"],
        ["原位", "新位", "备用位"],
        ["保留区", "拆除区", "改造区"],
        ["旧区", "新区", "改扩建区"],
        ["扩张区", "收缩区", "平衡区"],
        ["封闭", "半封闭", "开放"],
        ["一级路", "二级路", "三级路"],
        ["A点", "B点", "C点"],
        ["一区", "二区", "三区"],
        ["临湖", "临江", "临海"],
        ["平地", "丘陵", "山区"],
        ["草原", "荒漠", "森林"],
        ["湿地", "旱地", "半湿润"],
        ["寒带", "温带", "热带"],
        ["晴天区", "雨天区", "雪天区"],
        ["主干道", "次干道", "支路"],
        ["辅道", "主道", "快速路"],
        ["地面层", "上层", "下层"],
        ["西区", "东区", "南区"],
        ["老区", "中区", "新区"],
        ["临时停车位", "固定停车位", "过境停车位"],
        ["发车点", "终点", "中转点"],
        ["上客区", "下客区", "候车区"],
        ["装货区", "卸货区", "周转区"],
        ["配送区", "揽收区", "储存区"],
        ["内部区", "外部区", "过渡区"],
        ["贵宾区", "普通区", "经济区"],
        ["观光区", "普通区", "快速区"],
        ["城市群", "行政村", "边远村"]
    ]
    
    # 随机选择
    categories = random.choice(category_options)
    periods = random.choice(period_options)
    
    # 随机决定是否交换顺序
    if random.random() < 0.3:
        categories.reverse()
    
    # 第一行：主分类
    for category in categories:
        html += f'<th colspan="{len(periods)}" style="{cell_style}">{category}</th>'
    html += '</tr>'
    
    # 第二行：时间段
    html += '<tr>'
    for _ in range(len(categories)):  # 对每个主分类
        for period in periods:
            html += f'<th style="{cell_style}">{period}</th>'
    html += '</tr></thead>'
    
    # 数据行
    html += '<tbody>'
    current_first_level = None
    
    # 确保行层级非空
    if not row_level_values or len(row_level_values) < 2:
        # 创建一些默认行
        
        # 交通工具类型选项
        vehicle_types = random.choice(vehicle_type_options)
        
        # 创建行数据
        row_data = []
        
        # 子分类选项
        subcats = random.choice(subcat_options)
        
        for vehicle in vehicle_types:
            for subcat in subcats:
                row_data.append({
                    "first_level": vehicle,
                    "second_level": subcat
                })
    else:
        # 从行层级数据生成行
        row_data = []
        for i in range(len(row_level_values[0])):
            for j in range(len(row_level_values[1])):
                row_data.append({
                    "first_level": row_level_values[0][i],
                    "second_level": row_level_values[1][j]
                })
    
    # 生成行数据
    for row in row_data:
        html += '<tr>'
        
        # 第一级标题（可能需要合并单元格）
        if row["first_level"] != current_first_level:
            # 计算当前第一级有多少行
            same_first_level_count = sum(1 for r in row_data if r["first_level"] == row["first_level"])
            html += f'<td rowspan="{same_first_level_count}" style="{cell_style}">{row["first_level"]}</td>'
            current_first_level = row["first_level"]
        
        # 第二级标题
        html += f'<td style="{cell_style}">{row["second_level"]}</td>'
        
        # 生成单元格数据 - 总列数等于类别数 * 年份数
        total_cols = len(categories) * len(periods)
        for _ in range(total_cols):
            cell_value = generate_cell_value(data_type, min_value, max_value,
                                          empty_probability, special_chars_probability,
                                          special_chars, cell_rules, is_numeric_likely=True)
            html += f'<td style="{cell_style}">{cell_value}</td>'
        
        html += '</tr>'
    
    return html

def generate_education_table_html(title_text, row_levels, row_level_values, cell_style,
                                header_bg_color, data_type, min_value, max_value,
                                empty_probability, special_chars_probability,
                                special_chars, cell_rules):
    """生成教育交叉表的HTML"""
    html = '<thead><tr>'
    html += f'<th rowspan="2" colspan="2" style="{cell_style}">{title_text}</th>'
    
    # 教育类别选项
    category_options = [
        ["公立", "民办", "国际"],
        ["文科", "理科", "艺术"],
        ["义务教育", "高中", "高等教育"],
        ["普通教育", "职业教育", "继续教育"],
        ["小学", "中学", "大学"],
        ["本科", "硕士", "博士"],
        ["基础教育", "高等教育", "职业教育"],
        ["全日制", "非全日制", "网络教育"],
        ["人文类", "理工类", "商科类"],
        ["重点校", "普通校", "特色校"],
        ["城市学校", "农村学校", "边远学校"],
        ["示范学校", "一般学校", "薄弱学校"],
        # 增加新的选项 - 添加更多多样性
        ["普高", "职高", "技校"],
        ["985院校", "211院校", "普通院校"],
        ["双一流", "省属重点", "市属一般"],
        ["国家示范", "省级示范", "一般院校"],
        ["传统名校", "新建学校", "合并学校"],
        ["综合性大学", "专业学院", "职业学校"],
        ["公办", "民办", "中外合作"],
        ["统招生", "自主招生", "留学生"],
        ["寄宿制", "走读制", "混合制"],
        ["特殊教育", "普通教育", "尖子教育"],
        ["实验班", "普通班", "特长班"],
        ["精英教育", "大众教育", "通识教育"],
        ["研究型", "教学型", "应用型"],
        ["传统教学", "实验教学", "混合教学"],
        ["学院制", "书院制", "导师制"],
        ["普通教学", "远程教学", "互动教学"],
        ["理论教学", "实践教学", "社会实践"],
        ["自主学习", "课堂教学", "小组协作"],
        ["单科型", "多科型", "综合型"],
        ["行业特色", "地方特色", "国际特色"],
        ["实体校区", "网络校区", "混合校区"],
        ["严进严出", "宽进严出", "宽进宽出"],
        ["就业导向", "升学导向", "能力导向"],
        ["理论教育", "创新教育", "实践教育"],
        ["学术引领", "技能培养", "素质提升"],
        ["单科方向", "多科方向", "交叉学科"],
        ["分流制", "直通制", "分段制"],
        ["语言类", "工程类", "管理类"],
        ["医学类", "师范类", "农林类"],
        ["公共课", "专业课", "选修课"]
    ]
    
    # 评估维度选项
    dimension_options = [
        ["学习成绩", "综合素质", "课外活动"],
        ["知识掌握", "能力培养", "素质提升"],
        ["基础知识", "应用能力", "创新思维"],
        ["智育", "德育", "体育"],
        ["理论学习", "实践能力", "综合表现"],
        ["课内表现", "课外表现", "社会实践"],
        ["学科能力", "研究能力", "创新能力"],
        ["优秀", "良好", "一般"],
        ["90分以上", "80-90分", "80分以下"],
        ["A等级", "B等级", "C等级"],
        ["优", "良", "及格"],
        ["特长生", "普通生", "待提高"],
        # 增加新的选项 - 添加更多多样性
        ["专业技能", "综合素养", "研究能力"],
        ["理论水平", "实践操作", "创新突破"],
        ["自主学习", "团队协作", "问题解决"],
        ["专业知识", "通用能力", "思维方式"],
        ["认知理解", "应用实践", "分析评价"],
        ["记忆力", "理解力", "创造力"],
        ["表达能力", "思辨能力", "操作能力"],
        ["学科竞赛", "社会实践", "创新项目"],
        ["知识广度", "专业深度", "思维高度"],
        ["核心素养", "关键能力", "创新精神"],
        ["标准化测试", "过程性评价", "综合评定"],
        ["基础", "进阶", "精通"],
        ["学术型", "应用型", "复合型"],
        ["知识型", "能力型", "创新型"],
        ["学科基础", "专业方向", "拓展提高"],
        ["学习态度", "学习方法", "学习效果"],
        ["课内成绩", "实践表现", "综合评价"],
        ["专业认可", "行业认可", "社会认可"],
        ["考试成绩", "实验成绩", "项目成绩"],
        ["课程完成度", "技能掌握度", "创新活跃度"],
        ["理论评价", "实践评价", "综合评价"],
        ["学术研究", "技术应用", "创新创业"],
        ["基础课程", "专业课程", "实践环节"],
        ["第一课堂", "第二课堂", "第三课堂"],
        ["知识传授", "能力培养", "素质教育"],
        ["知识结构", "能力体系", "价值观念"],
        ["学业成就", "能力素质", "人格发展"],
        ["专业学习", "通识教育", "个性培养"],
        ["认知发展", "技能培养", "态度养成"],
        ["理性思考", "团队合作", "社会责任"]
    ]
    
    # 随机选择
    categories = random.choice(category_options)
    dimensions = random.choice(dimension_options)
    
    # 第一行：主分类
    for category in categories:
        html += f'<th colspan="{len(dimensions)}" style="{cell_style}">{category}</th>'
    html += '</tr>'
    
    # 第二行：评估维度
    html += '<tr>'
    for _ in range(len(categories)):  # 对每个主分类
        for dimension in dimensions:
            html += f'<th style="{cell_style}">{dimension}</th>'
    html += '</tr></thead>'
    
    # 数据行
    html += '<tbody>'
    current_first_level = None
    
    # 确保行层级非空
    if not row_level_values or len(row_level_values) < 2:
        # 学生分组选项
        student_options = [
            ["一年级", "二年级", "三年级", "四年级"],
            ["初一", "初二", "初三", "高一"],
            ["大一", "大二", "大三", "大四"],
            ["低年级", "中年级", "高年级", "毕业年级"],
            ["普通班", "重点班", "实验班", "特长班"],
            ["理科班", "文科班", "综合班", "实验班"],
            ["A班", "B班", "C班", "D班"],
            ["实验组", "对照组", "特殊组", "常规组"],
            ["全科生", "文科生", "理科生", "艺术生"],
            ["寄宿生", "走读生", "借读生", "交换生"],
            # 增加新的选项 - 添加更多多样性
            ["1班", "2班", "3班", "4班"],
            ["本科生", "研究生", "博士生", "交换生"],
            ["师范生", "非师范生", "定向生", "委培生"],
            ["普高生", "艺体生", "国际生", "特长生"],
            ["预科班", "本科班", "硕士班", "博士班"],
            ["经济类", "管理类", "工程类", "医学类"],
            ["自费生", "公费生", "奖学金生", "助学金生"],
            ["工科男", "理科男", "文科女", "艺术生"],
            ["优等生", "中等生", "学困生", "特殊生"],
            ["一本线上", "二本线上", "专科线上", "本科线下"],
            ["保研生", "考研生", "就业生", "创业生"],
            ["早班学生", "晚班学生", "业余学生", "网络学生"],
            ["城市学生", "农村学生", "偏远地区", "特殊地区"],
            ["班干部", "普通成员", "边缘学生", "特殊学生"],
            ["一线城市", "二线城市", "三线城市", "其他地区"],
            ["学习优秀", "中等水平", "学习困难", "特殊才能"],
            ["高考状元", "省市前十", "校级优秀", "班级优秀"],
            ["主修专业", "辅修专业", "双学位", "联合培养"],
            ["学术型学生", "应用型学生", "复合型学生", "创新型学生"],
            ["国内生源", "国际生源", "港澳台生", "华侨学生"]
        ]
        
        # 科目/特征选项
        subject_options = [
            ["语文", "数学", "英语", "科学"],
            ["文学", "理学", "工学", "医学"],
            ["必修课", "选修课", "实践课", "研究课"],
            ["基础科目", "专业科目", "选修科目", "实践科目"],
            ["学术科目", "艺术科目", "体育科目", "技能科目"],
            ["主科", "副科", "辅修", "特长"],
            ["理论课", "实验课", "设计课", "实践课"],
            ["基础能力", "专业能力", "综合能力", "创新能力"],
            ["学科知识", "实践技能", "综合素养", "创新思维"],
            ["课内知识", "课外拓展", "能力培养", "素质教育"],
            # 增加新的选项 - 添加更多多样性
            ["计算机科学", "电子工程", "机械工程", "土木工程"],
            ["经济学", "管理学", "法学", "政治学"],
            ["医学基础", "临床医学", "公共卫生", "口腔医学"],
            ["中国文学", "外国文学", "语言学", "文化研究"],
            ["高等数学", "线性代数", "概率统计", "离散数学"],
            ["有机化学", "无机化学", "分析化学", "物理化学"],
            ["理论物理", "应用物理", "实验物理", "天体物理"],
            ["微观经济", "宏观经济", "财政学", "金融学"],
            ["马克思主义", "中国特色社会主义", "思想政治", "道德法治"],
            ["市场营销", "人力资源", "财务管理", "战略管理"],
            ["软件工程", "人工智能", "大数据", "云计算"],
            ["生物科学", "生态学", "遗传学", "分子生物学"],
            ["中国哲学", "西方哲学", "伦理学", "美学"],
            ["世界历史", "中国历史", "考古学", "人类学"],
            ["教育学原理", "教育心理学", "课程教学论", "教育管理"],
            ["体育理论", "体育训练", "竞技体育", "休闲体育"],
            ["音乐理论", "声乐", "器乐", "作曲"],
            ["美术理论", "绘画", "雕塑", "设计"],
            ["英语听力", "英语口语", "英语阅读", "英语写作"],
            ["新闻学", "传播学", "广告学", "公共关系"],
            ["国际贸易", "国际金融", "国际政治", "国际法"],
            ["农学", "林学", "畜牧学", "水产学"],
            ["地理学", "地质学", "大气科学", "海洋科学"],
            ["军事理论", "军事技能", "国防教育", "安全防护"],
            ["创新创业", "职业规划", "就业指导", "社会实践"],
            ["心理学原理", "认知心理学", "发展心理学", "应用心理学"],
            ["研究方法", "学术规范", "科研写作", "学术报告"],
            ["项目管理", "质量管理", "风险管理", "创新管理"],
            ["环境科学", "环境工程", "生态保护", "可持续发展"],
            ["前沿讲座", "学科前沿", "交叉学科", "综合研讨"]
        ]
        
        # 随机选择
        students = random.choice(student_options)
        subjects = random.choice(subject_options)
        
        # 创建行数据
        row_data = []
        for student in students:
            for subject in subjects:
                row_data.append({
                    "first_level": student,
                    "second_level": subject
                })
    else:
        # 从行层级数据生成行
        row_data = []
        for i in range(len(row_level_values[0])):
            for j in range(len(row_level_values[1])):
                row_data.append({
                    "first_level": row_level_values[0][i],
                    "second_level": row_level_values[1][j]
                })
    
    # 生成行数据
    for row in row_data:
        html += '<tr>'
        
        # 第一级标题（可能需要合并单元格）
        if row["first_level"] != current_first_level:
            # 计算当前第一级有多少行
            same_first_level_count = sum(1 for r in row_data if r["first_level"] == row["first_level"])
            html += f'<td rowspan="{same_first_level_count}" style="{cell_style}">{row["first_level"]}</td>'
            current_first_level = row["first_level"]
        
        # 第二级标题
        html += f'<td style="{cell_style}">{row["second_level"]}</td>'
        
        # 生成单元格数据
        total_cols = len(categories) * len(dimensions)
        for _ in range(total_cols):
            cell_value = generate_cell_value(data_type, min_value, max_value,
                                          empty_probability, special_chars_probability,
                                          special_chars, cell_rules, is_numeric_likely=True)
            html += f'<td style="{cell_style}">{cell_value}</td>'
        
        html += '</tr>'
    
    return html

def generate_cell_value(data_type, min_value, max_value, empty_probability, 
                       special_chars_probability, special_chars, cell_rules, 
                       is_numeric_likely=False):
    """生成单元格数据"""
    # 如果是工具表，增加生成数值的概率
    if is_numeric_likely:
        empty_probability *= 0.6  # 减少空值概率
        special_chars_probability *= 0.4  # 减少特殊字符概率
        data_type = "numeric"  # 强制使用数值类型
    
    # 随机生成空值
    if random.random() < empty_probability:
        return ""
    
    # 随机生成特殊字符
    if random.random() < special_chars_probability and special_chars:
        return random.choice(special_chars)
    
    # 根据数据类型生成值
    if data_type == "numeric":
        value = random.uniform(min_value, max_value)
        format_str = cell_rules.get("numeric_format", "{:.2f}")  # 默认为2位小数
        
        try:
            # 如果是整数值，根据上下文决定是否显示为整数
            if value == int(value):
                # 某些指标（如比率、百分比）始终显示小数
                if "率" in str(cell_rules) or "比例" in str(cell_rules) or "%" in format_str:
                    return f"{value:.2f}"
                if "价格" in str(cell_rules) or "单价" in str(cell_rules) or "金额" in str(cell_rules):
                    return f"{value:.2f}"
                # 其他情况可以显示为整数
                return str(int(value))
            
            # 非整数值总是显示两位小数
            formatted_value = f"{value:.2f}"
            
            # 如果格式中包含单位或特殊格式，应用它
            if "%" in format_str:
                formatted_value += "%"
            elif "万" in format_str:
                formatted_value += "万"
            elif "元" in format_str:
                formatted_value += "元"
            
            return formatted_value
        except:
            # 默认返回2位小数
            return f"{value:.2f}"
    else:
        # 生成随机文本数据
        if random.random() < 0.5:
            # 数字
            return str(random.randint(int(min_value), int(max_value)))
        else:
            # 字母+数字
            return chr(random.randint(65, 90)) + str(random.randint(1, 99))

def generate_batch_cross_tables(count=100, base_dir=OUTPUT_FOLDER):
    """批量生成交叉表"""
    # 确保输出目录存在
    if not os.path.exists(base_dir):
        os.makedirs(base_dir)
    
    try:
        # 加载table_config.json
        print("正在加载table_config.json...")
        table_config = load_table_config()
        if not table_config:
            print("错误: 无法加载table_config.json，无法继续生成交叉表")
            return
        
        print(f"成功加载table_config.json，包含{len(table_config.get('themes', {}))}个主题")
        
        # 提取分类和度量变量
        print("正在提取分类和度量变量...")
        categories = extract_categories_from_config(table_config)
        metrics = extract_metrics_from_config(table_config)
        
        print(f"提取完成，找到{len(categories)}个分类变量和{len(metrics)}个度量变量")
        
        if not categories:
            print("错误: 无法从table_config.json中提取足够的分类变量")
            return
    except Exception as e:
        print(f"在准备阶段发生错误: {e}")
        import traceback
        traceback.print_exc()
        return
    
    try:
        # 生成动态交叉表配置
        print("正在生成交叉表配置...")
        base_configs = []
        target_base_configs = max(count // 50, 5)  # 生成足够多的基础配置，但至少5个
        
        # 获取所有可用的主题
        all_themes = set()
        for key, data in categories.items():
            all_themes.add(data["theme"])
        
        # 确保每个主题都至少有一个配置
        themes_to_generate = list(all_themes)
        random.shuffle(themes_to_generate)  # 随机打乱主题顺序
        
        # 首先为每个主题生成至少一个配置
        for theme in themes_to_generate:
            # 获取当前主题的分类变量
            theme_categories = [data for key, data in categories.items() if data["theme"] == theme]
            if len(theme_categories) >= 2:  # 需要至少两个分类变量来创建行列结构
                base_config = generate_dynamic_cross_table_config_for_theme(theme, theme_categories, metrics)
                if base_config["cross_tables"]:
                    base_configs.append(base_config)
                    print(f"成功为主题 '{theme}' 生成基础配置，包含{len(base_config['cross_tables'])}个交叉表")
        
        # 如果配置数量不足，再随机生成其他配置
        while len(base_configs) < target_base_configs:
            theme = random.choice(list(all_themes))
            theme_categories = [data for key, data in categories.items() if data["theme"] == theme]
            if len(theme_categories) >= 2:
                base_config = generate_dynamic_cross_table_config_for_theme(theme, theme_categories, metrics)
                if base_config["cross_tables"]:
                    base_configs.append(base_config)
                    print(f"成功为主题 '{theme}' 生成额外配置，包含{len(base_config['cross_tables'])}个交叉表")
        
        # 创建变种配置
        print("正在创建变种配置...")
        all_configs = []
        for i, base_config in enumerate(base_configs):
            all_configs.append(base_config)
            try:
                variations = create_variations(base_config)
                all_configs.append(variations)
                print(f"成功为第{i+1}个基础配置创建变种，增加{len(variations['cross_tables'])}个变种")
            except Exception as e:
                print(f"创建第{i+1}个基础配置的变种时出错: {e}")
        
        # 合并所有配置
        combined_config = {"cross_tables": {}}
        for config in all_configs:
            combined_config["cross_tables"].update(config["cross_tables"])
        
        print(f"合并配置完成，总共有{len(combined_config['cross_tables'])}种交叉表")
        
        # 保存生成的配置
        timestamp = int(time.time())
        config_file = os.path.join(base_dir, f"generated_cross_tables_{timestamp}.json")
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(combined_config, f, ensure_ascii=False, indent=2)
        
        print(f"配置已保存到{config_file}")
        print(f"已生成 {len(combined_config['cross_tables'])} 种交叉表配置")
    except Exception as e:
        print(f"在生成配置阶段发生错误: {e}")
        import traceback
        traceback.print_exc()
        return
    
    try:
        # 批量生成图片
        print("开始生成交叉表图片...")
        success_count = 0
        start_time = time.time()
        
        # 统计每个主题生成的表格数量
        theme_counts = {}
        
        # 确保平均分配主题
        table_keys = list(combined_config["cross_tables"].keys())
        random.shuffle(table_keys)  # 随机打乱顺序
        
        # 根据主题对表格键进行分组
        theme_grouped_keys = {}
        for table_key in table_keys:
            config = combined_config["cross_tables"][table_key]
            title = config.get("table_title_template", "")
            theme = identify_table_theme(title)
            
            if theme not in theme_grouped_keys:
                theme_grouped_keys[theme] = []
            theme_grouped_keys[theme].append(table_key)
        
        # 计算每个主题应该生成的表格数量
        themes_count = len(theme_grouped_keys)
        tables_per_theme = max(2, count // themes_count)  # 确保每个主题至少生成2张表
        
        # 轮流从各个主题中选择表格生成，确保主题多样性
        i = 0
        selected_keys = []
        
        # 首先，确保每个主题都有最低配额的表格
        for theme, keys in theme_grouped_keys.items():
            selected_keys.extend(keys[:min(tables_per_theme, len(keys))])
        
        # 如果还需要更多表格，随机添加
        if len(selected_keys) < count:
            remaining_count = count - len(selected_keys)
            remaining_keys = [k for k in table_keys if k not in selected_keys]
            random.shuffle(remaining_keys)
            selected_keys.extend(remaining_keys[:remaining_count])
        
        # 仅使用前count个表格键
        selected_keys = selected_keys[:count]
        random.shuffle(selected_keys)  # 再次打乱顺序，避免生成的表格按主题聚集
        
        # 生成表格
        for i, table_key in enumerate(selected_keys):
            table_config = combined_config["cross_tables"][table_key]
            title = table_config.get("table_title_template", "")
            theme = identify_table_theme(title)
            
            # 创建文件名和路径
            file_name = f"交叉表_{i+1}_{theme}.jpg"
            output_path = os.path.join(base_dir, file_name)
            
            try:
                # 生成交叉表图片
                success = create_random_cross_table_image(table_config, output_path)
                
                if success:
                    success_count += 1
                    theme_counts[theme] = theme_counts.get(theme, 0) + 1
                    if success_count % 10 == 0 or success_count == 1:
                        elapsed = time.time() - start_time
                        print(f"已生成 {success_count} 张交叉表，耗时 {elapsed:.2f} 秒")
            except Exception as e:
                print(f"生成第{i+1}张图片时出错: {e}")
        
        # 打印每个主题生成的表格数量统计
        print("\n主题分布统计:")
        for theme, count in theme_counts.items():
            print(f"  - {theme}: {count}张表格")
        
        total_time = time.time() - start_time
        print(f"批量生成完成，共生成 {success_count} 张交叉表，总耗时 {total_time:.2f} 秒")
        return success_count
    except Exception as e:
        print(f"在生成图片阶段发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 0

def generate_dynamic_cross_table_config_for_theme(theme, theme_categories, metrics):
    """为特定主题生成交叉表配置"""
    cross_tables = {}
    
    # 随机决定行列维度数量
    rows_count = random.randint(1, min(MAX_ROWS_LEVELS, len(theme_categories)-1))
    cols_count = min(MAX_COLS_LEVELS, len(theme_categories)-rows_count)
    
    # 确保至少有1行1列
    if rows_count < 1 or cols_count < 1:
        return {"cross_tables": {}}
        
    # 随机选择行和列维度
    random.shuffle(theme_categories)
    row_categories = theme_categories[:rows_count]
    col_categories = theme_categories[rows_count:rows_count+cols_count]
    
    # 随机选择度量变量（单元格数据）
    theme_metrics = [m for m in metrics.values() if m["theme"] == theme]
    if not theme_metrics:
        # 如果当前主题没有度量变量，从所有度量变量中随机选择
        theme_metrics = random.sample(list(metrics.values()), min(3, len(metrics)))
    
    metric = random.choice(theme_metrics) if theme_metrics else None
    
    # 生成交叉表配置
    table_key = f"{theme}_交叉表_{random.randint(1000, 9999)}"
    
    # 构造表格标题，确保能够识别主题
    title_template = f"{theme}交叉分析表"
    
    # 行层次结构 - 增加随机性
    row_hierarchy = []
    for cat in row_categories:
        values = cat["values"].copy()  # 使用副本避免修改原始数据
        # 随机决定是否对值进行排序或随机抽样
        if random.random() < 0.3:  # 30%概率排序
            if all(isinstance(v, str) for v in values):
                values = sorted(values)
            
        # 随机决定是否只保留部分值
        if len(values) > 3 and random.random() < 0.5:  # 50%概率抽样
            sample_size = random.randint(2, min(len(values), MAX_ROWS_VALUES))
            values = random.sample(values, sample_size)
        
        row_hierarchy.append({
            "name": cat["name"],
            "values": values,
            "display_name": cat["display_name"]
        })
    
    # 列层次结构 - 增加随机性
    column_hierarchy = []
    for cat in col_categories:
        values = cat["values"].copy()  # 使用副本避免修改原始数据
        
        # 随机决定是否对值进行排序或随机抽样
        if random.random() < 0.3:  # 30%概率排序
            if all(isinstance(v, str) for v in values):
                values = sorted(values)
        
        # 随机决定是否只保留部分值或添加repeat属性
        if len(values) > 2 and random.random() < 0.5:  # 50%概率抽样或重复
            if random.random() < 0.7:  # 70%概率抽样
                sample_size = random.randint(2, min(len(values), MAX_COLS_VALUES))
                values = random.sample(values, sample_size)
            else:  # 30%概率添加repeat属性
                repeat = random.randint(2, 3)
                column_hierarchy.append({
                    "name": cat["name"],
                    "values": values[:2],  # 限制为前2个值
                    "display_name": cat["display_name"],
                    "repeat": repeat
                })
                continue
        
        column_hierarchy.append({
            "name": cat["name"],
            "values": values,
            "display_name": cat["display_name"]
        })
    
    # 检查并调整列数量，确保不超过最大限制
    column_hierarchy, total_cols = check_and_adjust_columns(column_hierarchy)
    
    # 随机调整单元格数据规则
    empty_prob = random.uniform(0.1, 0.4)
    special_chars_prob = random.uniform(0.05, 0.25)
    
    # 根据度量变量随机选择数据类型
    if metric and random.random() < 0.7:  # 70%概率使用数值型
        data_type = "numeric"
        min_val = metric["min_value"]
        max_val = metric["max_value"]
        format_string = metric["format_string"]
    else:
        data_type = "text"
        min_val = 1
        max_val = 100
        format_string = "{:d}"
    
    # Determine available supplementary names
    used_display_names = set()
    for cat_item in row_categories:
        used_display_names.add(cat_item["display_name"])
    for cat_item in col_categories:
        used_display_names.add(cat_item["display_name"])

    # theme_categories is the input list of all category dicts for this theme
    all_possible_theme_cat_display_names = [cat["display_name"] for cat in theme_categories]
    available_sup_names = [name for name in all_possible_theme_cat_display_names if name not in used_display_names]
    random.shuffle(available_sup_names)

    cross_tables[table_key] = {
        "display_name": f"{theme}交叉表",
        "table_title_template": title_template,
        "row_hierarchy": row_hierarchy,
        "column_hierarchy": column_hierarchy,
        "total_columns": total_cols,  # 记录总列数
        "cell_data_rules": {
            "data_type": data_type,
            "empty_probability": empty_prob,
            "special_chars_probability": special_chars_prob,
            "special_chars": ["a", "b", "c", "d", "e", "f", "g", "h", "i", "N", "M", "L", "K"],
            "min_value": min_val,
            "max_value": max_val,
            "numeric_format": format_string
        },
        "style_settings": {
            "header_background_color": random.choice([
                "#f5f5f5", "#e6f3ff", "#f0f8ff", "#e9f7ef", "#fdebd0", 
                "#ebdef0", "#d6eaf8", "#fae5d3", "#f2f3f4", "#eaeded"
            ]),
            "header_font_weight": "bold",
            "data_font_size": 10,
            "cell_padding": 4,
            "border_style": "1px solid black",
            "text_align": "center"
        },
        "available_sup_names": available_sup_names # Store the available names
    }
    
    return {"cross_tables": cross_tables}

def create_multiple_cross_tables(count=1):
    """创建多个交叉表"""
    # 确保输出目录存在
    if not os.path.exists(OUTPUT_FOLDER):
        os.makedirs(OUTPUT_FOLDER)
    
    # 加载交叉表配置
    cross_config = load_cross_table_config()
    
    # 检查是否需要从table_config动态生成配置
    if not cross_config["cross_tables"]:
        # 加载table_config
        table_config = load_table_config()
        if table_config:
            # 提取分类和度量变量
            categories = extract_categories_from_config(table_config)
            metrics = extract_metrics_from_config(table_config)
            
            # 生成动态交叉表配置
            cross_config = generate_dynamic_cross_table_config(categories, metrics)
    
    # 确保有交叉表配置
    if not cross_config["cross_tables"]:
        print("无法生成交叉表，缺少配置")
        return
    
    # 生成指定数量的交叉表
    success_count = 0
    cross_table_types = list(cross_config["cross_tables"].keys())
    
    for i in range(count):
        selected_type = random.choice(cross_table_types)
        cross_table_config = cross_config["cross_tables"][selected_type]
        
        # 创建文件名和路径
        file_name = f"交叉表_{i+1}_{selected_type.split('_')[0]}.jpg"
        output_path = os.path.join(OUTPUT_FOLDER, file_name)
        
        # 生成交叉表图片
        success = create_random_cross_table_image(cross_table_config, output_path)
        
        if success:
            success_count += 1
    
    print(f"成功生成 {success_count} 张交叉表图片")
    return success_count

def generate_real_estate_table_html(title_text, row_levels, row_level_values, cell_style, 
                                 header_bg_color, data_type, min_value, max_value, 
                                 empty_probability, special_chars_probability, 
                                 special_chars, cell_rules):
    """生成房地产交叉表的HTML"""
    # 随机决定是否显示小计列
    show_subtotal = random.random() < 0.7
    
    # 区域分类选项
    area_categories = [
        ["市中心", "城市郊区", "新兴区域", "旧城区"],
        ["一线城市", "二线城市", "三线城市", "四线城市"],
        ["北区", "南区", "东区", "西区"],
        ["核心商圈", "居住区", "工业区", "混合区"],
        ["内环", "中环", "外环", "远郊"],
        ["主城区", "开发区", "高新区", "经济区"],
        ["老城区", "新城区", "卫星城", "城乡结合部"],
        ["传统商圈", "新兴商圈", "高档社区", "普通社区"],
        ["城中村", "棚户区", "保障房区", "别墅区"],
        ["热点区域", "潜力区域", "饱和区域", "萧条区域"]
    ]
    
    # 物业类型选项
    property_types = [
        ["住宅", "商铺", "写字楼", "工业厂房"],
        ["普通住宅", "别墅", "公寓", "洋房"],
        ["商品房", "保障房", "租赁房", "二手房"],
        ["小高层", "高层", "多层", "低层"],
        ["住宅物业", "商业物业", "办公物业", "产业物业"],
        ["精装修", "毛坯", "简装", "豪装"],
        ["大户型", "中户型", "小户型", "复式"],
        ["公寓式住宅", "别墅式住宅", "联排住宅", "独栋住宅"],
        ["标准住宅", "非标准住宅", "商住两用", "LOFT"],
        ["一居室", "二居室", "三居室", "四居及以上"]
    ]
    
    # 时间划分选项
    time_periods = [
        ["2020年前", "2020-2021年", "2022-2023年", "2024年后"],
        ["10年以上", "5-10年", "3-5年", "3年以内"],
        ["一季度", "二季度", "三季度", "四季度"],
        ["2020年", "2021年", "2022年", "2023年"],
        ["上半年", "下半年", "年初", "年末"],
        ["2000年前", "2000-2010年", "2010-2020年", "2020年后"],
        ["疫情前", "疫情期间", "后疫情时代", "新常态"],
        ["房地产黄金期", "调控期", "稳定期", "转型期"],
        ["限购前", "限购中", "限购后", "政策松绑期"],
        ["市场低谷", "复苏期", "高峰期", "回调期"]
    ]
    
    # 指标类型选项
    metrics = [
        ["成交量", "成交额", "价格", "去化率"],
        ["单价", "总价", "涨幅", "跌幅"],
        ["新增供应", "新增需求", "存量", "消化周期"],
        ["空置率", "出租率", "回报率", "利润率"],
        ["投资回报率", "使用年限", "满五唯一", "带押过户"],
        ["网签量", "预售许可", "实际交付", "入住率"],
        ["均价", "中位数价格", "最高价", "最低价"],
        ["房贷利率", "首付比例", "贷款年限", "月供金额"],
        ["土地出让金", "税费", "中介费", "物业费"],
        ["地价", "楼面价", "建安成本", "开发投入"]
    ]
    
    # 随机选择分类
    selected_area = random.choice(area_categories)
    selected_property_types = random.choice(property_types)
    selected_time_periods = random.choice(time_periods)
    selected_metrics = random.choice(metrics)
    
    # 开始构建HTML表格
    html = '<thead><tr>'
    html += f'<th colspan="2" rowspan="2" style="{cell_style}">{title_text}</th>'
    
    # 随机决定行列布局
    layout_type = random.randint(1, 3)
    
    if layout_type == 1:
        # 区域作为列，物业类型作为子列
        for area in selected_area:
            html += f'<th colspan="{len(selected_property_types)}" style="{cell_style}">{area}</th>'
        html += '</tr><tr>'
        
        # 物业类型行
        for _ in range(len(selected_area)):
            for prop_type in selected_property_types:
                html += f'<th style="{cell_style}">{prop_type}</th>'
        
        # 时间和指标作为行
        row_items = []
        for time in selected_time_periods:
            for metric in selected_metrics:
                row_items.append({"time": time, "metric": metric})
    
    elif layout_type == 2:
        # 时间作为列，物业类型作为子列
        for time in selected_time_periods:
            html += f'<th colspan="{len(selected_property_types)}" style="{cell_style}">{time}</th>'
        html += '</tr><tr>'
        
        # 物业类型行
        for _ in range(len(selected_time_periods)):
            for prop_type in selected_property_types:
                html += f'<th style="{cell_style}">{prop_type}</th>'
        
        # 区域和指标作为行
        row_items = []
        for area in selected_area:
            for metric in selected_metrics:
                row_items.append({"area": area, "metric": metric})
    
    else:
        # 物业类型作为列，指标作为子列
        for prop_type in selected_property_types:
            html += f'<th colspan="{len(selected_metrics)}" style="{cell_style}">{prop_type}</th>'
        html += '</tr><tr>'
        
        # 指标行
        for _ in range(len(selected_property_types)):
            for metric in selected_metrics:
                html += f'<th style="{cell_style}">{metric}</th>'
        
        # 区域和时间作为行
        row_items = []
        for area in selected_area:
            for time in selected_time_periods:
                row_items.append({"area": area, "time": time})
    
    html += '</tr></thead>'
    
    # 数据行
    html += '<tbody>'
    
    # 使用行项目生成数据行
    current_first_level = None
    for i, row_item in enumerate(row_items):
        html += '<tr>'
        
        # 第一列
        if layout_type == 1:
            if i % len(selected_metrics) == 0:
                # 合并时间行
                html += f'<td rowspan="{len(selected_metrics)}" style="{cell_style}">{row_item["time"]}</td>'
            html += f'<td style="{cell_style}">{row_item["metric"]}</td>'
        elif layout_type == 2:
            if i % len(selected_metrics) == 0:
                # 合并区域行
                html += f'<td rowspan="{len(selected_metrics)}" style="{cell_style}">{row_item["area"]}</td>'
            html += f'<td style="{cell_style}">{row_item["metric"]}</td>'
        else:
            if i % len(selected_time_periods) == 0:
                # 合并区域行
                html += f'<td rowspan="{len(selected_time_periods)}" style="{cell_style}">{row_item["area"]}</td>'
            html += f'<td style="{cell_style}">{row_item["time"]}</td>'
        
        # 数据单元格
        cols = len(selected_area) * len(selected_property_types) if layout_type == 1 else (
            len(selected_time_periods) * len(selected_property_types) if layout_type == 2 else
            len(selected_property_types) * len(selected_metrics)
        )
        
        # 生成单元格数据
        for _ in range(cols):
            # 针对房地产的数值特征
            if "价格" in str(row_item) or "单价" in str(row_item) or "总价" in str(row_item):
                # 价格类数据通常较大且有小数点
                value = round(random.uniform(8000, 80000), 2)
                if random.random() < 0.7:  # 70%的概率显示为整数
                    value = int(value)
                cell_value = f"{value}"
                if random.random() < 0.5:  # 50%的概率添加单位
                    cell_value += "元/㎡" if "单价" in str(row_item) else "万元"
            elif "率" in str(row_item):
                # 比率类数据通常为百分比
                value = round(random.uniform(0, 100), 2)
                if random.random() < 0.7:  # 70%的概率四舍五入到一位小数
                    value = round(value, 1)
                cell_value = f"{value}%"
            elif "面积" in str(row_item):
                # 面积类数据
                value = round(random.uniform(30, 500), 2)
                if random.random() < 0.6:  # 60%的概率显示为整数
                    value = int(value)
                cell_value = f"{value}㎡"
            elif "量" in str(row_item) or "数" in str(row_item) or "套" in str(row_item):
                # 整数类数据
                value = random.randint(10, 10000)
                if value >= 1000:
                    value = round(value / 1000, 1)
                    cell_value = f"{value}千" if value < 10 else f"{int(value)}千"
                else:
                    cell_value = str(value)
            else:
                # 其他数据使用通用生成
                cell_value = generate_cell_value(data_type, min_value, max_value, 
                                            empty_probability, special_chars_probability, 
                                            special_chars, cell_rules)
            
            html += f'<td style="{cell_style}">{cell_value}</td>'
        
        html += '</tr>'
    
    html += '</tbody>'
    return html

def generate_tourism_table_html(title_text, row_levels, row_level_values, cell_style, 
                              header_bg_color, data_type, min_value, max_value, 
                              empty_probability, special_chars_probability, 
                              special_chars, cell_rules):
    """生成旅游交叉表的HTML"""
    # 旅游景点类型选项
    attraction_types = [
        ["自然景观", "人文景观", "主题公园", "休闲度假"],
        ["山岳景区", "湖泊景区", "海滨景区", "森林景区"],
        ["5A景区", "4A景区", "3A景区", "非A级景区"],
        ["国家级景区", "省级景区", "市级景区", "县级景区"],
        ["文化遗产", "自然遗产", "世界遗产", "非遗景点"]
    ]
    
    # 游客类型选项
    tourist_types = [
        ["国内游客", "国际游客", "港澳台游客", "跨境游客"],
        ["个人游", "团队游", "亲子游", "老年游"],
        ["自由行", "跟团游", "定制游", "自驾游"],
        ["商务游客", "休闲游客", "学生群体", "家庭旅游"],
        ["高端游客", "中端游客", "经济型游客", "背包族"]
    ]
    
    # 季节时间选项
    seasons = [
        ["春季", "夏季", "秋季", "冬季"],
        ["旺季", "淡季", "平季", "节假日"],
        ["周末", "工作日", "小长假", "黄金周"],
        ["上午", "下午", "夜间", "全天"],
        ["早高峰", "午高峰", "晚高峰", "平峰期"]
    ]
    
    # 指标类型选项
    metrics = [
        ["游客量", "旅游收入", "人均消费", "停留时间"],
        ["入境人次", "出境人次", "国内人次", "接待总量"],
        ["景区门票", "餐饮消费", "住宿消费", "购物消费"],
        ["满意度", "重游率", "推荐率", "投诉率"],
        ["日均消费", "人均消费", "团队消费", "个人消费"]
    ]
    
    # 随机选择分类
    selected_attractions = random.choice(attraction_types)
    selected_tourists = random.choice(tourist_types)
    selected_seasons = random.choice(seasons)
    selected_metrics = random.choice(metrics)
    
    # 开始构建HTML表格
    html = '<thead><tr>'
    html += f'<th colspan="2" rowspan="2" style="{cell_style}">{title_text}</th>'
    
    # 随机决定行列布局
    layout_type = random.randint(1, 3)
    
    if layout_type == 1:
        # 景点作为列，游客类型作为子列
        for attraction in selected_attractions:
            html += f'<th colspan="{len(selected_tourists)}" style="{cell_style}">{attraction}</th>'
        html += '</tr><tr>'
        
        # 游客类型行
        for _ in range(len(selected_attractions)):
            for tourist in selected_tourists:
                html += f'<th style="{cell_style}">{tourist}</th>'
        
        # 季节和指标作为行
        row_items = []
        for season in selected_seasons:
            for metric in selected_metrics:
                row_items.append({"season": season, "metric": metric})
    
    elif layout_type == 2:
        # 季节作为列，游客类型作为子列
        for season in selected_seasons:
            html += f'<th colspan="{len(selected_tourists)}" style="{cell_style}">{season}</th>'
        html += '</tr><tr>'
        
        # 游客类型行
        for _ in range(len(selected_seasons)):
            for tourist in selected_tourists:
                html += f'<th style="{cell_style}">{tourist}</th>'
        
        # 景点和指标作为行
        row_items = []
        for attraction in selected_attractions:
            for metric in selected_metrics:
                row_items.append({"attraction": attraction, "metric": metric})
    
    else:
        # 游客类型作为列，指标作为子列
        for tourist in selected_tourists:
            html += f'<th colspan="{len(selected_metrics)}" style="{cell_style}">{tourist}</th>'
        html += '</tr><tr>'
        
        # 指标行
        for _ in range(len(selected_tourists)):
            for metric in selected_metrics:
                html += f'<th style="{cell_style}">{metric}</th>'
        
        # 景点和季节作为行
        row_items = []
        for attraction in selected_attractions:
            for season in selected_seasons:
                row_items.append({"attraction": attraction, "season": season})
    
    html += '</tr></thead>'
    
    # 数据行
    html += '<tbody>'
    
    # 使用行项目生成数据行
    current_first_level = None
    for i, row_item in enumerate(row_items):
        html += '<tr>'
        
        # 第一列
        if layout_type == 1:
            if i % len(selected_metrics) == 0:
                # 合并季节行
                html += f'<td rowspan="{len(selected_metrics)}" style="{cell_style}">{row_item["season"]}</td>'
            html += f'<td style="{cell_style}">{row_item["metric"]}</td>'
        elif layout_type == 2:
            if i % len(selected_metrics) == 0:
                # 合并景点行
                html += f'<td rowspan="{len(selected_metrics)}" style="{cell_style}">{row_item["attraction"]}</td>'
            html += f'<td style="{cell_style}">{row_item["metric"]}</td>'
        else:
            if i % len(selected_seasons) == 0:
                # 合并景点行
                html += f'<td rowspan="{len(selected_seasons)}" style="{cell_style}">{row_item["attraction"]}</td>'
            html += f'<td style="{cell_style}">{row_item["season"]}</td>'
        
        # 数据单元格
        cols = len(selected_attractions) * len(selected_tourists) if layout_type == 1 else (
            len(selected_seasons) * len(selected_tourists) if layout_type == 2 else
            len(selected_tourists) * len(selected_metrics)
        )
        
        # 生成单元格数据
        for _ in range(cols):
            # 针对旅游的数值特征
            if "人次" in str(row_item) or "游客量" in str(row_item) or "接待" in str(row_item):
                # 人次数据通常是整数且较大
                value = random.randint(1000, 100000)
                if value >= 10000:
                    value = round(value / 10000, 2)
                    if value == int(value):
                        value = int(value)
                    cell_value = f"{value}万"
                else:
                    cell_value = f"{value}"
            elif "收入" in str(row_item) or "消费" in str(row_item) or "价" in str(row_item):
                # 金额类数据
                if "总收入" in str(row_item):
                    value = round(random.uniform(1000, 10000), 2)
                    if value == int(value):
                        value = int(value)
                    cell_value = f"{value}万元"
                elif "人均" in str(row_item) or "日均" in str(row_item):
                    value = round(random.uniform(100, 2000), 0)
                    cell_value = f"{int(value)}元"
                else:
                    value = round(random.uniform(500, 5000), 0)
                    cell_value = f"{int(value)}元"
            elif "率" in str(row_item):
                # 比率类数据通常为百分比
                value = round(random.uniform(0, 100), 1)
                cell_value = f"{value}%"
            elif "时间" in str(row_item):
                # 时间类数据
                hours = random.randint(0, 12)
                minutes = random.randint(0, 59)
                if hours > 0:
                    if minutes > 0:
                        cell_value = f"{hours}时{minutes}分"
                    else:
                        cell_value = f"{hours}小时"
                else:
                    cell_value = f"{minutes}分钟"
            else:
                # 其他数据使用通用生成
                cell_value = generate_cell_value(data_type, min_value, max_value, 
                                             empty_probability, special_chars_probability, 
                                             special_chars, cell_rules)
            
            html += f'<td style="{cell_style}">{cell_value}</td>'
        
        html += '</tr>'
    
    html += '</tbody>'
    return html

# 为第二种图表类型添加一个新的生成函数
def generate_time_range_table_html(title_text, cell_style):
    """生成时间范围表格，针对第二张图所示的格式"""
    html = '<thead><tr>'
    
    # 第一行：基本标题
    html += f'<th rowspan="2" style="{cell_style}">入职日期</th>'
    html += f'<th rowspan="2" style="{cell_style}">上交资料日</th>'
    
    # 两个主要时间阶段
    html += f'<th colspan="3" style="{cell_style}">已婚</th>'
    html += f'<th colspan="3" style="{cell_style}">未婚</th>'
    html += '</tr><tr>'
    
    # 第二行：子标题
    entries = ["已婚", "未婚"]
    sub_entries = ["已缴", "未缴", "未婚"]
    
    for _ in range(2):  # 重复两个主要分类
        for sub in sub_entries:
            html += f'<th style="{cell_style}">{sub}</th>'
    
    html += '</tr></thead>'
    
    # 数据行
    html += '<tbody>'
    
    # 定义一些典型的时间范围
    time_ranges = [
        "2019-2020",
        "2020-2021",
        "2021-2022",
        "2022-2023"
    ]
    
    # 每个时间范围有2个子行
    for time_range in time_ranges:
        # 第一子行
        html += '<tr>'
        html += f'<td rowspan="2" style="{cell_style}">{time_range}</td>'
        html += f'<td style="{cell_style}">{time_range}</td>'
        
        # 生成数据单元格
        for _ in range(6):  # 6列数据
            # 使用财务数字格式，带千位分隔符
            value = random.randint(1000000, 50000000) / 100
            formatted_value = f"{value:,.2f}".replace(",", " ")
            html += f'<td style="{cell_style}">{formatted_value}</td>'
        
        html += '</tr>'
        
        # 第二子行
        html += '<tr>'
        html += f'<td style="{cell_style}">{int(time_range.split("-")[0])+1}-{int(time_range.split("-")[1])+1}</td>'
        
        # 生成数据单元格
        for _ in range(6):  # 6列数据
            if random.random() < 0.2:
                # 有时显示字母
                html += f'<td style="{cell_style}">{random.choice(["a", "b", "c", "d", "g", "h", "k", "N"])}</td>'
            else:
                # 大部分是数值
                value = random.randint(1000000, 50000000) / 100
                formatted_value = f"{value:,.2f}".replace(",", " ")
                html += f'<td style="{cell_style}">{formatted_value}</td>'
        
        html += '</tr>'
    
    html += '</tbody>'
    
    return html

def generate_finance_table_html(title_text, row_levels, row_level_values, cell_style,
                              data_type, min_value, max_value,
                              empty_probability, special_chars_probability,
                              special_chars, cell_rules):
    """生成金融行业交叉表的HTML，类似于第一张图"""
    html = '<thead><tr>'
    
    # 第一行标题
    html += f'<th rowspan="2" style="{cell_style}">行业</th>'
    html += f'<th rowspan="2" style="{cell_style}">经营范围</th>'
    
    # 年份标题
    years = ["2022年", "2023年", "2024年"]
    for year in years:
        html += f'<th colspan="3" style="{cell_style}">{year}</th>'
    html += '</tr><tr>'
    
    # 第二行子标题
    metrics = ["数量", "比例", "增长率"]
    for _ in range(len(years)):
        for metric in metrics:
            html += f'<th style="{cell_style}">{metric}</th>'
    html += '</tr></thead>'
    
    # 数据体
    html += '<tbody>'
    
    # 行业类型
    industries = [
        "教育培训", 
        "金融银行", 
        "互联网IT", 
        "医疗健康",
        "房地产",
        "制造业",
        "零售业",
        "物流",
        "餐饮",
        "旅游"
    ]
    
    # 经营范围
    business_scopes = {
        "教育培训": ["软件开发培训", "医疗器械培训", "金融投资培训"],
        "金融银行": ["软件开发运维", "医疗器械融资", "金融投资咨询"],
        "互联网IT": ["软件开发运维", "医疗器械系统", "金融投资平台"],
        "医疗健康": ["软件开发导入", "医疗器械研发", "金融投资规划"],
        "房地产": ["软件开发外包", "医疗器械销售", "金融投资分析"]
    }
    
    # 选择要展示的行业
    selected_industries = random.sample(industries, min(5, len(industries)))
    
    # 生成行数据
    for industry in selected_industries:
        # 获取该行业的经营范围，如果没有预设，则使用通用范围
        if industry in business_scopes:
            scopes = business_scopes[industry]
        else:
            scopes = ["软件开发", "医疗器械", "金融投资"]
        
        # 为每个经营范围创建一行
        for scope in scopes:
            html += '<tr>'
            
            # 如果是该行业的第一个范围，合并行业单元格
            if scope == scopes[0]:
                html += f'<td rowspan="{len(scopes)}" style="{cell_style}">{industry}</td>'
            
            # 经营范围单元格
            html += f'<td style="{cell_style}">{scope}</td>'
            
            # 生成数据单元格
            for i in range(len(years) * len(metrics)):
                col_index = i % len(metrics)
                
                # 根据不同的列类型生成不同格式的数据
                if col_index == 0:  # 数量列
                    # 生成具有一定规律的数量数据
                    base_value = random.uniform(1000, 90000)
                    growth = random.uniform(0.8, 1.2)  # 增长因子
                    year_index = i // len(metrics)
                    value = base_value * (growth ** year_index)
                    
                    # 50%概率显示整数
                    if random.random() < 0.5:
                        cell_value = f"{int(value):,}".replace(",", " ")
                    else:
                        cell_value = f"{value:.2f}".replace(",", " ")
                
                elif col_index == 1:  # 比例列
                    # 生成百分比数据
                    value = random.uniform(0, 100)
                    cell_value = f"{value:.2f}%"
                    
                    # 20%概率使用特殊字符
                    if random.random() < 0.2:
                        cell_value = random.choice(["a", "b", "c", "d", "e", "N", "M", "K"])
                
                else:  # 增长率列
                    # 生成正负增长率
                    value = random.uniform(-20, 50)
                    
                    # 20%概率使用特殊字符
                    if random.random() < 0.2:
                        cell_value = random.choice(["a", "b", "c", "d", "e", "N", "M", "K"])
                    # 10%概率显示空值
                    elif random.random() < 0.1:
                        cell_value = ""
                    else:
                        cell_value = f"{value:.2f}%"
                
                html += f'<td style="{cell_style}">{cell_value}</td>'
            
            html += '</tr>'
    
    html += '</tbody>'
    return html

if __name__ == '__main__':
    print("=" * 50)
    print("交叉表生成程序启动")
    print("=" * 50)
    # 生成少量交叉表测试
    print("开始生成交叉表测试...")
    result = generate_batch_cross_tables(100)
    print(f"测试完成，生成了{result}张交叉表")
    print("如果需要生成40000张交叉表，请取消注释并运行下一行")
    # generate_batch_cross_tables(40000)
