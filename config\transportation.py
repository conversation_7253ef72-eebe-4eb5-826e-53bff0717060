transportation = {
    "display_name": "交通工具",
    "table_title_template": "交通工具信息表",
    "columns": {
        "类型": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "轿车",
                    "SUV",
                    "MPV",
                    "跑车",
                    "皮卡",
                    "卡车",
                    "公交车",
                    "越野车",
                    "敞篷车",
                    "混合动力车"
                ]
            }
        },
        "品牌": {
            "generator_type": "faker_company",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "型号": {
            "generator_type": "alphanumeric_pattern",
            "data_category": "text",
            "params": {
                "patterns": [
                    "X###",
                    "XY-###",
                    "Model ##",
                    "Series-#"
                ]
            }
        },
        "生产商": {
            "generator_type": "faker_company",
            "data_category": "text",
            "params": {
                "locale": "zh_CN"
            }
        },
        "生产国": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "中国",
                    "美国",
                    "日本",
                    "德国",
                    "韩国",
                    "法国",
                    "英国",
                    "意大利",
                    "瑞典",
                    "西班牙"
                ]
            }
        },
        "上市时间": {
            "generator_type": "date_range",
            "data_category": "date",
            "params": {
                "start_year": 2000,
                "end_year": 2024,
                "format": "%Y-%m-%d"
            }
        },
        "价格区间": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 50000,
                "max_value": 1000000,
                "format_string": "{:,}"
            }
        },
        "座位数": {
            "generator_type": "integer_range",
            "data_category": "numeric",
            "params": {
                "min": 2,
                "max": 9
            }
        },
        "载重量": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 0.5,
                "max_value": 50.0,
                "decimals": 1,
                "format_string": "{:.1f}吨"
            }
        },
        "尺寸规格": {
            "generator_type": "dimension_format",
            "data_category": "text",
            "params": {
                "min_length": 3000,
                "max_length": 6000,
                "min_width": 1500,
                "max_width": 2200,
                "min_height": 1200,
                "max_height": 2000,
                "format_string": "{:d}×{:d}×{:d}mm"
            }
        },
        "动力类型": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "汽油",
                    "柴油",
                    "电动",
                    "混合动力",
                    "氢能源",
                    "天然气",
                    "生物燃料"
                ]
            }
        },
        "最高速度": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 100,
                "max_value": 350,
                "format_string": "{:d}km/h"
            }
        },
        "油耗": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 4.5,
                "max_value": 15.0,
                "decimals": 1,
                "format_string": "{:.1f}L/100km"
            }
        },
        "颜色": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "红色",
                    "蓝色",
                    "黑色",
                    "白色",
                    "灰色",
                    "银色",
                    "绿色",
                    "黄色",
                    "棕色",
                    "紫色"
                ]
            }
        },
        "发动机型号": {
            "generator_type": "alphanumeric_pattern",
            "data_category": "text",
            "params": {
                "patterns": [
                    "V##-TDI",
                    "L#-TSI",
                    "T##",
                    "EA###",
                    "F##"
                ]
            }
        },
        "车牌号": {
            "generator_type": "categorical_with_pattern",
            "data_category": "text",
            "params": {
                "prefixes": [
                    "京",
                    "沪",
                    "粤",
                    "津",
                    "冀",
                    "晋",
                    "蒙",
                    "辽",
                    "吉",
                    "黑"
                ],
                "suffixes": [
                    "A",
                    "B",
                    "C",
                    "D",
                    "E",
                    "F",
                    "G",
                    "H",
                    "J",
                    "K"
                ]
            }
        },
        "年检日期": {
            "generator_type": "date_range",
            "data_category": "date",
            "params": {
                "start_year": 2023,
                "end_year": 2025,
                "format": "%Y-%m-%d"
            }
        },
        "保险到期": {
            "generator_type": "date_range",
            "data_category": "date",
            "params": {
                "start_year": 2023,
                "end_year": 2025,
                "format": "%Y-%m-%d"
            }
        },
        "燃油效率": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 10,
                "max_value": 35,
                "decimals": 1,
                "format_string": "{:.1f}km/L"
            }
        },
        "排放标准": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "国六",
                    "国五",
                    "国四",
                    "欧六",
                    "欧五",
                    "T1",
                    "T2",
                    "T3",
                    "零排放"
                ]
            }
        },
        "变速箱": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "手动",
                    "自动",
                    "CVT",
                    "双离合",
                    "AMT",
                    "AT",
                    "DCT"
                ]
            }
        }
    },
    "text_columns_count": 8,
    "text_columns_names": [
        "类型",
        "品牌",
        "生产商",
        "生产国",
        "动力类型",
        "颜色",
        "变速箱",
        "排放标准"
    ],
    "numeric_columns_count": 10,
    "numeric_columns_names": [
        "型号",
        "价格区间",
        "座位数",
        "载重量",
        "尺寸规格",
        "最高速度",
        "油耗",
        "发动机型号",
        "车牌号",
        "燃油效率"
    ],
    "date_columns_count": 3,
    "date_columns_names": [
        "上市时间",
        "年检日期",
        "保险到期"
    ],
    "other_columns_count": 1,
    "other_columns_names": [
        "排放标准"
    ],
    "all_columns": [
        "类型",
        "品牌",
        "生产商",
        "生产国",
        "动力类型",
        "颜色",
        "变速箱",
        "型号",
        "价格区间",
        "座位数",
        "载重量",
        "尺寸规格",
        "最高速度",
        "油耗",
        "发动机型号",
        "车牌号",
        "燃油效率",
        "上市时间",
        "年检日期",
        "保险到期",
        "排放标准"
    ]
}

