{"common_data": {"cities": ["北京", "上海", "广州", "深圳", "成都", "武汉", "杭州", "重庆", "西安", "南京", "天津", "苏州", "青岛", "郑州", "长沙", "合肥", "无锡", "宁波", "福州", "厦门"], "major_cities": ["北京", "上海", "广州", "深圳", "成都", "重庆", "武汉", "杭州", "南京", "西安"]}, "themes": {"transportation": {"display_name": "交通工具", "table_title_template": "交通工具信息表", "columns": {"类型": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["轿车", "SUV", "MPV", "跑车", "皮卡", "卡车", "公交车", "越野车", "敞篷车", "混合动力车"]}}, "品牌": {"generator_type": "faker_company", "data_category": "text", "params": {"locale": "zh_CN"}}, "型号": {"generator_type": "alphanumeric_pattern", "data_category": "text", "params": {"patterns": ["X###", "XY-###", "Model ##", "Series-#"]}}, "生产商": {"generator_type": "faker_company", "data_category": "text", "params": {"locale": "zh_CN"}}, "生产国": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["中国", "美国", "日本", "德国", "韩国", "法国", "英国", "意大利", "瑞典", "西班牙"]}}, "上市时间": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2000, "end_year": 2024, "format": "%Y-%m-%d"}}, "价格区间": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 50000, "max_value": 1000000, "format_string": "{:,}元"}}, "座位数": {"generator_type": "integer_range", "data_category": "numeric", "params": {"min": 2, "max": 9}}, "载重量": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 0.5, "max_value": 50.0, "decimals": 1, "format_string": "{:.1f}吨"}}, "尺寸规格": {"generator_type": "dimension_format", "data_category": "text", "params": {"min_length": 3000, "max_length": 6000, "min_width": 1500, "max_width": 2200, "min_height": 1200, "max_height": 2000, "format_string": "{:d}×{:d}×{:d}mm"}}, "动力类型": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["汽油", "柴油", "电动", "混合动力", "氢能源", "天然气", "生物燃料"]}}, "最高速度": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 100, "max_value": 350, "format_string": "{:d}km/h"}}, "油耗": {"generator_type": "numerical_range_formatted", "data_category": "numeric", "params": {"min_value": 4.5, "max_value": 15.0, "decimals": 1, "format_string": "{:.1f}L/100km"}}}}, "personnel": {"display_name": "人员信息", "table_title_template": "人员基本信息表", "columns": {"姓名": {"generator_type": "faker_name", "data_category": "text", "params": {"locale": "zh_CN"}}, "年龄": {"generator_type": "integer_range", "data_category": "numeric", "params": {"min": 18, "max": 65}}, "性别": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["男", "女", "未知", "保密"]}}, "部门": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["研发部", "市场部", "销售部", "行政部", "财务部", "人力资源部", "法务部"]}}, "职位": {"generator_type": "faker_job", "data_category": "text", "params": {"locale": "zh_CN"}}, "入职日期": {"generator_type": "date_range", "data_category": "date", "params": {"start_year": 2010, "end_year": 2024, "format": "%Y-%m-%d"}}, "工号": {"generator_type": "alphanumeric_pattern", "data_category": "text", "params": {"patterns": ["EMP####", "WK-####", "P####"]}}, "联系电话": {"generator_type": "faker_phone_number", "data_category": "text", "params": {"locale": "zh_CN"}}, "电子邮箱": {"generator_type": "faker_email", "data_category": "text", "params": {"locale": "zh_CN"}}, "学历": {"generator_type": "categorical", "data_category": "text", "params": {"values": ["高中", "中专", "大专", "本科", "硕士", "博士", "MBA"]}}}}}}