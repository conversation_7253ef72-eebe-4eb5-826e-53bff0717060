financial = {
    "display_name": "金融数据",
    "table_title_template": "金融市场数据表",
    "columns": {
        "证券名称": {
            "generator_type": "categorical_with_pattern",
            "data_category": "text",
            "params": {
                "prefixes": [
                    "中国",
                    "建设",
                    "工商",
                    "农业",
                    "交通",
                    "招商",
                    "民生",
                    "兴业",
                    "浦发",
                    "广发"
                ],
                "suffixes": [
                    "银行",
                    "证券",
                    "保险",
                    "信托",
                    "投资",
                    "地产",
                    "科技",
                    "电力",
                    "石油",
                    "钢铁"
                ]
            }
        },
        "证券代码": {
            "generator_type": "alphanumeric_pattern",
            "data_category": "text",
            "params": {
                "patterns": [
                    "60####",
                    "00####",
                    "30####",
                    "688###",
                    "83####"
                ]
            }
        },
        "交易日期": {
            "generator_type": "date_range",
            "data_category": "date",
            "params": {
                "start_year": 2022,
                "end_year": 2023,
                "format": "%Y-%m-%d"
            }
        },
        "开盘价": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 5.0,
                "max_value": 100.0,
                "decimals": 2,
                "format_string": "{:.2f}"
            }
        },
        "收盘价": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 5.0,
                "max_value": 100.0,
                "decimals": 2,
                "format_string": "{:.2f}"
            }
        },
        "最高价": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 5.0,
                "max_value": 100.0,
                "decimals": 2,
                "format_string": "{:.2f}"
            }
        },
        "最低价": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 5.0,
                "max_value": 100.0,
                "decimals": 2,
                "format_string": "{:.2f}"
            }
        },
        "成交量": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 1000000,
                "max_value": 100000000,
                "format_string": "{:,}"
            }
        },
        "成交额": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 10000000,
                "max_value": 1000000000,
                "format_string": "{:,}"
            }
        },
        "涨跌幅": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": -10.0,
                "max_value": 10.0,
                "decimals": 2,
                "format_string": "{:.2f}%"
            }
        },
        "市盈率": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 5.0,
                "max_value": 100.0,
                "decimals": 2,
                "format_string": "{:.2f}"
            }
        },
        "交易所": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "上海证券交易所",
                    "深圳证券交易所",
                    "北京证券交易所",
                    "香港交易所",
                    "纽约证券交易所",
                    "纳斯达克",
                    "伦敦证券交易所",
                    "东京证券交易所",
                    "新加坡证券交易所",
                    "法兰克福证券交易所"
                ]
            }
        },
        "行业分类": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "金融业",
                    "房地产业",
                    "信息技术",
                    "医药健康",
                    "能源",
                    "原材料",
                    "必需消费品",
                    "可选消费品",
                    "工业",
                    "公用事业"
                ]
            }
        },
        "股息率": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 0.0,
                "max_value": 10.0,
                "decimals": 2,
                "format_string": "{:.2f}%"
            }
        },
        "总市值": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 1000000000,
                "max_value": 10000000000000,
                "format_string": "{:,}"
            }
        },
        "备注": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "无",
                    "重要",
                    "紧急",
                    "待跟进",
                    "已完成",
                    "已取消"
                ]
            },
            "data_category": "text"
        }
    },
    "text_columns_count": 4,
    "text_columns_names": [
        "交易所",
        "行业分类",
        "备注",
        "证券名称",
    ],
    "numeric_columns_count": 12,
    "numeric_columns_names": [
        "证券名称",
        "证券代码",
        "开盘价",
        "收盘价",
        "最高价",
        "最低价",
        "成交量",
        "成交额",
        "涨跌幅",
        "市盈率",
        "股息率",
        "总市值"
    ],
    "date_columns_count": 1,
    "date_columns_names": [
        "交易日期"
    ],
    "other_columns_count": 0,
    "other_columns_names": [],
    "all_columns": [
        "交易所",
        "行业分类",
        "备注",
        "证券名称",
        "证券代码",
        "开盘价",
        "收盘价",
        "最高价",
        "最低价",
        "成交量",
        "成交额",
        "涨跌幅",
        "市盈率",
        "股息率",
        "总市值",
        "交易日期"
    ]
}

