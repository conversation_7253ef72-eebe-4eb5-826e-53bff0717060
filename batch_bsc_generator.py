#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
批量平衡计分卡(BSC)图表生成器
用于批量生成具有不同内容的BSC图表，内容基于table_config.json中的行业主题
"""
import os
import json
import random
import time
import re
from datetime import datetime

# 从主模块导入必要的功能
from generate_40k_tables_multiprocess import (
    generate_blocked_table,
    load_table_config,
    OUTPUT_FOLDER_BLOCKED
)

# 导入BSC并行生成功能
from bsc_table_generator import generate_bsc_tables_multiprocess
import multiprocessing as mp

# 导入卡线表生成功能
from 卡线表生成 import generate_card_line_tables_batch

def extract_keywords_from_theme(theme_data):
    """从主题数据中提取关键词，用于生成更有针对性的内容"""
    keywords = []

    # 尝试从列定义中提取关键词
    for col_name, col_config in theme_data.get("columns", {}).items():
        # 添加列名作为关键词
        if isinstance(col_name, str) and len(col_name) > 1:
            keywords.append(col_name)

        # 从分类变量的值中提取关键词
        if col_config.get("generator_type") == "categorical":
            values = col_config.get("params", {}).get("values", [])
            if isinstance(values, list):
                for value in values:
                    if isinstance(value, str) and len(value) > 1:
                        keywords.append(value)

    # 确保列表中没有重复项
    unique_keywords = list(set(keywords))

    # 如果提取的关键词太少，添加主题显示名
    if len(unique_keywords) < 3:
        display_name = theme_data.get("display_name", "")
        if display_name and display_name not in unique_keywords:
            unique_keywords.append(display_name)

    return unique_keywords

def generate_industry_specific_content(theme_key, theme_data, content_type):
    """
    为特定行业主题生成内容

    参数:
    - theme_key: 主题键
    - theme_data: 主题数据
    - content_type: 内容类型 ("central", "financial", "customer", "internal", "learning")

    返回:
    - 生成的内容字符串
    """
    theme_display = theme_data.get("display_name", theme_key)

    # 提取关键词
    keywords = extract_keywords_from_theme(theme_data)

    # 如果没有足够的关键词，使用默认内容
    if len(keywords) < 2:
        if content_type == "central":
            return f"{theme_display}行业战略：通过提升组织学习能力，优化内部业务流程，增强客户满意度，实现财务绩效的最大化。"
        return None

    # 随机选择关键词
    keyword1 = random.choice(keywords)
    remaining_keywords = [k for k in keywords if k != keyword1]
    keyword2 = random.choice(remaining_keywords) if remaining_keywords else keyword1

    # 根据内容类型生成特定内容
    if content_type == "central":
        templates = [
            f"{theme_display}领域战略：通过提升{keyword1}和{keyword2}相关的组织学习能力，优化内部流程，提高客户满意度，实现财务目标。",
            f"{theme_display}企业愿景：成为{keyword1}行业的领导者，通过持续改进{keyword2}相关流程，为客户创造价值，实现利润增长。",
            f"{theme_display}发展战略：围绕{keyword1}，通过组织学习提升{keyword2}能力，改善客户体验，实现可持续的财务增长。"
        ]
        return random.choice(templates)

    elif content_type == "financial":
        templates = [
            f"如何通过{keyword1}和{keyword2}来提高{theme_display}领域的财务绩效？",
            f"在{theme_display}行业中，我们应如何平衡{keyword1}投入与短期财务回报？",
            f"为实现{theme_display}业务的可持续发展，应如何优化{keyword1}相关的财务策略？"
        ]
        return random.choice(templates)

    elif content_type == "customer":
        templates = [
            f"如何通过提升{keyword1}质量来增强{theme_display}领域的客户满意度？",
            f"在{theme_display}市场中，客户最关注{keyword1}和{keyword2}的哪些方面？",
            f"如何针对不同的{theme_display}客户群体，提供差异化的{keyword1}服务？"
        ]
        return random.choice(templates)

    elif content_type == "internal":
        templates = [
            f"为提高{theme_display}业务效率，我们应如何优化{keyword1}相关的内部流程？",
            f"在{theme_display}领域，哪些{keyword1}流程需要重点改进以支持战略目标？",
            f"如何通过精益管理提升{theme_display}中{keyword1}和{keyword2}的运营效率？"
        ]
        return random.choice(templates)

    elif content_type == "learning":
        templates = [
            f"为支持{theme_display}业务发展，员工需要掌握哪些{keyword1}相关的知识和技能？",
            f"如何建立支持{theme_display}领域{keyword1}创新的组织文化？",
            f"在{theme_display}行业转型中，我们需要关注哪些{keyword1}技术发展趋势？"
        ]
        return random.choice(templates)

    return None

def generate_batch_bsc_tables(count=10, output_folder=OUTPUT_FOLDER_BLOCKED, theme_filter=None):
    """
    批量生成平衡计分卡(BSC)图表

    参数:
    - count: 要生成的BSC图表数量
    - output_folder: 输出文件夹，默认为分块表文件夹
    - theme_filter: 主题过滤器，如果设置，只生成指定主题的BSC图表

    返回:
    - 成功生成的图表数量
    """
    print(f"开始批量生成 {count} 张平衡计分卡(BSC)图表...")
    start_time = time.time()

    # 确保输出目录存在
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        print(f"创建目录: {output_folder}")

    # 加载表格配置以获取内容素材
    config_data = load_table_config()
    if not config_data:
        print("错误: 无法加载表格配置文件，将使用默认内容。")
        # 创建一个默认配置
        config_data = {
            "themes": {
                "default": {
                    "columns": {}
                }
            }
        }

    # 从配置中提取主题
    themes = {}
    for theme_key, theme_data in config_data.get("themes", {}).items():
        # 如果设置了主题过滤器，只包含匹配的主题
        if theme_filter and theme_filter not in theme_key:
            continue
        themes[theme_key] = theme_data

    if not themes:
        print("警告: 未找到匹配的主题，将使用默认内容。")
        themes = {"default": {"display_name": "默认", "columns": {}}}

    # 通用引导性问题模板
    generic_questions = {
        "financial": [
            "为取得财务上的成功，我们应如何满足股东的期望？",
            "如何提高企业盈利能力和股东回报？",
            "我们需要实现怎样的财务目标才能支持战略实施？",
            "如何平衡短期利润与长期增长的关系？",
            "如何优化资源配置以提高财务绩效？"
        ],
        "customer": [
            "为达成公司愿景，我们应如何满足客户的需求？",
            "如何从客户角度创造独特的价值主张？",
            "客户最关心我们产品或服务的哪些方面？",
            "如何提升客户满意度和忠诚度？",
            "我们应当如何区分和服务不同的客户群体？"
        ],
        "internal": [
            "为满足客户和股东的期望，我们必须擅长哪些内部流程？",
            "哪些业务流程需要优化以支持我们的战略目标？",
            "如何提高内部运营效率和质量？",
            "我们应当如何改进产品开发和创新流程？",
            "如何优化价值链以提高竞争优势？"
        ],
        "learning": [
            "为达成公司愿景，我们应如何保持学习、创新和持续改进的能力？",
            "员工需要掌握哪些知识和技能以支持战略执行？",
            "如何建立持续学习的组织文化？",
            "我们需要什么样的技术基础设施来支持未来发展？",
            "如何激励员工积极参与变革和创新？"
        ]
    }

    # 中心区块通用文本模板
    generic_central_texts = [
        "企业愿景与战略：必须通过学习成长，强化核心能力，持续改善内部运作，获得竞争优势，以获得最大化的客户满意，才能够获得理想的财务收益。",
        "公司使命：通过持续的学习成长和内部流程优化，为客户创造卓越价值，从而实现可持续的财务成果。",
        "战略目标：提升组织学习能力，优化内部运作流程，增强客户满意度，实现财务收益最大化。",
        "核心理念：只有重视学习成长，才能改进内部运作，才能提升客户满意，最终才能获得持续的财务收益。",
        "发展战略：以人才发展为基础，以流程优化为手段，以客户为中心，以财务绩效为导向，实现组织可持续发展。"
    ]

    # 创建表头模板
    header_templates = {
        "financial": [
            ["财务目标", "衡量指标", "目标值", "行动方案"],
            ["战略目标", "关键指标", "目标值", "具体措施"],
            ["财务战略", "绩效指标", "期望值", "行动计划"],
            ["盈利目标", "评估标准", "预期结果", "实施策略"],
            ["收益目标", "监测指标", "阈值", "执行方案"]
        ],
        "customer": [
            ["客户目标", "衡量指标", "目标值", "行动方案"],
            ["客户价值", "评估方法", "期望值", "实施策略"],
            ["市场目标", "关键指标", "目标值", "行动计划"],
            ["客户满意", "监测指标", "阈值", "推进措施"],
            ["客户体验", "评价标准", "预期结果", "执行方案"]
        ],
        "internal": [
            ["内部流程", "衡量指标", "目标值", "行动方案"],
            ["运营目标", "关键指标", "预期值", "实施策略"],
            ["流程优化", "评估标准", "期望结果", "行动计划"],
            ["质量管理", "监测指标", "阈值", "推进措施"],
            ["效率提升", "评价方法", "目标标准", "执行方案"]
        ],
        "learning": [
            ["学习与成长", "衡量指标", "目标值", "行动方案"],
            ["人才发展", "关键指标", "期望值", "实施策略"],
            ["创新能力", "评估标准", "预期结果", "执行计划"],
            ["组织学习", "监测指标", "阈值", "推进措施"],
            ["文化建设", "评价方法", "目标标准", "实施方案"]
        ]
    }

    # 生成指定数量的BSC图表
    success_count = 0
    theme_keys = list(themes.keys())

    for i in range(1, count + 1):
        # 随机选择主题
        theme_key = random.choice(theme_keys)
        theme_data = themes[theme_key]
        theme_display = theme_data.get("display_name", theme_key)

        # 为该主题生成专业内容
        # 中心区块内容 - 70%概率使用行业特定内容，30%概率使用通用内容
        if random.random() < 0.7:
            central_text = generate_industry_specific_content(theme_key, theme_data, "central")
            if not central_text:  # 如果生成失败，使用通用内容
                central_text = random.choice(generic_central_texts)
                if theme_key != "default":
                    central_text = f"{theme_display}领域战略：{central_text}"
        else:
            central_text = random.choice(generic_central_texts)
            if theme_key != "default":
                central_text = f"{theme_display}领域战略：{central_text}"

        # 财务区块引导性问题 - 60%概率使用行业特定内容
        if random.random() < 0.6:
            financial_question = generate_industry_specific_content(theme_key, theme_data, "financial")
            if not financial_question:
                financial_question = random.choice(generic_questions["financial"])
        else:
            financial_question = random.choice(generic_questions["financial"])

        # 客户区块引导性问题 - 60%概率使用行业特定内容
        if random.random() < 0.6:
            customer_question = generate_industry_specific_content(theme_key, theme_data, "customer")
            if not customer_question:
                customer_question = random.choice(generic_questions["customer"])
        else:
            customer_question = random.choice(generic_questions["customer"])

        # 内部流程区块引导性问题 - 60%概率使用行业特定内容
        if random.random() < 0.6:
            internal_question = generate_industry_specific_content(theme_key, theme_data, "internal")
            if not internal_question:
                internal_question = random.choice(generic_questions["internal"])
        else:
            internal_question = random.choice(generic_questions["internal"])

        # 学习成长区块引导性问题 - 60%概率使用行业特定内容
        if random.random() < 0.6:
            learning_question = generate_industry_specific_content(theme_key, theme_data, "learning")
            if not learning_question:
                learning_question = random.choice(generic_questions["learning"])
        else:
            learning_question = random.choice(generic_questions["learning"])

        # 随机选择各区块的表头
        financial_headers = random.choice(header_templates["financial"])
        customer_headers = random.choice(header_templates["customer"])
        internal_headers = random.choice(header_templates["internal"])
        learning_headers = random.choice(header_templates["learning"])

        # 设置输出文件名，使用主题名称作为前缀
        timestamp = int(time.time())
        output_filename = f"bsc_{theme_key}_{timestamp}_{i}.jpg"

        # 确定图片宽度 - 在1500到1800之间随机选择
        image_width = random.randint(1500, 1800)

        # 生成BSC图表
        success = generate_blocked_table(
            central_text=central_text,
            financial_headers=financial_headers,
            financial_question=financial_question,
            customer_headers=customer_headers,
            customer_question=customer_question,
            internal_headers=internal_headers,
            internal_question=internal_question,
            learning_headers=learning_headers,
            learning_question=learning_question,
            output_filename=output_filename,
            image_width=image_width
        )

        if success:
            success_count += 1
            print(f"成功生成第 {success_count} 张BSC图表: {output_filename} (主题: {theme_display})")
        else:
            print(f"生成第 {i} 张BSC图表时失败 (主题: {theme_display})")

    # 计算耗时
    total_elapsed_seconds = time.time() - start_time
    hours, remainder = divmod(total_elapsed_seconds, 3600)
    minutes, seconds = divmod(remainder, 60)

    print("=" * 60)
    print(f"BSC图表批量生成完成!")
    print(f"目标生成数量: {count}")
    print(f"实际成功生成: {success_count}")
    print(f"总耗时: {int(hours)}小时 {int(minutes)}分钟 {seconds:.2f}秒")
    if success_count > 0:
        print(f"平均每张图表耗时: {total_elapsed_seconds / success_count:.4f} 秒")
    print("=" * 60)

    return success_count

def generate_batch_bsc_tables_parallel(count=1000, process_count=None, output_folder=OUTPUT_FOLDER_BLOCKED, theme_filter=None):
    """
    使用并行处理批量生成平衡计分卡(BSC)图表 - 高效版本

    参数:
    - count: 要生成的BSC图表数量
    - process_count: 并行进程数，默认为CPU核心数-1
    - output_folder: 输出文件夹，默认为分块表文件夹
    - theme_filter: 主题过滤器，如果设置，只生成指定主题的BSC图表

    返回:
    - 成功生成的图表数量
    """
    print(f"🚀 开始并行生成 {count} 张平衡计分卡(BSC)图表...")
    print(f"📊 使用高效多进程并行生成模式")

    if process_count is None:
        process_count = max(1, mp.cpu_count() - 1)

    print(f"⚙️ 配置: {process_count} 个并行进程")
    print(f"📁 输出目录: {output_folder}")
    if theme_filter:
        print(f"🎯 主题过滤: {theme_filter}")
    print("=" * 60)

    start_time = time.time()

    # 确保输出目录存在
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        print(f"创建目录: {output_folder}")

    # 使用专门的BSC并行生成函数
    success_count = generate_bsc_tables_multiprocess(count, process_count)

    # 计算耗时
    total_elapsed_seconds = time.time() - start_time
    hours, remainder = divmod(total_elapsed_seconds, 3600)
    minutes, seconds = divmod(remainder, 60)

    print("=" * 60)
    print(f"🎉 BSC图表并行生成完成!")
    print(f"📊 目标生成数量: {count}")
    print(f"✅ 实际成功生成: {success_count}")
    print(f"📈 成功率: {(success_count/count*100):.1f}%")
    print(f"⏱️ 总耗时: {int(hours)}小时 {int(minutes)}分钟 {seconds:.2f}秒")
    if success_count > 0:
        print(f"⚡ 平均每张图表耗时: {total_elapsed_seconds / success_count:.4f} 秒")
        print(f"🚀 生成速度: {success_count / total_elapsed_seconds:.2f} 张/秒")
    print("=" * 60)

    return success_count

# 主函数
if __name__ == "__main__":
    import sys
    import argparse

    # 默认配置
    default_count = 10 # 生成1000张不同的BSC图表
    default_process_count = max(1, mp.cpu_count() - 1)  # 默认并行进程数

    parser = argparse.ArgumentParser(description='批量生成平衡计分卡(BSC)图表')
    parser.add_argument('-c', '--count', type=int, default=default_count, help='要生成的BSC图表数量')
    parser.add_argument('-p', '--processes', type=int, default=default_process_count, help='并行进程数')
    parser.add_argument('-t', '--theme', type=str, default=None, help='主题过滤器，只生成包含指定字符串的主题')
    parser.add_argument('-o', '--output', type=str, default=OUTPUT_FOLDER_BLOCKED, help='输出文件夹路径')
    parser.add_argument('--mode', choices=['parallel', 'sequential'], default='parallel',
                       help='生成模式: parallel(并行,推荐) 或 sequential(顺序)')

    args = parser.parse_args()

    # 显示配置信息
    print("🎯 平衡计分卡(BSC)批量生成器")
    print("=" * 60)
    print(f"📊 生成数量: {args.count} 张")
    print(f"⚙️ 生成模式: {'并行处理' if args.mode == 'parallel' else '顺序处理'}")
    if args.mode == 'parallel':
        print(f"🔄 并行进程数: {args.processes}")
    print(f"📁 输出目录: {args.output}")
    if args.theme:
        print(f"🎯 主题过滤: {args.theme}")
    print("=" * 60)

    # 根据模式选择生成方法
    if args.mode == 'parallel':
        # 使用高效并行生成模式（推荐）
        print("🚀 使用并行生成模式（推荐用于大批量生成）")
        success_count = generate_batch_bsc_tables_parallel(
            count=args.count,
            process_count=args.processes,
            output_folder=args.output,
            theme_filter=args.theme
        )
    else:
        # 使用原始顺序生成模式
        print("🐌 使用顺序生成模式（适合小批量或调试）")
        success_count = generate_batch_bsc_tables(
            count=args.count,
            output_folder=args.output,
            theme_filter=args.theme
        )

    print(f"\n🎉 生成完成！成功生成 {success_count} 张BSC图表")