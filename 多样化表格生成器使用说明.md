# 多样化卡片线表格生成器使用说明

## 概述

`multi_shape_table_generator.py` 是一个全新的多样化卡片线表格生成器，支持多种几何图形布局的表格生成系统。与传统的固定菱形布局不同，该生成器可以创建基于不同几何图形的动态表格布局。

## 主要特性

### 1. 支持的几何图形类型
- **正方形 (Square)**: 4个顶点，规整布局
- **菱形 (Diamond)**: 4个顶点，经典菱形布局
- **三角形 (Triangle)**: 3个顶点，紧凑布局
- **五边形 (Pentagon)**: 5个顶点，均衡分布
- **六边形 (Hexagon)**: 6个顶点，蜂窝状布局
- **圆形 (Circle)**: 6-8个顶点，圆周分布
- **八边形 (Octagon)**: 8个顶点，多角度布局
- **五角星 (Star)**: 10个顶点，星形布局

### 2. 表格特性
- **动态规格**: 1-4列，1-5行的随机组合
- **智能定位**: 基于几何图形顶点的精确定位
- **重叠避免**: 自动检测和解决表格重叠问题
- **边界保护**: 确保表格不超出画布边界

### 3. 样式增强
- **5种配色方案**: 清新蓝绿、温暖橙黄、优雅紫色、自然绿色、经典灰色
- **随机单元格合并**: 支持水平、垂直、L型合并
- **动态颜色分配**: 智能的单元格背景色彩分配

### 4. 画布系统
- **灵活尺寸**: 支持1200-2000px宽，800-1200px高的画布
- **标准化坐标**: 使用百分比坐标系统，便于缩放
- **可视化辅助**: 包含图形轮廓线和顶点标记

## 使用方法

### 基本使用

```python
from multi_shape_table_generator import generate_multi_shape_table, ShapeType

# 生成单个表格
generate_multi_shape_table(
    shape_type=ShapeType.PENTAGON,
    output_filename="我的五边形表格.jpg",
    canvas_width=1600,
    canvas_height=1000
)
```

### 测试所有图形类型

```python
from multi_shape_table_generator import test_all_shapes

# 测试所有支持的图形类型
test_all_shapes()
```

### 批量生成随机表格

```python
from multi_shape_table_generator import generate_random_tables

# 生成10张随机表格
generate_random_tables(10)
```

### 演示特定图形

```python
from multi_shape_table_generator import demo_specific_shapes

# 演示特定图形的生成效果
demo_specific_shapes()
```

## 核心类说明

### GeometryGenerator
负责生成各种几何图形的顶点坐标，支持：
- 数学精确的顶点计算
- 随机尺寸变化
- SVG路径生成

### LayoutManager  
管理表格布局和位置优化：
- 智能位置计算
- 重叠检测和解决
- 边界约束处理

### StyleManager
处理视觉样式和配色：
- 多种配色方案
- 随机颜色分配
- 单元格合并规则

### TableGenerator
主要的表格生成器：
- 整合所有组件
- 生成完整HTML文档
- 输出最终图片

## 输出文件

生成的图片保存在 `表格/多样化表格/` 目录下，包含：
- 图形轮廓线（虚线显示）
- 多个数据表格（位于顶点位置）
- 顶点标记（红色圆点）
- 信息面板（显示图形类型、顶点数量等）

## 技术要求

- Python 3.7+
- imgkit 库
- wkhtmltopdf 工具
- 支持中文字体的系统

## 配置说明

### 画布尺寸
- 默认: 1600x1000 像素
- 推荐范围: 1200-2000px 宽，800-1200px 高

### 表格规格
- 列数: 1-4列（随机）
- 行数: 1-5行（随机）
- 最小尺寸: 8%x6%（画布百分比）
- 最大尺寸: 18%x15%（画布百分比）

### 颜色方案
每个方案包含5种颜色：主色、次色、强调色、背景色、文字色

## 扩展性

该生成器设计具有良好的扩展性：
- 可以轻松添加新的几何图形类型
- 支持自定义配色方案
- 可以调整表格生成规则
- 支持不同的合并策略

## 注意事项

1. 确保安装了 wkhtmltopdf 工具
2. 生成大量图片时注意磁盘空间
3. 复杂图形（如星形）可能需要更多处理时间
4. 建议在生成前清理旧的测试文件

## 示例输出

运行测试后，您将看到：
- 8种不同图形类型的测试图片
- 5张随机配置的样例图片
- 4张特定图形的演示图片

每张图片都展示了独特的几何布局和表格配置，体现了系统的多样性和灵活性。
