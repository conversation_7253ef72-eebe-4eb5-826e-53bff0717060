education = {
    "display_name": "教育信息",
    "table_title_template": "教育信息表",
    "columns": {
        "学校名称": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "北京大学",
                    "清华大学",
                    "复旦大学",
                    "上海交通大学",
                    "浙江大学",
                    "南京大学",
                    "武汉大学",
                    "中国人民大学",
                    "中山大学",
                    "华中科技大学",
                    "南开大学",
                    "四川大学",
                    "吉林大学",
                    "山东大学",
                    "厦门大学",
                    "哈尔滨工业大学",
                    "西安交通大学",
                    "中南大学",
                    "电子科技大学",
                    "东南大学",
                    "同济大学",
                    "北京师范大学",
                    "天津大学",
                    "华南理工大学",
                    "中国科学技术大学",
                    "华东师范大学",
                    "北京航空航天大学",
                    "大连理工大学",
                    "重庆大学",
                    "西北工业大学",
                    "中国农业大学",
                    "湖南大学",
                    "东北大学",
                    "北京理工大学",
                    "华中师范大学",
                    "北京科技大学",
                    "南京师范大学",
                    "中国海洋大学",
                    "西南大学",
                    "兰州大学",
                    "云南大学",
                    "广西大学",
                    "贵州大学",
                    "东北师范大学",
                    "西北大学",
                    "安徽大学",
                    "郑州大学",
                    "首都师范大学",
                    "暨南大学",
                    "深圳大学",
                    "苏州大学",
                    "南昌大学",
                    "海南大学",
                    "福州大学",
                    "宁波大学",
                    "青岛大学",
                    "合肥工业大学",
                    "北京交通大学",
                    "北京邮电大学",
                    "华南师范大学",
                    "西南交通大学",
                    "北京工业大学",
                    "南京航空航天大学",
                    "华东理工大学",
                    "河海大学",
                    "中国矿业大学",
                    "北京中医药大学",
                    "上海财经大学",
                    "中国政法大学",
                    "中央财经大学",
                    "对外经济贸易大学",
                    "中国传媒大学",
                    "上海外国语大学",
                    "复旦大学附属中学",
                    "北京四中",
                    "上海中学",
                    "南京外国语学校",
                    "成都七中",
                    "华南师范大学附属中学",
                    "广州市第二中学",
                    "深圳中学",
                    "杭州学军中学",
                    "西安交通大学附属中学",
                    "武汉外国语学校",
                    "东北师范大学附属中学",
                    "清华大学附属中学",
                    "复旦大学附属小学",
                    "上海师范大学附属小学",
                    "人大附中",
                    "北大附中",
                    "南师附中"
                ]
            },
            "data_category": "text"
        },
        "专业": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "计算机科学与技术",
                    "软件工程",
                    "人工智能",
                    "数据科学",
                    "信息安全",
                    "电子信息工程",
                    "通信工程",
                    "自动化",
                    "机械工程",
                    "金融学",
                    "经济学",
                    "会计学",
                    "工商管理",
                    "市场营销",
                    "国际贸易",
                    "法学",
                    "汉语言文学",
                    "英语",
                    "日语",
                    "新闻学",
                    "医学",
                    "临床医学",
                    "药学",
                    "化学",
                    "物理学",
                    "数学",
                    "统计学",
                    "生物技术",
                    "生物医学工程",
                    "环境科学",
                    "材料科学与工程",
                    "土木工程",
                    "建筑学",
                    "城市规划",
                    "风景园林",
                    "航空航天工程",
                    "船舶与海洋工程",
                    "电气工程",
                    "能源与动力工程",
                    "核工程",
                    "农学",
                    "林学",
                    "水产养殖学",
                    "动物医学",
                    "兽医学",
                    "食品科学与工程",
                    "生物工程",
                    "化学工程与工艺",
                    "制药工程",
                    "高分子材料与工程",
                    "冶金工程",
                    "矿业工程",
                    "石油工程",
                    "测绘工程",
                    "地质工程",
                    "安全工程",
                    "工业设计",
                    "产品设计",
                    "视觉传达设计",
                    "环境设计",
                    "音乐表演",
                    "绘画",
                    "雕塑",
                    "戏剧影视文学",
                    "广播电视编导",
                    "播音与主持艺术",
                    "动画",
                    "舞蹈学",
                    "运动训练",
                    "体育教育",
                    "社会学",
                    "政治学与行政学",
                    "德语",
                    "法语",
                    "西班牙语",
                    "意大利语",
                    "俄语",
                    "阿拉伯语",
                    "朝鲜语",
                    "印地语",
                    "历史学",
                    "考古学",
                    "哲学",
                    "宗教学",
                    "天文学",
                    "地理科学",
                    "大气科学",
                    "海洋科学",
                    "地质学",
                    "心理学",
                    "应用心理学",
                    "教育学",
                    "学前教育",
                    "特殊教育",
                    "小学教育",
                    "公共事业管理",
                    "行政管理",
                    "资源环境与城乡规划管理",
                    "电子商务",
                    "物流管理",
                    "旅游管理",
                    "计算机科学与技术",
                    "软件工程",
                    "人工智能",
                    "网络工程",
                    "信息安全",
                    "数据科学与大数据技术",
                    "物联网工程",
                    "数字媒体技术",
                    "智能科学与技术",
                    "空间信息与数字技术",
                    "电子信息工程",
                    "通信工程",
                    "自动化",
                    "电气工程及其自动化",
                    "机械设计制造及其自动化",
                    "车辆工程",
                    "能源与动力工程",
                    "土木工程",
                    "工程管理",
                    "工业设计",
                    "建筑学",
                    "城市规划",
                    "环境设计",
                    "测绘工程",
                    "化学工程与工艺",
                    "制药工程",
                    "生物工程",
                    "生物信息学",
                    "临床医学",
                    "口腔医学",
                    "护理学",
                    "医学影像学",
                    "中医学",
                    "公共卫生与预防医学",
                    "药学",
                    "经济学",
                    "国际经济与贸易",
                    "金融学",
                    "会计学",
                    "财务管理",
                    "审计学",
                    "工商管理",
                    "人力资源管理",
                    "市场营销",
                    "物流管理",
                    "电子商务",
                    "旅游管理",
                    "法学",
                    "知识产权",
                    "社会工作",
                    "政治学与行政学",
                    "国际政治",
                    "汉语言文学",
                    "新闻学",
                    "广告学",
                    "广播电视学",
                    "网络与新媒体",
                    "英语",
                    "日语",
                    "翻译",
                    "教育学",
                    "学前教育",
                    "小学教育",
                    "心理学",
                    "历史学",
                    "数学与应用数学",
                    "信息与计算科学",
                    "物理学",
                    "应用物理学",
                    "化学",
                    "应用化学",
                    "统计学",
                    "地理科学",
                    "地质学",
                    "环境科学",
                    "环境工程",
                    "生态学",
                    "海洋科学",
                    "材料科学与工程",
                    "高分子材料与工程",
                    "冶金工程",
                    "安全工程",
                    "核工程与核技术",
                    "船舶与海洋工程",
                    "航空航天工程",
                    "兵器科学与技术",
                    "交通运输",
                    "轨道交通信号与控制",
                    "农业机械化及其自动化",
                    "园艺",
                    "动物医学",
                    "食品科学与工程",
                    "酿酒工程",
                    "茶学",
                    "美术学",
                    "音乐学",
                    "舞蹈学",
                    "戏剧影视文学",
                    "数字艺术",
                    "动画",
                    "游戏设计"
                ]
            },
            "data_category": "text"
        },
        "学历": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "高中",
                    "中专",
                    "大专",
                    "本科",
                    "硕士",
                    "博士",
                    "博士后",
                    "初中",
                    "小学",
                    "职业高中",
                    "技工学校",
                    "自学考试",
                    "成人高考",
                    "电大",
                    "远程教育",
                    "研究生在读",
                    "函授",
                    "留学",
                    "联合培养",
                    "MBA",
                    "EMBA",
                    "MPA",
                    "双学位",
                    "辅修学位",
                    "专升本",
                    "高起专",
                    "专转本",
                    "硕博连读",
                    "学士学位",
                    "硕士学位",
                    "博士学位"
                ]
            },
            "data_category": "text"
        },
        "毕业时间": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2010,
                "end_year": 2023,
                "format": "%Y-%m-%d"
            },
            "data_category": "date"
        },
        "入学时间": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2005,
                "end_year": 2020,
                "format": "%Y-%m-%d"
            },
            "data_category": "date"
        },
        "学位": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "学士学位",
                    "硕士学位",
                    "博士学位",
                    "名誉博士学位",
                    "副博士学位",
                    "法学学士",
                    "理学学士",
                    "工学学士",
                    "医学学士",
                    "管理学学士",
                    "经济学学士",
                    "文学学士",
                    "教育学学士",
                    "农学学士",
                    "军事学学士",
                    "艺术学学士",
                    "哲学学士",
                    "历史学学士",
                    "法学硕士",
                    "理学硕士",
                    "工学硕士",
                    "医学硕士",
                    "管理学硕士",
                    "经济学硕士",
                    "文学硕士",
                    "教育学硕士",
                    "农学硕士",
                    "军事学硕士",
                    "艺术学硕士",
                    "哲学硕士",
                    "历史学硕士",
                    "法学博士",
                    "理学博士",
                    "工学博士",
                    "医学博士",
                    "管理学博士",
                    "经济学博士",
                    "文学博士",
                    "教育学博士",
                    "农学博士",
                    "军事学博士",
                    "艺术学博士",
                    "哲学博士",
                    "历史学博士",
                    "双学位",
                    "联合学位",
                    "专业学位",
                    "学位证书",
                    "同等学力申硕"
                ]
            },
            "data_category": "text"
        },
        "绩点": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 2.0,
                "max_value": 4.0,
                "decimals": 2,
                "format_string": "{:.2f}"
            },
            "data_category": "numeric"
        },
        "排名": {
            "generator_type": "integer_range_with_unit",
            "params": {
                "min": 1,
                "max": 30,
                "unit": "%"
            },
            "data_category": "numeric"
        },
        "奖学金": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "国家奖学金",
                    "国家励志奖学金",
                    "校级一等奖学金",
                    "校级二等奖学金",
                    "校级三等奖学金",
                    "专项奖学金",
                    "企业奖学金",
                    "无",
                    "研究生国家奖学金",
                    "优秀学生奖学金",
                    "学习优秀奖学金",
                    "科研优秀奖学金",
                    "社会工作奖学金",
                    "社会实践奖学金",
                    "单项奖学金",
                    "优秀新生奖学金",
                    "优秀毕业生奖学金",
                    "综合奖学金",
                    "国际交流奖学金",
                    "学科竞赛奖学金",
                    "学术创新奖学金",
                    "特等奖学金",
                    "院长奖学金",
                    "校长奖学金",
                    "社会捐赠奖学金",
                    "助学金",
                    "勤工助学基金",
                    "贫困生补助",
                    "少数民族学生奖学金",
                    "优秀学生干部奖学金",
                    "创新创业奖学金",
                    "体育特长奖学金",
                    "艺术特长奖学金",
                    "军训优秀奖学金",
                    "优秀志愿者奖学金",
                    "科技发明奖学金",
                    "社会工作积极分子奖学金",
                    "文艺活动积极分子奖学金",
                    "优秀团员奖学金",
                    "优秀党员奖学金",
                    "竞赛优胜奖学金"
                ]
            },
            "data_category": "text"
        },
        "导师": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        }
    },
    "text_columns_count": 6,
    "text_columns_names": [
        "学校名称",
        "专业",
        "学历",
        "学位",
        "奖学金",
        "导师"
    ],
    "numeric_columns_count": 2,
    "numeric_columns_names": [
        "绩点",
        "排名"
    ],
    "date_columns_count": 2,
    "date_columns_names": [
        "毕业时间",
        "入学时间"
    ],
    "other_columns_count": 0,
    "other_columns_names": [],
    "all_columns": [
        "学校名称",
        "专业",
        "学历",
        "学位",
        "奖学金",
        "导师",
        "绩点",
        "排名",
        "毕业时间",
        "入学时间"
    ]
}

