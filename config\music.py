music = {
    "display_name": "音乐数据",
    "table_title_template": "音乐作品数据表",
    "columns": {
        "歌曲名称": {
            "generator_type": "categorical_with_pattern",
            "params": {
                "prefixes": [
                    "夜的",
                    "爱的",
                    "心的",
                    "梦中",
                    "青春",
                    "永恒",
                    "回忆",
                    "思念",
                    "孤独",
                    "快乐",
                    "忧伤",
                    "希望"
                ],
                "suffixes": [
                    "旋律",
                    "光芒",
                    "眼泪",
                    "微笑",
                    "记忆",
                    "舞曲",
                    "幻想",
                    "思绪",
                    "旅程",
                    "故事",
                    "声音",
                    "倒影",
                    "节奏"
                ]
            },
            "data_category": "text"
        },
        "歌手": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "专辑名称": {
            "generator_type": "categorical_with_pattern",
            "params": {
                "prefixes": [
                    "新的",
                    "最美的",
                    "永恒的",
                    "我的",
                    "心的",
                    "爱的",
                    "第一",
                    "梦想",
                    "回归",
                    "黑色",
                    "白色",
                    "红色"
                ],
                "suffixes": [
                    "世界",
                    "旅程",
                    "时光",
                    "季节",
                    "爱情",
                    "记忆",
                    "梦想",
                    "告白",
                    "选择",
                    "勇气",
                    "空间",
                    "宣言"
                ]
            },
            "data_category": "text"
        },
        "发行时间": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2000,
                "end_year": 2023,
                "format": "%Y-%m-%d"
            },
            "data_category": "date"
        },
        "音乐风格": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "流行",
                    "摇滚",
                    "民谣",
                    "电子",
                    "嘻哈",
                    "R&B",
                    "爵士",
                    "古典",
                    "蓝调",
                    "重金属",
                    "朋克",
                    "乡村",
                    "民族",
                    "拉丁",
                    "雷鬼",
                    "世界音乐",
                    "实验音乐",
                    "后摇",
                    "新世纪",
                    "独立音乐"
                ]
            },
            "data_category": "text"
        },
        "时长": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 2.0,
                "max_value": 8.0,
                "decimals": 2,
                "format_string": "{:.2f}分钟"
            },
            "data_category": "numeric"
        },
        "播放量": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 10000,
                "max_value": 100000000,
                "format_string": "{:,}"
            },
            "data_category": "numeric"
        },
        "评分": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 6.0,
                "max_value": 10.0,
                "decimals": 1,
                "format_string": "{:.1f}"
            },
            "data_category": "numeric"
        },
        "语言": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "国语",
                    "粤语",
                    "英语",
                    "日语",
                    "韩语",
                    "法语",
                    "西班牙语",
                    "意大利语",
                    "德语",
                    "拉丁语",
                    "方言",
                    "多语种"
                ]
            },
            "data_category": "text"
        },
        "作曲": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "作词": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "唱片公司": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "环球音乐",
                    "索尼音乐",
                    "华纳音乐",
                    "摩登天空",
                    "太合音乐",
                    "英皇娱乐",
                    "华研国际",
                    "福茂唱片",
                    "相信音乐",
                    "海蝶音乐",
                    "乐华娱乐",
                    "百纳娱乐",
                    "杰威尔音乐",
                    "奔跑怪物",
                    "听见音乐",
                    "华纳飞碟",
                    "金牌大风"
                ]
            },
            "data_category": "text"
        }
    },
    "text_columns_count": 6,
    "text_columns_names": [
        "歌手",
        "音乐风格",
        "语言",
        "作曲",
        "作词",
        "唱片公司"
    ],
    "numeric_columns_count": 5,
    "numeric_columns_names": [
        "歌曲名称",
        "专辑名称",
        "时长",
        "播放量",
        "评分"
    ],
    "date_columns_count": 1,
    "date_columns_names": [
        "发行时间"
    ],
    "other_columns_count": 0,
    "other_columns_names": [],
    "all_columns": [
        "歌手",
        "音乐风格",
        "语言",
        "作曲",
        "作词",
        "唱片公司",
        "歌曲名称",
        "专辑名称",
        "时长",
        "播放量",
        "评分",
        "发行时间"
    ]
}

