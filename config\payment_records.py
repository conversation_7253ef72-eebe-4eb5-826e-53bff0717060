payment_records = {
    "display_name": "支付流水",
    "table_title_template": "支付流水记录表",
    "columns": {
        "流水号": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "PAY########",
                    "TX########",
                    "P########",
                    "PMT########"
                ]
            },
            "data_category": "text"
        },
        "交易时间": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2023,
                "end_year": 2023,
                "format": "%Y-%m-%d %H:%M:%S"
            },
            "data_category": "date"
        },
        "用户ID": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "UID####",
                    "U######",
                    "USER####"
                ]
            },
            "data_category": "text"
        },
        "用户名": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "交易类型": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "消费",
                    "充值",
                    "提现",
                    "转账",
                    "退款",
                    "收款",
                    "还款",
                    "缴费",
                    "投资",
                    "分红",
                    "借款",
                    "红包",
                    "工资",
                    "报销",
                    "奖金",
                    "利息",
                    "租金",
                    "保险理赔",
                    "退税"
                ]
            },
            "data_category": "text"
        },
        "交易金额": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 0.01,
                "max_value": 100000,
                "decimals": 2,
                "format_string": "{:.2f}"
            },
            "data_category": "numeric"
        },
        "支付方式": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "支付宝",
                    "微信支付",
                    "银行卡",
                    "信用卡",
                    "Apple Pay",
                    "花呗",
                    "借呗",
                    "PayPal",
                    "现金",
                    "余额支付",
                    "积分抵扣",
                    "银联",
                    "Google Pay",
                    "云闪付",
                    "快捷支付"
                ]
            },
            "data_category": "text"
        },
        "交易状态": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "成功",
                    "失败",
                    "处理中",
                    "已退款",
                    "部分退款",
                    "已撤销",
                    "已冻结",
                    "待确认",
                    "异常"
                ]
            },
            "data_category": "text"
        },
        "支付渠道": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "APP",
                    "网页",
                    "小程序",
                    "H5",
                    "扫码",
                    "POS机",
                    "自动柜员机",
                    "线下门店",
                    "智能终端",
                    "语音助手",
                    "第三方平台",
                    "API接口"
                ]
            },
            "data_category": "text"
        },
        "收款方": {
            "generator_type": "faker_company",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "交易备注": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "订单支付成功",
                    "感谢您的购买",
                    "退款已处理",
                    "支付失败，请重试",
                    "订单已取消",
                    "发货完成",
                    "交易完成",
                    "货到付款",
                    "商品已发货",
                    "支付成功",
                    "正在处理中",
                    "订单待确认",
                    "付款成功，准备发货",
                    "感谢您的支持",
                    "订单确认中",
                    "正在备货",
                    "感谢光临",
                    "订单正在配送",
                    "商品退货中",
                    "发货中",
                    "付款处理中",
                    "您的商品已发出",
                    "订单取消成功",
                    "退款处理中",
                    "支付确认中",
                    "交易处理中",
                    "退货成功",
                    "支付超时，请重新支付",
                    "发货提醒",
                    "订单已打包",
                    "发货完成，等待送达",
                    "已收到付款",
                    "订单已发货，请查收",
                    "交易成功",
                    "订单已完成",
                    "支付确认",
                    "订单处理中",
                    "库存不足，等待补货",
                    "订单已退货",
                    "商品已收回",
                    "等待确认支付",
                    "付款已到账",
                    "商品补发中",
                    "退货退款申请成功",
                    "订单已处理",
                    "支付后请耐心等待",
                    "物流已安排",
                    "退款成功",
                    "支付失败，请联系",
                    "订单已完成，感谢",
                    "请尽快完成支付",
                    "正在处理订单",
                    "已为您处理退款",
                    "订单已发货，正在途中",
                    "支付确认成功",
                    "已退款，感谢等待",
                    "等待货物发出",
                    "商品库存不足",
                    "订单正在出库",
                    "配送途中",
                    "付款已完成",
                    "订单待发货",
                    "商品发出，预计送达",
                    "已确认支付",
                    "等待发货",
                    "订单已转交快递",
                    "正在进行中",
                    "商品已发出，请耐心等候",
                    "订单取消处理中",
                    "退款中，请稍等",
                    "订单已暂停",
                    "支付成功，开始处理",
                    "退款已处理完成",
                    "发货待处理",
                    "订单已确认",
                    "处理中，请耐心等待",
                    "已处理您的订单",
                    "等待配送中",
                    "商品已退货",
                    "退款已到账",
                    "订单已支付",
                    "发货提醒，注意查收",
                    "订单提交成功",
                    "订单审核中",
                    "订单创建成功",
                    "商品已发货，请查收",
                    "订单被取消",
                    "商品正在包装中",
                    "订单已取消，请知悉",
                    "支付处理中",
                    "配送已安排",
                    "付款已确认",
                    "订单已出库",
                    "支付完成，准备发货",
                    "订单已付款，正在处理",
                    "商品已退回",
                    "订单取消已完成",
                    "商品补发完成"
                ]
            },
            "data_category": "text"
        },
        "手续费": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 0,
                "max_value": 100,
                "decimals": 2,
                "format_string": "{:.2f}"
            },
            "data_category": "numeric"
        }
    },
    "text_columns_count": 6,
    "text_columns_names": [
        "用户名",
        "交易类型",
        "支付方式",
        "交易状态",
        "收款方",
        "交易备注"
    ],
    "numeric_columns_count": 4,
    "numeric_columns_names": [
        "流水号",
        "用户ID",
        "交易金额",
        "手续费"
    ],
    "date_columns_count": 1,
    "date_columns_names": [
        "交易时间"
    ],
    "other_columns_count": 1,
    "other_columns_names": [
        "支付渠道"
    ],
    "all_columns": [
        "用户名",
        "交易类型",
        "支付方式",
        "交易状态",
        "收款方",
        "交易备注",
        "流水号",
        "用户ID",
        "交易金额",
        "手续费",
        "交易时间",
        "支付渠道"
    ]
}

