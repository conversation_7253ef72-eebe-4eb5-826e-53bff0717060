travel = {
    "display_name": "旅游数据",
    "table_title_template": "旅游景点数据表",
    "columns": {
        "景点名称": {
            "generator_type": "categorical_with_pattern",
            "data_category": "text",
            "params": {
                "prefixes": {
                    "type": "common_data",
                    "key": "major_cities"
                },
                "suffixes": [
                    "故宫",
                    "长城",
                    "博物馆",
                    "动物园",
                    "植物园",
                    "公园",
                    "湖",
                    "山",
                    "寺",
                    "塔",
                    "广场",
                    "海滩",
                    "古镇"
                ]
            }
        },
        "所在地": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": {
                    "type": "common_data",
                    "key": "cities"
                }
            }
        },
        "景区等级": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "5A级",
                    "4A级",
                    "3A级",
                    "2A级",
                    "1A级",
                    "非A级",
                    "世界遗产",
                    "国家公园",
                    "国家风景名胜区",
                    "国家森林公园",
                    "国家地质公园"
                ]
            }
        },
        "门票价格": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 0,
                "max_value": 300,
                "format_string": "{:d}"
            }
        },
        "游客数量": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 1,
                "max_value": 100,
                "format_string": "{:d}万人次/年"
            }
        },
        "开放时间": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "08:00-17:00",
                    "08:30-17:30",
                    "09:00-18:00",
                    "09:00-17:00",
                    "全天开放",
                    "08:00-18:00",
                    "08:00-20:00",
                    "09:00-16:00"
                ]
            }
        },
        "建议游玩时间": {
            "generator_type": "integer_range_with_unit",
            "data_category": "numeric",
            "params": {
                "min": 2,
                "max": 8,
                "unit": "小时"
            }
        },
        "景点类型": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "自然风光",
                    "历史遗迹",
                    "博物馆",
                    "主题公园",
                    "休闲娱乐",
                    "宗教场所",
                    "民俗文化",
                    "现代建筑",
                    "古建筑群",
                    "海滩海岛"
                ]
            }
        },
        "最佳季节": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "春季",
                    "夏季",
                    "秋季",
                    "冬季",
                    "全年",
                    "春夏",
                    "秋冬",
                    "春秋",
                    "3-5月",
                    "6-8月",
                    "9-11月",
                    "12-2月"
                ]
            }
        },
        "交通方式": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "公交",
                    "地铁",
                    "出租车",
                    "自驾",
                    "景区巴士",
                    "步行",
                    "高铁",
                    "飞机",
                    "多种方式",
                    "轮船"
                ]
            }
        },
        "住宿选择": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "五星级酒店",
                    "四星级酒店",
                    "三星级酒店",
                    "经济型酒店",
                    "精品酒店",
                    "主题酒店",
                    "度假村",
                    "民宿",
                    "客栈",
                    "青年旅舍"
                ]
            }
        },
        "特色体验": {
            "generator_type": "categorical",
            "data_category": "text",
            "params": {
                "values": [
                    "温泉泡浴",
                    "滑雪",
                    "冲浪",
                    "潜水",
                    "攀岩",
                    "徒步",
                    "骑马",
                    "骑骆驼",
                    "热气球",
                    "直升机观光",
                    "游船",
                    "漂流",
                    "滑翔伞",
                    "蹦极",
                    "跳伞"
                ]
            }
        },
        "旅游评分": {
            "generator_type": "numerical_range_formatted",
            "data_category": "numeric",
            "params": {
                "min_value": 3.5,
                "max_value": 5.0,
                "decimals": 1,
                "format_string": "{:.1f}分"
            }
        },
        "参观日期": {
            "generator_type": "date_range",
            "data_category": "date",
            "params": {
                "start_year": 2022,
                "end_year": 2024,
                "format": "%Y-%m-%d"
            }
        }
    },
    "text_columns_count": 7,
    "text_columns_names": [
        "所在地",
        "景点类型",
        "交通方式",
        "特色体验",
        "最佳季节",
        "住宿选择",
        "景区等级"
    ],
    "numeric_columns_count": 5,
    "numeric_columns_names": [
        "景点名称",
        "景区等级",
        "门票价格",
        "游客数量",
        "旅游评分"
    ],
    "date_columns_count": 3,
    "date_columns_names": [
        "开放时间",
        "建议游玩时间",
        "参观日期"
    ],
    "other_columns_count": 2,
    "other_columns_names": [
        "最佳季节",
        "住宿选择"
    ],
    "all_columns": [
        "所在地",
        "景点类型",
        "交通方式",
        "特色体验",
        "景点名称",
        "景区等级",
        "门票价格",
        "游客数量",
        "旅游评分",
        "开放时间",
        "建议游玩时间",
        "参观日期",
        "最佳季节",
        "住宿选择"
    ]
}

