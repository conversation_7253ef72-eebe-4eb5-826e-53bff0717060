#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
多样化卡片线表格生成器
支持多种几何图形布局的表格生成系统
"""

import os
import json
import random
import time
import math
import imgkit
from typing import List, Dict, Tuple, Any, Optional, NamedTuple
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
from faker import Faker
import importlib
import glob

# Configure imgkit to use wkhtmltopdf executable
WKHTMLTOPDF_PATH = r'C:\Program Files\wkhtmltopdf\bin\wkhtmltoimage.exe'
if os.path.exists(WKHTMLTOPDF_PATH):
    config = imgkit.config(wkhtmltoimage=WKHTMLTOPDF_PATH)
else:
    config = None

# 输出目录常量
OUTPUT_FOLDER = os.path.join("表格", "多样化表格")

class ShapeType(Enum):
    """支持的几何图形类型"""
    SQUARE = "square"
    DIAMOND = "diamond"
    TRIANGLE = "triangle"
    PENTAGON = "pentagon"
    HEXAGON = "hexagon"
    CIRCLE = "circle"
    OCTAGON = "octagon"
    STAR = "star"

@dataclass
class Point:
    """二维坐标点"""
    x: float
    y: float
    
    def __post_init__(self):
        # 确保坐标在合理范围内
        self.x = max(0, min(100, self.x))
        self.y = max(0, min(100, self.y))

@dataclass
class TablePosition:
    """表格位置信息"""
    center: Point
    width: float
    height: float
    rows: int
    cols: int
    
    @property
    def left(self) -> float:
        return self.center.x - self.width / 2
    
    @property
    def right(self) -> float:
        return self.center.x + self.width / 2
    
    @property
    def top(self) -> float:
        return self.center.y - self.height / 2
    
    @property
    def bottom(self) -> float:
        return self.center.y + self.height / 2

@dataclass
class ColorScheme:
    """配色方案"""
    name: str
    primary: str
    secondary: str
    accent: str
    background: str
    text: str

class GeometryGenerator:
    """几何图形生成器"""
    
    def __init__(self, canvas_width: int = 1600, canvas_height: int = 1000):
        self.canvas_width = canvas_width
        self.canvas_height = canvas_height
        self.center_x = 50.0  # 画布中心点 (百分比)
        self.center_y = 50.0
        
    def generate_vertices(self, shape_type: ShapeType, vertex_count: Optional[int] = None) -> List[Point]:
        """
        生成指定图形的顶点坐标
        
        Args:
            shape_type: 图形类型
            vertex_count: 顶点数量（可选，某些图形支持自定义）
            
        Returns:
            顶点坐标列表
        """
        if shape_type == ShapeType.SQUARE:
            return self._generate_square_vertices()
        elif shape_type == ShapeType.DIAMOND:
            return self._generate_diamond_vertices()
        elif shape_type == ShapeType.TRIANGLE:
            return self._generate_triangle_vertices()
        elif shape_type == ShapeType.PENTAGON:
            return self._generate_pentagon_vertices()
        elif shape_type == ShapeType.HEXAGON:
            return self._generate_hexagon_vertices()
        elif shape_type == ShapeType.CIRCLE:
            count = vertex_count or random.randint(6, 8)
            return self._generate_circle_vertices(count)
        elif shape_type == ShapeType.OCTAGON:
            return self._generate_octagon_vertices()
        elif shape_type == ShapeType.STAR:
            return self._generate_star_vertices()
        else:
            raise ValueError(f"不支持的图形类型: {shape_type}")
    
    def _generate_square_vertices(self) -> List[Point]:
        """生成正方形顶点"""
        size = random.uniform(25, 35)  # 正方形边长的一半
        return [
            Point(self.center_x - size, self.center_y - size),  # 左上
            Point(self.center_x + size, self.center_y - size),  # 右上
            Point(self.center_x + size, self.center_y + size),  # 右下
            Point(self.center_x - size, self.center_y + size),  # 左下
        ]
    
    def _generate_diamond_vertices(self) -> List[Point]:
        """生成菱形顶点"""
        width = random.uniform(30, 40)
        height = random.uniform(25, 35)
        return [
            Point(self.center_x, self.center_y - height),      # 上
            Point(self.center_x + width, self.center_y),       # 右
            Point(self.center_x, self.center_y + height),      # 下
            Point(self.center_x - width, self.center_y),       # 左
        ]
    
    def _generate_triangle_vertices(self) -> List[Point]:
        """生成等边三角形顶点"""
        size = random.uniform(28, 38)
        height = size * math.sqrt(3) / 2
        return [
            Point(self.center_x, self.center_y - height * 2/3),           # 顶点
            Point(self.center_x - size, self.center_y + height * 1/3),    # 左下
            Point(self.center_x + size, self.center_y + height * 1/3),    # 右下
        ]
    
    def _generate_pentagon_vertices(self) -> List[Point]:
        """生成正五边形顶点"""
        radius = random.uniform(25, 35)
        vertices = []
        for i in range(5):
            angle = 2 * math.pi * i / 5 - math.pi / 2  # 从顶部开始
            x = self.center_x + radius * math.cos(angle)
            y = self.center_y + radius * math.sin(angle)
            vertices.append(Point(x, y))
        return vertices
    
    def _generate_hexagon_vertices(self) -> List[Point]:
        """生成正六边形顶点"""
        radius = random.uniform(25, 35)
        vertices = []
        for i in range(6):
            angle = 2 * math.pi * i / 6
            x = self.center_x + radius * math.cos(angle)
            y = self.center_y + radius * math.sin(angle)
            vertices.append(Point(x, y))
        return vertices
    
    def _generate_circle_vertices(self, count: int) -> List[Point]:
        """生成圆形上均匀分布的顶点"""
        radius = random.uniform(25, 35)
        vertices = []
        for i in range(count):
            angle = 2 * math.pi * i / count
            x = self.center_x + radius * math.cos(angle)
            y = self.center_y + radius * math.sin(angle)
            vertices.append(Point(x, y))
        return vertices
    
    def _generate_octagon_vertices(self) -> List[Point]:
        """生成正八边形顶点"""
        radius = random.uniform(25, 35)
        vertices = []
        for i in range(8):
            angle = 2 * math.pi * i / 8
            x = self.center_x + radius * math.cos(angle)
            y = self.center_y + radius * math.sin(angle)
            vertices.append(Point(x, y))
        return vertices
    
    def _generate_star_vertices(self) -> List[Point]:
        """生成五角星顶点"""
        outer_radius = random.uniform(30, 40)
        inner_radius = outer_radius * 0.4
        vertices = []
        for i in range(5):
            # 外顶点
            angle = 2 * math.pi * i / 5 - math.pi / 2
            x = self.center_x + outer_radius * math.cos(angle)
            y = self.center_y + outer_radius * math.sin(angle)
            vertices.append(Point(x, y))
            
            # 内顶点
            angle = 2 * math.pi * (i + 0.5) / 5 - math.pi / 2
            x = self.center_x + inner_radius * math.cos(angle)
            y = self.center_y + inner_radius * math.sin(angle)
            vertices.append(Point(x, y))
        return vertices
    
    def get_shape_svg_path(self, shape_type: ShapeType, vertices: List[Point]) -> str:
        """生成SVG路径字符串用于绘制图形轮廓"""
        if not vertices:
            return ""

        path_parts = [f"M {vertices[0].x} {vertices[0].y}"]
        for vertex in vertices[1:]:
            path_parts.append(f"L {vertex.x} {vertex.y}")
        path_parts.append("Z")  # 闭合路径

        return " ".join(path_parts)


class DataGenerator:
    """数据生成器 - 为表格单元格生成有意义的内容"""

    def __init__(self):
        self.fake = Faker(['zh_CN', 'en_US'])
        Faker.seed(random.randint(0, 999999))

        # 预定义的数据类别和对应的生成方法
        self.data_categories = {
            'person': self._generate_person_data,
            'company': self._generate_company_data,
            'product': self._generate_product_data,
            'location': self._generate_location_data,
            'finance': self._generate_finance_data,
            'education': self._generate_education_data,
            'technology': self._generate_technology_data,
            'general': self._generate_general_data
        }

        # 预定义的中文数据池
        self.chinese_data_pools = {
            'names': ['张伟', '王芳', '李娜', '刘强', '陈静', '杨洋', '赵敏', '孙丽', '周杰', '吴磊'],
            'companies': ['华为技术', '腾讯科技', '阿里巴巴', '百度网络', '京东集团', '小米科技', '字节跳动', '美团网络'],
            'cities': ['北京', '上海', '广州', '深圳', '杭州', '南京', '成都', '武汉', '西安', '重庆'],
            'products': ['智能手机', '笔记本电脑', '平板电脑', '智能手表', '无线耳机', '智能音箱', '电动汽车', '智能家居'],
            'departments': ['研发部', '销售部', '市场部', '人事部', '财务部', '运营部', '客服部', '技术部'],
            'subjects': ['数学', '语文', '英语', '物理', '化学', '生物', '历史', '地理', '政治', '体育'],
            'status': ['进行中', '已完成', '待审核', '已取消', '暂停', '优秀', '良好', '一般', '待改进']
        }

    def generate_table_data(self, rows: int, cols: int) -> List[List[str]]:
        """
        生成表格数据

        Args:
            rows: 行数
            cols: 列数

        Returns:
            二维数据列表，第一行为表头，其余为数据行
        """
        # 随机选择数据类别
        category = random.choice(list(self.data_categories.keys()))

        # 生成表头
        headers = self._generate_headers(cols, category)

        # 生成数据行
        data_rows = []
        for i in range(rows - 1):  # 减1因为第一行是表头
            row_data = []
            for j, header in enumerate(headers):
                if j == 0:  # 第一列通常是序号
                    row_data.append(str(i + 1))
                else:
                    # 根据表头类型生成相应数据
                    data = self._generate_cell_data(header, category)
                    row_data.append(data)
            data_rows.append(row_data)

        return [headers] + data_rows

    def _generate_headers(self, cols: int, category: str) -> List[str]:
        """根据数据类别生成表头"""
        headers_map = {
            'person': ['序号', '姓名', '年龄', '职业', '联系方式'],
            'company': ['序号', '公司名称', '行业', '员工数', '成立年份'],
            'product': ['序号', '产品名称', '价格', '库存', '销量'],
            'location': ['序号', '城市', '人口', '面积', 'GDP'],
            'finance': ['序号', '项目', '收入', '支出', '利润'],
            'education': ['序号', '学科', '分数', '等级', '备注'],
            'technology': ['序号', '技术', '版本', '状态', '更新时间'],
            'general': ['序号', '项目', '数值', '状态', '备注']
        }

        available_headers = headers_map.get(category, headers_map['general'])

        # 确保第一列是序号
        selected_headers = ['序号']

        # 从剩余表头中随机选择
        remaining_headers = available_headers[1:]
        needed_cols = min(cols - 1, len(remaining_headers))

        if needed_cols > 0:
            selected_headers.extend(random.sample(remaining_headers, needed_cols))

        # 如果列数不够，添加通用列名
        while len(selected_headers) < cols:
            selected_headers.append(f'列{len(selected_headers)}')

        return selected_headers[:cols]

    def _generate_cell_data(self, header: str, category: str) -> str:
        """根据表头和类别生成单元格数据"""
        header_lower = header.lower()

        # 根据表头关键词生成相应数据
        if '姓名' in header or 'name' in header_lower:
            return random.choice(self.chinese_data_pools['names'])
        elif '公司' in header or 'company' in header_lower:
            return random.choice(self.chinese_data_pools['companies'])
        elif '城市' in header or 'city' in header_lower:
            return random.choice(self.chinese_data_pools['cities'])
        elif '产品' in header or 'product' in header_lower:
            return random.choice(self.chinese_data_pools['products'])
        elif '部门' in header or 'department' in header_lower:
            return random.choice(self.chinese_data_pools['departments'])
        elif '学科' in header or 'subject' in header_lower:
            return random.choice(self.chinese_data_pools['subjects'])
        elif '状态' in header or 'status' in header_lower:
            return random.choice(self.chinese_data_pools['status'])
        elif '年龄' in header or 'age' in header_lower:
            return str(random.randint(18, 65))
        elif '价格' in header or 'price' in header_lower:
            return f'¥{random.randint(100, 9999)}'
        elif '分数' in header or 'score' in header_lower:
            return str(random.randint(60, 100))
        elif '数量' in header or '库存' in header or 'count' in header_lower:
            return str(random.randint(1, 999))
        elif '时间' in header or 'time' in header_lower or '日期' in header:
            return self.fake.date_this_year().strftime('%Y-%m-%d')
        elif '电话' in header or 'phone' in header_lower:
            return self.fake.phone_number()
        elif '邮箱' in header or 'email' in header_lower:
            return self.fake.email()
        else:
            # 使用类别生成器生成数据
            return self.data_categories.get(category, self._generate_general_data)()

    def _generate_person_data(self) -> str:
        """生成人员相关数据"""
        options = [
            self.fake.name(),
            f'{random.randint(20, 60)}岁',
            self.fake.job(),
            self.fake.phone_number()
        ]
        return random.choice(options)

    def _generate_company_data(self) -> str:
        """生成公司相关数据"""
        options = [
            self.fake.company(),
            f'{random.randint(10, 5000)}人',
            f'{random.randint(1990, 2023)}年',
            random.choice(['科技', '制造', '服务', '金融', '教育', '医疗'])
        ]
        return random.choice(options)

    def _generate_product_data(self) -> str:
        """生成产品相关数据"""
        options = [
            random.choice(self.chinese_data_pools['products']),
            f'¥{random.randint(99, 9999)}',
            f'{random.randint(0, 999)}件',
            f'{random.randint(1, 999)}台'
        ]
        return random.choice(options)

    def _generate_location_data(self) -> str:
        """生成地理位置相关数据"""
        options = [
            random.choice(self.chinese_data_pools['cities']),
            f'{random.randint(50, 3000)}万人',
            f'{random.randint(1000, 50000)}平方公里',
            self.fake.address()
        ]
        return random.choice(options)

    def _generate_finance_data(self) -> str:
        """生成财务相关数据"""
        options = [
            f'¥{random.randint(1000, 999999)}',
            f'{random.uniform(0.1, 99.9):.1f}%',
            f'{random.randint(-50, 200)}%',
            f'{random.randint(1, 12)}月'
        ]
        return random.choice(options)

    def _generate_education_data(self) -> str:
        """生成教育相关数据"""
        options = [
            random.choice(self.chinese_data_pools['subjects']),
            f'{random.randint(60, 100)}分',
            random.choice(['优秀', '良好', '中等', '及格']),
            f'第{random.randint(1, 10)}名'
        ]
        return random.choice(options)

    def _generate_technology_data(self) -> str:
        """生成技术相关数据"""
        options = [
            f'v{random.randint(1, 9)}.{random.randint(0, 9)}.{random.randint(0, 9)}',
            random.choice(['开发中', '测试中', '已发布', '维护中']),
            f'{random.randint(1, 30)}天前',
            random.choice(['Python', 'Java', 'JavaScript', 'C++', 'Go'])
        ]
        return random.choice(options)

    def _generate_general_data(self) -> str:
        """生成通用数据"""
        options = [
            f'数据{random.randint(1, 999)}',
            f'{random.randint(1, 100)}',
            random.choice(['是', '否']),
            random.choice(['正常', '异常', '待处理']),
            f'项目{random.randint(1, 99)}'
        ]
        return random.choice(options)


class LayoutManager:
    """布局管理器"""

    def __init__(self, canvas_width: int = 1600, canvas_height: int = 1000):
        self.canvas_width = canvas_width
        self.canvas_height = canvas_height
        self.min_table_width = 8.0   # 最小表格宽度 (百分比)
        self.min_table_height = 6.0  # 最小表格高度 (百分比)
        self.max_table_width = 18.0  # 最大表格宽度 (百分比)
        self.max_table_height = 15.0 # 最大表格高度 (百分比)
        self.margin = 2.0           # 表格间最小间距 (百分比)

    def calculate_table_positions(self, vertices: List[Point]) -> List[TablePosition]:
        """
        根据顶点计算表格位置

        Args:
            vertices: 图形顶点列表

        Returns:
            表格位置列表
        """
        positions = []

        # 为每个顶点生成一个表格
        for i, vertex in enumerate(vertices):
            # 随机生成表格规格
            rows = random.randint(1, 5)
            cols = random.randint(1, 4)

            # 根据表格大小计算尺寸
            base_width = self.min_table_width + (cols - 1) * 2.5
            base_height = self.min_table_height + (rows - 1) * 2.0

            width = min(base_width, self.max_table_width)
            height = min(base_height, self.max_table_height)

            # 调整位置避免超出画布边界
            adjusted_center = self._adjust_position_for_bounds(vertex, width, height)

            position = TablePosition(
                center=adjusted_center,
                width=width,
                height=height,
                rows=rows,
                cols=cols
            )
            positions.append(position)

        # 检测并解决重叠问题
        positions = self._resolve_overlaps(positions)

        return positions

    def _adjust_position_for_bounds(self, center: Point, width: float, height: float) -> Point:
        """调整位置确保表格不超出画布边界"""
        # 计算边界限制
        min_x = width / 2 + 1  # 左边界
        max_x = 100 - width / 2 - 1  # 右边界
        min_y = height / 2 + 1  # 上边界
        max_y = 100 - height / 2 - 1  # 下边界

        # 调整坐标
        adjusted_x = max(min_x, min(max_x, center.x))
        adjusted_y = max(min_y, min(max_y, center.y))

        return Point(adjusted_x, adjusted_y)

    def _resolve_overlaps(self, positions: List[TablePosition]) -> List[TablePosition]:
        """解决表格重叠问题"""
        if len(positions) <= 1:
            return positions

        resolved_positions = [positions[0]]  # 第一个位置保持不变

        for current_pos in positions[1:]:
            # 检查与已放置表格的重叠
            max_attempts = 10
            for attempt in range(max_attempts):
                overlaps = False

                for existing_pos in resolved_positions:
                    if self._check_overlap(current_pos, existing_pos):
                        overlaps = True
                        # 尝试调整位置
                        current_pos = self._adjust_position_to_avoid_overlap(current_pos, existing_pos)
                        break

                if not overlaps:
                    break

            resolved_positions.append(current_pos)

        return resolved_positions

    def _check_overlap(self, pos1: TablePosition, pos2: TablePosition) -> bool:
        """检查两个表格是否重叠"""
        # 添加边距检查
        margin = self.margin

        return not (pos1.right + margin < pos2.left or
                   pos2.right + margin < pos1.left or
                   pos1.bottom + margin < pos2.top or
                   pos2.bottom + margin < pos1.top)

    def _adjust_position_to_avoid_overlap(self, current: TablePosition, existing: TablePosition) -> TablePosition:
        """调整位置以避免重叠"""
        # 计算移动方向
        dx = current.center.x - existing.center.x
        dy = current.center.y - existing.center.y

        # 如果位置太接近，随机选择一个方向
        if abs(dx) < 1 and abs(dy) < 1:
            angle = random.uniform(0, 2 * math.pi)
            dx = math.cos(angle)
            dy = math.sin(angle)

        # 标准化方向向量
        length = math.sqrt(dx * dx + dy * dy)
        if length > 0:
            dx /= length
            dy /= length

        # 计算需要移动的距离
        min_distance = (current.width + existing.width) / 2 + self.margin
        move_distance = min_distance + 2  # 额外间距

        # 计算新位置
        new_x = current.center.x + dx * move_distance
        new_y = current.center.y + dy * move_distance

        # 确保新位置在画布范围内
        new_center = self._adjust_position_for_bounds(
            Point(new_x, new_y),
            current.width,
            current.height
        )

        return TablePosition(
            center=new_center,
            width=current.width,
            height=current.height,
            rows=current.rows,
            cols=current.cols
        )


class StyleManager:
    """样式管理器"""

    def __init__(self):
        self.color_schemes = self._init_color_schemes()

    def _init_color_schemes(self) -> List[ColorScheme]:
        """初始化配色方案"""
        return [
            ColorScheme(
                name="清新蓝绿",
                primary="#E3F2FD",
                secondary="#B3E5FC",
                accent="#4FC3F7",
                background="#F8F9FA",
                text="#212529"
            ),
            ColorScheme(
                name="温暖橙黄",
                primary="#FFF3E0",
                secondary="#FFE0B2",
                accent="#FFB74D",
                background="#FAFAFA",
                text="#424242"
            ),
            ColorScheme(
                name="优雅紫色",
                primary="#F3E5F5",
                secondary="#E1BEE7",
                accent="#BA68C8",
                background="#F9F9F9",
                text="#37474F"
            ),
            ColorScheme(
                name="自然绿色",
                primary="#E8F5E8",
                secondary="#C8E6C9",
                accent="#81C784",
                background="#F1F8E9",
                text="#2E7D32"
            ),
            ColorScheme(
                name="经典灰色",
                primary="#F5F5F5",
                secondary="#EEEEEE",
                accent="#9E9E9E",
                background="#FAFAFA",
                text="#424242"
            )
        ]

    def get_random_color_scheme(self) -> ColorScheme:
        """获取随机配色方案"""
        return random.choice(self.color_schemes)

    def generate_cell_colors(self, rows: int, cols: int, scheme: ColorScheme) -> List[List[str]]:
        """生成单元格颜色矩阵"""
        cell_colors = []
        for i in range(rows):
            row_colors = []
            for j in range(cols):
                # 随机选择颜色，但保持一定的规律性
                if random.random() < 0.3:  # 30%概率使用强调色
                    color = scheme.accent
                elif random.random() < 0.5:  # 50%概率使用次要色
                    color = scheme.secondary
                else:  # 其余使用主色
                    color = scheme.primary
                row_colors.append(color)
            cell_colors.append(row_colors)

        return cell_colors

    def generate_cell_merges(self, rows: int, cols: int) -> List[Dict[str, Any]]:
        """生成单元格合并规则"""
        merges = []

        # 随机决定是否进行合并
        if random.random() < 0.4:  # 40%概率进行合并
            merge_type = random.choice(['horizontal', 'vertical', 'l_shape'])

            if merge_type == 'horizontal' and cols > 1:
                # 水平合并
                row = random.randint(0, rows - 1)
                start_col = random.randint(0, cols - 2)
                span = random.randint(2, min(cols - start_col, 3))
                merges.append({
                    'type': 'horizontal',
                    'row': row,
                    'col': start_col,
                    'colspan': span,
                    'rowspan': 1
                })

            elif merge_type == 'vertical' and rows > 1:
                # 垂直合并
                col = random.randint(0, cols - 1)
                start_row = random.randint(0, rows - 2)
                span = random.randint(2, min(rows - start_row, 3))
                merges.append({
                    'type': 'vertical',
                    'row': start_row,
                    'col': col,
                    'colspan': 1,
                    'rowspan': span
                })

            elif merge_type == 'l_shape' and rows > 1 and cols > 1:
                # L型合并（简化版）
                start_row = random.randint(0, rows - 2)
                start_col = random.randint(0, cols - 2)
                merges.append({
                    'type': 'l_shape',
                    'row': start_row,
                    'col': start_col,
                    'colspan': 2,
                    'rowspan': 2
                })

        return merges


class TableGenerator:
    """表格生成器"""

    def __init__(self):
        self.geometry_generator = GeometryGenerator()
        self.layout_manager = LayoutManager()
        self.style_manager = StyleManager()
        self.data_generator = DataGenerator()

    def generate_table_html(self, position: TablePosition, scheme: ColorScheme,
                          table_id: str = "") -> str:
        """生成单个表格的HTML"""

        # 生成表格数据
        table_data = self.data_generator.generate_table_data(position.rows, position.cols)

        # 生成单元格颜色和合并规则
        cell_colors = self.style_manager.generate_cell_colors(
            position.rows, position.cols, scheme
        )
        merges = self.style_manager.generate_cell_merges(
            position.rows, position.cols
        )

        # 创建合并映射
        merge_map = {}
        skip_cells = set()

        for merge in merges:
            row, col = merge['row'], merge['col']
            rowspan, colspan = merge['rowspan'], merge['colspan']
            merge_map[(row, col)] = (rowspan, colspan)

            # 标记需要跳过的单元格
            for r in range(row, row + rowspan):
                for c in range(col, col + colspan):
                    if r != row or c != col:  # 不跳过起始单元格
                        skip_cells.add((r, c))

        # 生成表格HTML
        table_html = f"""
        <div class="table-container" style="
            position: absolute;
            left: {position.left}%;
            top: {position.top}%;
            width: {position.width}%;
            height: {position.height}%;
        ">
            <table class="data-table" id="{table_id}">
        """

        for row in range(position.rows):
            table_html += "<tr>"
            for col in range(position.cols):
                if (row, col) in skip_cells:
                    continue

                # 检查是否有合并
                rowspan, colspan = merge_map.get((row, col), (1, 1))
                color = cell_colors[row][col]

                # 获取单元格数据
                cell_data = "&nbsp;"
                if row < len(table_data) and col < len(table_data[row]):
                    cell_data = table_data[row][col]

                # 为表头行添加特殊样式
                header_style = ""
                if row == 0:
                    header_style = "font-weight: bold;"

                table_html += f"""
                <td style="background-color: {color}; color: {scheme.text}; {header_style}"
                    rowspan="{rowspan}" colspan="{colspan}">
                    {cell_data}
                </td>
                """
            table_html += "</tr>"

        table_html += """
            </table>
        </div>
        """

        return table_html

    def generate_complete_layout(self, shape_type: ShapeType,
                               canvas_width: int = 1600, canvas_height: int = 1000) -> Dict[str, Any]:
        """
        生成完整的布局数据

        Args:
            shape_type: 几何图形类型
            canvas_width: 画布宽度
            canvas_height: 画布高度

        Returns:
            包含所有布局信息的字典
        """
        # 更新生成器尺寸
        self.geometry_generator.canvas_width = canvas_width
        self.geometry_generator.canvas_height = canvas_height
        self.layout_manager.canvas_width = canvas_width
        self.layout_manager.canvas_height = canvas_height

        # 生成几何图形顶点
        vertices = self.geometry_generator.generate_vertices(shape_type)

        # 计算表格位置
        table_positions = self.layout_manager.calculate_table_positions(vertices)

        # 选择配色方案
        color_scheme = self.style_manager.get_random_color_scheme()

        # 生成SVG路径
        svg_path = self.geometry_generator.get_shape_svg_path(shape_type, vertices)

        return {
            'shape_type': shape_type,
            'vertices': vertices,
            'table_positions': table_positions,
            'color_scheme': color_scheme,
            'svg_path': svg_path,
            'canvas_width': canvas_width,
            'canvas_height': canvas_height
        }

    def generate_html_document(self, layout_data: Dict[str, Any]) -> str:
        """生成完整的HTML文档"""

        # 提取布局数据
        shape_type = layout_data['shape_type']
        vertices = layout_data['vertices']
        table_positions = layout_data['table_positions']
        color_scheme = layout_data['color_scheme']
        svg_path = layout_data['svg_path']
        canvas_width = layout_data['canvas_width']
        canvas_height = layout_data['canvas_height']

        # 生成所有表格HTML
        tables_html = ""
        for i, position in enumerate(table_positions):
            table_html = self.generate_table_html(position, color_scheme, f"table_{i}")
            tables_html += table_html

        # 生成顶点标记
        vertices_html = ""
        for i, vertex in enumerate(vertices):
            vertices_html += f"""
            <div class="vertex-marker" style="
                position: absolute;
                left: {vertex.x - 0.5}%;
                top: {vertex.y - 0.5}%;
                width: 1%;
                height: 1%;
                background-color: #FF4444;
                border-radius: 50%;
                z-index: 1000;
            " title="顶点 {i+1}"></div>
            """

        # 构建完整HTML文档
        html_document = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多样化卡片线表格 - {shape_type.value}</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: {color_scheme.background};
            color: {color_scheme.text};
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }}

        .canvas-container {{
            position: relative;
            width: {canvas_width}px;
            height: {canvas_height}px;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }}

        .shape-outline {{
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }}

        .table-container {{
            z-index: 10;
            border: 1px solid #ccc;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}

        .data-table {{
            width: 100%;
            height: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }}

        .data-table td {{
            border: 1px solid #ddd;
            text-align: center;
            vertical-align: middle;
            padding: 4px;
            font-size: 12px;
            min-height: 20px;
        }}

        .vertex-marker {{
            border: 2px solid white;
            box-shadow: 0 0 4px rgba(0,0,0,0.3);
        }}

        .info-panel {{
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: rgba(255,255,255,0.9);
            padding: 10px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 1001;
        }}
    </style>
</head>
<body>
    <div class="canvas-container" id="capture">
        <!-- 图形轮廓 -->
        <svg class="shape-outline" viewBox="0 0 100 100" preserveAspectRatio="none">
            <path d="{svg_path}"
                  fill="none"
                  stroke="{color_scheme.accent}"
                  stroke-width="0.2"
                  stroke-dasharray="1,1"/>
        </svg>

        <!-- 表格 -->
        {tables_html}

        <!-- 顶点标记 -->
        {vertices_html}

        <!-- 信息面板 -->
        <div class="info-panel">
            <strong>图形类型:</strong> {shape_type.value}<br>
            <strong>顶点数量:</strong> {len(vertices)}<br>
            <strong>表格数量:</strong> {len(table_positions)}<br>
            <strong>配色方案:</strong> {color_scheme.name}
        </div>
    </div>
</body>
</html>"""

        return html_document


def generate_multi_shape_table(shape_type: ShapeType, output_filename: str,
                              canvas_width: int = 1600, canvas_height: int = 1000) -> bool:
    """
    生成多样化卡片线表格图片

    Args:
        shape_type: 几何图形类型
        output_filename: 输出文件名
        canvas_width: 画布宽度
        canvas_height: 画布高度

    Returns:
        是否生成成功
    """
    try:
        # 确保输出目录存在
        if not os.path.exists(OUTPUT_FOLDER):
            os.makedirs(OUTPUT_FOLDER)
            print(f"创建目录: {OUTPUT_FOLDER}")

        # 创建表格生成器
        generator = TableGenerator()

        # 生成布局数据
        layout_data = generator.generate_complete_layout(shape_type, canvas_width, canvas_height)

        # 生成HTML文档
        html_content = generator.generate_html_document(layout_data)

        # 生成图片
        output_path = os.path.join(OUTPUT_FOLDER, output_filename)
        options = {
            'width': canvas_width + 40,
            'height': canvas_height + 40,
            'encoding': "UTF-8",
            'quiet': ''
        }

        if config:
            imgkit.from_string(html_content, output_path, options=options, config=config)
        else:
            imgkit.from_string(html_content, output_path, options=options)

        print(f"多样化表格已保存到: {output_path}")
        print(f"图形类型: {shape_type.value}")
        print(f"顶点数量: {len(layout_data['vertices'])}")
        print(f"表格数量: {len(layout_data['table_positions'])}")
        print(f"配色方案: {layout_data['color_scheme'].name}")

        return True

    except Exception as e:
        print(f"生成多样化表格时出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_all_shapes():
    """测试所有支持的图形类型"""
    print("开始测试所有图形类型...")

    shapes_to_test = [
        ShapeType.SQUARE,
        ShapeType.DIAMOND,
        ShapeType.TRIANGLE,
        ShapeType.PENTAGON,
        ShapeType.HEXAGON,
        ShapeType.CIRCLE,
        ShapeType.OCTAGON,
        ShapeType.STAR
    ]

    success_count = 0
    total_count = len(shapes_to_test)

    for i, shape_type in enumerate(shapes_to_test):
        print(f"\n测试 {i+1}/{total_count}: {shape_type.value}")

        filename = f"测试_{shape_type.value}_{int(time.time())}.jpg"

        if generate_multi_shape_table(shape_type, filename):
            success_count += 1
            print(f"✓ {shape_type.value} 测试成功")
        else:
            print(f"✗ {shape_type.value} 测试失败")

    print(f"\n测试完成: {success_count}/{total_count} 成功")
    return success_count == total_count


def test_single_shape(shape_type: ShapeType):
    """测试单个图形类型"""
    print(f"测试图形类型: {shape_type.value}")

    filename = f"单测_{shape_type.value}_{int(time.time())}.jpg"

    if generate_multi_shape_table(shape_type, filename):
        print(f"✓ {shape_type.value} 生成成功")
        return True
    else:
        print(f"✗ {shape_type.value} 生成失败")
        return False


def demo_specific_shapes():
    """演示特定图形的生成效果"""
    print("演示特定图形生成效果...")

    # 演示不同的图形类型
    demo_shapes = [
        (ShapeType.TRIANGLE, "三角形布局"),
        (ShapeType.PENTAGON, "五边形布局"),
        (ShapeType.HEXAGON, "六边形布局"),
        (ShapeType.STAR, "五角星布局")
    ]

    for shape_type, description in demo_shapes:
        print(f"\n生成 {description}...")
        filename = f"演示_{shape_type.value}_{description}.jpg"

        if generate_multi_shape_table(shape_type, filename, 1800, 1200):
            print(f"✓ {description} 生成成功")
        else:
            print(f"✗ {description} 生成失败")


def test_data_filling():
    """测试数据填充功能"""
    print("测试数据填充功能...")

    # 创建数据生成器实例
    data_gen = DataGenerator()

    # 测试不同规格的表格数据生成
    test_cases = [
        (2, 3, "2行3列"),
        (3, 4, "3行4列"),
        (4, 2, "4行2列"),
        (5, 5, "5行5列")
    ]

    for rows, cols, description in test_cases:
        print(f"\n测试 {description} 表格数据生成:")
        table_data = data_gen.generate_table_data(rows, cols)

        print("表头:", table_data[0])
        for i, row in enumerate(table_data[1:], 1):
            print(f"第{i}行:", row)

    print("\n数据填充功能测试完成！")


def demo_data_filled_tables():
    """演示填充数据的表格效果"""
    print("演示填充数据的表格效果...")

    # 测试不同图形类型的数据填充效果
    test_shapes = [
        ShapeType.SQUARE,
        ShapeType.PENTAGON,
        ShapeType.HEXAGON
    ]

    for shape_type in test_shapes:
        print(f"\n生成带数据的 {shape_type.value} 表格...")
        filename = f"数据填充_{shape_type.value}_{int(time.time())}.jpg"

        if generate_multi_shape_table(shape_type, filename, 1600, 1000):
            print(f"✓ {shape_type.value} 数据填充表格生成成功")
        else:
            print(f"✗ {shape_type.value} 数据填充表格生成失败")

    print("\n数据填充演示完成！")


def generate_random_tables(count: int = 10):
    """生成指定数量的随机表格"""
    print(f"开始生成 {count} 张随机多样化表格...")

    all_shapes = list(ShapeType)
    success_count = 0

    for i in range(count):
        # 随机选择图形类型
        shape_type = random.choice(all_shapes)

        # 随机选择画布尺寸
        canvas_width = random.randint(1200, 2000)
        canvas_height = random.randint(800, 1200)

        filename = f"多样化表格_{i+1:03d}_{shape_type.value}.jpg"

        print(f"生成 {i+1}/{count}: {shape_type.value} ({canvas_width}x{canvas_height})")

        if generate_multi_shape_table(shape_type, filename, canvas_width, canvas_height):
            success_count += 1

        # 添加小延迟避免文件名冲突
        time.sleep(0.1)

    print(f"\n批量生成完成: {success_count}/{count} 成功")
    return success_count


def clean_test_files():
    """清理测试文件"""
    if not os.path.exists(OUTPUT_FOLDER):
        return

    print("清理测试文件...")
    cleaned_count = 0

    for filename in os.listdir(OUTPUT_FOLDER):
        if filename.startswith(('测试_', '单测_')) and filename.endswith('.jpg'):
            file_path = os.path.join(OUTPUT_FOLDER, filename)
            try:
                os.remove(file_path)
                cleaned_count += 1
                print(f"删除测试文件: {filename}")
            except Exception as e:
                print(f"删除文件失败 {filename}: {e}")

    print(f"清理完成，删除了 {cleaned_count} 个测试文件")


def interactive_test():
    """交互式测试函数"""
    print("\n" + "="*50)
    print("多样化表格生成器 - 交互式测试")
    print("="*50)

    shapes = list(ShapeType)

    print("\n可用的图形类型:")
    for i, shape in enumerate(shapes, 1):
        print(f"{i}. {shape.value}")

    print(f"{len(shapes)+1}. 随机选择")
    print(f"{len(shapes)+2}. 测试所有类型")
    print("0. 退出")

    while True:
        try:
            choice = input(f"\n请选择图形类型 (0-{len(shapes)+2}): ").strip()

            if choice == '0':
                print("退出测试")
                break
            elif choice == str(len(shapes)+1):
                # 随机选择
                shape_type = random.choice(shapes)
                print(f"随机选择: {shape_type.value}")
                test_single_shape(shape_type)
            elif choice == str(len(shapes)+2):
                # 测试所有类型
                test_all_shapes()
            else:
                choice_idx = int(choice) - 1
                if 0 <= choice_idx < len(shapes):
                    shape_type = shapes[choice_idx]
                    test_single_shape(shape_type)
                else:
                    print("无效选择，请重新输入")
                    continue

        except (ValueError, KeyboardInterrupt):
            print("\n退出测试")
            break
        except Exception as e:
            print(f"发生错误: {e}")
            continue


if __name__ == "__main__":
    print("=" * 60)
    print("多样化卡片线表格生成器")
    print("=" * 60)

    print("\n选择运行模式:")
    print("1. 完整测试模式（测试所有功能）")
    print("2. 交互式测试模式")
    print("3. 快速演示模式")
    print("4. 数据填充测试模式")
    print("5. 清理测试文件")

    try:
        mode = input("\n请选择模式 (1-5): ").strip()

        if mode == "1":
            print("\n=== 完整测试模式 ===")
            # 清理旧的测试文件
            clean_test_files()

            print("\n1. 测试所有图形类型...")
            test_all_shapes()

            print("\n2. 生成随机表格样例...")
            generate_random_tables(5)

            print("\n3. 演示特定图形...")
            demo_specific_shapes()

            print("\n完整测试完成！")

        elif mode == "2":
            print("\n=== 交互式测试模式 ===")
            interactive_test()

        elif mode == "3":
            print("\n=== 快速演示模式 ===")
            print("生成几个示例图形...")

            demo_shapes = [ShapeType.TRIANGLE, ShapeType.PENTAGON, ShapeType.STAR]
            for shape in demo_shapes:
                print(f"生成 {shape.value}...")
                test_single_shape(shape)

            print("快速演示完成！")

        elif mode == "4":
            print("\n=== 数据填充测试模式 ===")
            test_data_filling()
            print("\n演示数据填充表格...")
            demo_data_filled_tables()

        elif mode == "5":
            print("\n=== 清理模式 ===")
            clean_test_files()
            print("清理完成！")

        else:
            print("无效选择，运行默认测试...")
            test_single_shape(ShapeType.HEXAGON)

    except KeyboardInterrupt:
        print("\n\n用户中断，程序退出")
    except Exception as e:
        print(f"\n发生错误: {e}")
        print("运行默认测试...")
        test_single_shape(ShapeType.SQUARE)
