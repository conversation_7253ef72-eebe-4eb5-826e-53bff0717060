#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
多样化卡片线表格生成器
支持多种几何图形布局的表格生成系统
"""

import os
import json
import random
import time
import math
import imgkit
from typing import List, Dict, Tuple, Any, Optional, NamedTuple
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
from faker import Faker
import importlib
import glob
import threading
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing

# Configure imgkit to use wkhtmltopdf executable
WKHTMLTOPDF_PATH = r'C:\Program Files\wkhtmltopdf\bin\wkhtmltoimage.exe'
if os.path.exists(WKHTMLTOPDF_PATH):
    config = imgkit.config(wkhtmltoimage=WKHTMLTOPDF_PATH)
else:
    config = None

# 输出目录常量
OUTPUT_FOLDER = os.path.join("表格", "多样化表格")

# 全局文件计数器
_file_counter = 0
_counter_lock = threading.Lock()

def get_next_filename() -> str:
    """获取下一个文件名"""
    global _file_counter
    with _counter_lock:
        _file_counter += 1
        return f"分块表_{_file_counter:03d}.jpg"

def reset_file_counter():
    """重置文件计数器"""
    global _file_counter
    with _counter_lock:
        _file_counter = 0

class ShapeType(Enum):
    """支持的几何图形类型"""
    # 原有图形类型
    SQUARE = "square"
    DIAMOND = "diamond"
    TRIANGLE = "triangle"
    PENTAGON = "pentagon"
    HEXAGON = "hexagon"
    CIRCLE = "circle"
    OCTAGON = "octagon"
    STAR = "star"

    # 新增多边形图形类型
    NONAGON = "nonagon"              # 九边形
    DECAGON = "decagon"              # 十边形
    HENDECAGON = "hendecagon"        # 十一边形
    DODECAGON = "dodecagon"          # 十二边形
    TRIDECAGON = "tridecagon"        # 十三边形
    TETRADECAGON = "tetradecagon"    # 十四边形
    PENTADECAGON = "pentadecagon"    # 十五边形
    HEXADECAGON = "hexadecagon"      # 十六边形

    # 几何图形变体
    ELLIPSE = "ellipse"              # 椭圆
    RECTANGLE = "rectangle"          # 矩形
    PARALLELOGRAM = "parallelogram"  # 平行四边形
    TRAPEZOID = "trapezoid"          # 梯形
    RHOMBUS = "rhombus"              # 菱形（与diamond不同的实现）
    IRREGULAR_POLYGON = "irregular"   # 不规则多边形

@dataclass
class Point:
    """二维坐标点"""
    x: float
    y: float
    
    def __post_init__(self):
        # 确保坐标在合理范围内
        self.x = max(0, min(100, self.x))
        self.y = max(0, min(100, self.y))

@dataclass
class TablePosition:
    """表格位置信息"""
    center: Point
    width: float
    height: float
    rows: int
    cols: int
    
    @property
    def left(self) -> float:
        return self.center.x - self.width / 2
    
    @property
    def right(self) -> float:
        return self.center.x + self.width / 2
    
    @property
    def top(self) -> float:
        return self.center.y - self.height / 2
    
    @property
    def bottom(self) -> float:
        return self.center.y + self.height / 2

@dataclass
class ColorScheme:
    """配色方案"""
    name: str
    primary: str
    secondary: str
    accent: str
    background: str
    text: str

class GeometryGenerator:
    """几何图形生成器"""
    
    def __init__(self, canvas_width: int = 1600, canvas_height: int = 1000):
        self.canvas_width = canvas_width
        self.canvas_height = canvas_height
        self.center_x = 50.0  # 画布中心点 (百分比)
        self.center_y = 50.0
        
    def generate_vertices(self, shape_type: ShapeType, vertex_count: Optional[int] = None) -> List[Point]:
        """
        生成指定图形的顶点坐标
        
        Args:
            shape_type: 图形类型
            vertex_count: 顶点数量（可选，某些图形支持自定义）
            
        Returns:
            顶点坐标列表
        """
        if shape_type == ShapeType.SQUARE:
            return self._generate_square_vertices()
        elif shape_type == ShapeType.DIAMOND:
            return self._generate_diamond_vertices()
        elif shape_type == ShapeType.TRIANGLE:
            return self._generate_triangle_vertices()
        elif shape_type == ShapeType.PENTAGON:
            return self._generate_pentagon_vertices()
        elif shape_type == ShapeType.HEXAGON:
            return self._generate_hexagon_vertices()
        elif shape_type == ShapeType.CIRCLE:
            count = vertex_count or random.randint(6, 8)
            return self._generate_circle_vertices(count)
        elif shape_type == ShapeType.OCTAGON:
            return self._generate_octagon_vertices()
        elif shape_type == ShapeType.STAR:
            return self._generate_star_vertices()
        elif shape_type == ShapeType.NONAGON:
            return self._generate_regular_polygon_vertices(9)
        elif shape_type == ShapeType.DECAGON:
            return self._generate_regular_polygon_vertices(10)
        elif shape_type == ShapeType.HENDECAGON:
            return self._generate_regular_polygon_vertices(11)
        elif shape_type == ShapeType.DODECAGON:
            return self._generate_regular_polygon_vertices(12)
        elif shape_type == ShapeType.TRIDECAGON:
            return self._generate_regular_polygon_vertices(13)
        elif shape_type == ShapeType.TETRADECAGON:
            return self._generate_regular_polygon_vertices(14)
        elif shape_type == ShapeType.PENTADECAGON:
            return self._generate_regular_polygon_vertices(15)
        elif shape_type == ShapeType.HEXADECAGON:
            return self._generate_regular_polygon_vertices(16)
        elif shape_type == ShapeType.ELLIPSE:
            count = vertex_count or random.randint(8, 12)
            return self._generate_ellipse_vertices(count)
        elif shape_type == ShapeType.RECTANGLE:
            return self._generate_rectangle_vertices()
        elif shape_type == ShapeType.PARALLELOGRAM:
            return self._generate_parallelogram_vertices()
        elif shape_type == ShapeType.TRAPEZOID:
            return self._generate_trapezoid_vertices()
        elif shape_type == ShapeType.RHOMBUS:
            return self._generate_rhombus_vertices()
        elif shape_type == ShapeType.IRREGULAR_POLYGON:
            count = vertex_count or random.randint(6, 10)
            return self._generate_irregular_polygon_vertices(count)
        else:
            raise ValueError(f"不支持的图形类型: {shape_type}")
    
    def _generate_square_vertices(self) -> List[Point]:
        """生成正方形顶点"""
        size = random.uniform(25, 35)  # 正方形边长的一半
        return [
            Point(self.center_x - size, self.center_y - size),  # 左上
            Point(self.center_x + size, self.center_y - size),  # 右上
            Point(self.center_x + size, self.center_y + size),  # 右下
            Point(self.center_x - size, self.center_y + size),  # 左下
        ]
    
    def _generate_diamond_vertices(self) -> List[Point]:
        """生成菱形顶点"""
        width = random.uniform(30, 40)
        height = random.uniform(25, 35)
        return [
            Point(self.center_x, self.center_y - height),      # 上
            Point(self.center_x + width, self.center_y),       # 右
            Point(self.center_x, self.center_y + height),      # 下
            Point(self.center_x - width, self.center_y),       # 左
        ]
    
    def _generate_triangle_vertices(self) -> List[Point]:
        """生成等边三角形顶点"""
        size = random.uniform(28, 38)
        height = size * math.sqrt(3) / 2
        return [
            Point(self.center_x, self.center_y - height * 2/3),           # 顶点
            Point(self.center_x - size, self.center_y + height * 1/3),    # 左下
            Point(self.center_x + size, self.center_y + height * 1/3),    # 右下
        ]
    
    def _generate_pentagon_vertices(self) -> List[Point]:
        """生成正五边形顶点"""
        radius = random.uniform(25, 35)
        vertices = []
        for i in range(5):
            angle = 2 * math.pi * i / 5 - math.pi / 2  # 从顶部开始
            x = self.center_x + radius * math.cos(angle)
            y = self.center_y + radius * math.sin(angle)
            vertices.append(Point(x, y))
        return vertices
    
    def _generate_hexagon_vertices(self) -> List[Point]:
        """生成正六边形顶点"""
        radius = random.uniform(25, 35)
        vertices = []
        for i in range(6):
            angle = 2 * math.pi * i / 6
            x = self.center_x + radius * math.cos(angle)
            y = self.center_y + radius * math.sin(angle)
            vertices.append(Point(x, y))
        return vertices
    
    def _generate_circle_vertices(self, count: int) -> List[Point]:
        """生成圆形上均匀分布的顶点"""
        radius = random.uniform(25, 35)
        vertices = []
        for i in range(count):
            angle = 2 * math.pi * i / count
            x = self.center_x + radius * math.cos(angle)
            y = self.center_y + radius * math.sin(angle)
            vertices.append(Point(x, y))
        return vertices
    
    def _generate_octagon_vertices(self) -> List[Point]:
        """生成正八边形顶点"""
        radius = random.uniform(25, 35)
        vertices = []
        for i in range(8):
            angle = 2 * math.pi * i / 8
            x = self.center_x + radius * math.cos(angle)
            y = self.center_y + radius * math.sin(angle)
            vertices.append(Point(x, y))
        return vertices
    
    def _generate_star_vertices(self) -> List[Point]:
        """生成五角星顶点"""
        outer_radius = random.uniform(30, 40)
        inner_radius = outer_radius * 0.4
        vertices = []
        for i in range(5):
            # 外顶点
            angle = 2 * math.pi * i / 5 - math.pi / 2
            x = self.center_x + outer_radius * math.cos(angle)
            y = self.center_y + outer_radius * math.sin(angle)
            vertices.append(Point(x, y))
            
            # 内顶点
            angle = 2 * math.pi * (i + 0.5) / 5 - math.pi / 2
            x = self.center_x + inner_radius * math.cos(angle)
            y = self.center_y + inner_radius * math.sin(angle)
            vertices.append(Point(x, y))
        return vertices

    def _generate_regular_polygon_vertices(self, sides: int) -> List[Point]:
        """生成正多边形顶点"""
        radius = random.uniform(25, 35)
        vertices = []
        for i in range(sides):
            angle = 2 * math.pi * i / sides - math.pi / 2  # 从顶部开始
            x = self.center_x + radius * math.cos(angle)
            y = self.center_y + radius * math.sin(angle)
            vertices.append(Point(x, y))
        return vertices

    def _generate_ellipse_vertices(self, count: int) -> List[Point]:
        """生成椭圆上均匀分布的顶点"""
        # 椭圆的长轴和短轴
        a = random.uniform(30, 40)  # 长半轴
        b = random.uniform(20, 30)  # 短半轴
        vertices = []
        for i in range(count):
            angle = 2 * math.pi * i / count
            x = self.center_x + a * math.cos(angle)
            y = self.center_y + b * math.sin(angle)
            vertices.append(Point(x, y))
        return vertices

    def _generate_rectangle_vertices(self) -> List[Point]:
        """生成矩形顶点"""
        width = random.uniform(30, 40)
        height = random.uniform(20, 30)
        return [
            Point(self.center_x - width, self.center_y - height),  # 左上
            Point(self.center_x + width, self.center_y - height),  # 右上
            Point(self.center_x + width, self.center_y + height),  # 右下
            Point(self.center_x - width, self.center_y + height),  # 左下
        ]

    def _generate_parallelogram_vertices(self) -> List[Point]:
        """生成平行四边形顶点"""
        width = random.uniform(30, 40)
        height = random.uniform(20, 30)
        skew = random.uniform(5, 15)  # 倾斜度
        return [
            Point(self.center_x - width + skew, self.center_y - height),  # 左上
            Point(self.center_x + width + skew, self.center_y - height),  # 右上
            Point(self.center_x + width - skew, self.center_y + height),  # 右下
            Point(self.center_x - width - skew, self.center_y + height),  # 左下
        ]

    def _generate_trapezoid_vertices(self) -> List[Point]:
        """生成梯形顶点"""
        top_width = random.uniform(20, 30)
        bottom_width = random.uniform(35, 45)
        height = random.uniform(25, 35)
        return [
            Point(self.center_x - top_width, self.center_y - height),     # 左上
            Point(self.center_x + top_width, self.center_y - height),     # 右上
            Point(self.center_x + bottom_width, self.center_y + height),  # 右下
            Point(self.center_x - bottom_width, self.center_y + height),  # 左下
        ]

    def _generate_rhombus_vertices(self) -> List[Point]:
        """生成菱形顶点（与diamond不同的实现，更加倾斜）"""
        width = random.uniform(35, 45)
        height = random.uniform(20, 30)
        return [
            Point(self.center_x, self.center_y - height),      # 上
            Point(self.center_x + width, self.center_y),       # 右
            Point(self.center_x, self.center_y + height),      # 下
            Point(self.center_x - width, self.center_y),       # 左
        ]

    def _generate_irregular_polygon_vertices(self, count: int) -> List[Point]:
        """生成不规则多边形顶点"""
        base_radius = random.uniform(25, 35)
        vertices = []
        for i in range(count):
            # 在基础半径基础上添加随机变化
            radius_variation = random.uniform(0.7, 1.3)
            radius = base_radius * radius_variation

            # 角度也添加一些随机变化
            base_angle = 2 * math.pi * i / count
            angle_variation = random.uniform(-0.3, 0.3)
            angle = base_angle + angle_variation

            x = self.center_x + radius * math.cos(angle)
            y = self.center_y + radius * math.sin(angle)
            vertices.append(Point(x, y))
        return vertices

    def get_shape_svg_path(self, shape_type: ShapeType, vertices: List[Point]) -> str:
        """生成SVG路径字符串用于绘制图形轮廓"""
        if not vertices:
            return ""

        path_parts = [f"M {vertices[0].x} {vertices[0].y}"]
        for vertex in vertices[1:]:
            path_parts.append(f"L {vertex.x} {vertex.y}")
        path_parts.append("Z")  # 闭合路径

        return " ".join(path_parts)

    def calculate_intersection_points(self, shape_type: ShapeType, vertices: List[Point]) -> List[Point]:
        """
        计算图形内部的交叉点

        Args:
            shape_type: 图形类型
            vertices: 图形顶点列表

        Returns:
            交叉点列表
        """
        intersection_points = []

        # 根据图形类型计算不同的交叉点
        if shape_type == ShapeType.STAR:
            # 五角星有明显的内部交叉点
            intersection_points = self._calculate_star_intersections(vertices)
        elif shape_type in [ShapeType.PENTAGON, ShapeType.HEXAGON, ShapeType.OCTAGON,
                           ShapeType.NONAGON, ShapeType.DECAGON, ShapeType.HENDECAGON,
                           ShapeType.DODECAGON, ShapeType.TRIDECAGON, ShapeType.TETRADECAGON,
                           ShapeType.PENTADECAGON, ShapeType.HEXADECAGON]:
            # 正多边形的对角线交叉点
            intersection_points = self._calculate_polygon_diagonal_intersections(vertices)
        elif shape_type in [ShapeType.SQUARE, ShapeType.RECTANGLE, ShapeType.DIAMOND,
                           ShapeType.RHOMBUS, ShapeType.PARALLELOGRAM]:
            # 四边形的对角线交点
            intersection_points = self._calculate_quadrilateral_intersections(vertices)
        elif shape_type == ShapeType.TRIANGLE:
            # 三角形的重心点
            intersection_points = self._calculate_triangle_centroid(vertices)
        elif shape_type in [ShapeType.CIRCLE, ShapeType.ELLIPSE]:
            # 圆形和椭圆的内部网格点
            intersection_points = self._calculate_circular_grid_points(vertices)
        elif shape_type == ShapeType.IRREGULAR_POLYGON:
            # 不规则多边形的内部采样点
            intersection_points = self._calculate_irregular_interior_points(vertices)

        return intersection_points

    def _calculate_star_intersections(self, vertices: List[Point]) -> List[Point]:
        """计算五角星内部交叉点"""
        if len(vertices) < 10:
            return []

        intersections = []
        # 五角星的内部有5个主要交叉点
        # 计算内五边形的顶点作为交叉点
        for i in range(0, 10, 2):  # 取外顶点
            if i + 2 < len(vertices):
                # 计算相邻外顶点连线与对面线段的交点
                p1 = vertices[i]
                p2 = vertices[(i + 4) % 10]

                # 简化计算：取两点中点附近的位置
                intersection_x = (p1.x + p2.x) / 2 + random.uniform(-3, 3)
                intersection_y = (p1.y + p2.y) / 2 + random.uniform(-3, 3)
                intersections.append(Point(intersection_x, intersection_y))

        return intersections[:3]  # 限制数量

    def _calculate_polygon_diagonal_intersections(self, vertices: List[Point]) -> List[Point]:
        """计算正多边形对角线交叉点"""
        if len(vertices) < 5:
            return []

        intersections = []
        center_x = sum(v.x for v in vertices) / len(vertices)
        center_y = sum(v.y for v in vertices) / len(vertices)

        # 在中心附近生成几个交叉点
        num_intersections = min(3, len(vertices) // 3)
        for i in range(num_intersections):
            angle = 2 * math.pi * i / num_intersections
            radius = random.uniform(8, 15)  # 距离中心的距离

            x = center_x + radius * math.cos(angle)
            y = center_y + radius * math.sin(angle)
            intersections.append(Point(x, y))

        return intersections

    def _calculate_quadrilateral_intersections(self, vertices: List[Point]) -> List[Point]:
        """计算四边形对角线交点"""
        if len(vertices) != 4:
            return []

        # 计算两条对角线的交点
        # 对角线1: vertices[0] 到 vertices[2]
        # 对角线2: vertices[1] 到 vertices[3]

        # 简化计算：取四个顶点的中心点作为交叉点
        center_x = sum(v.x for v in vertices) / 4
        center_y = sum(v.y for v in vertices) / 4

        return [Point(center_x, center_y)]

    def _calculate_triangle_centroid(self, vertices: List[Point]) -> List[Point]:
        """计算三角形重心"""
        if len(vertices) != 3:
            return []

        # 三角形重心
        center_x = sum(v.x for v in vertices) / 3
        center_y = sum(v.y for v in vertices) / 3

        return [Point(center_x, center_y)]

    def _calculate_circular_grid_points(self, vertices: List[Point]) -> List[Point]:
        """计算圆形/椭圆内部网格点"""
        if len(vertices) < 6:
            return []

        # 计算中心点
        center_x = sum(v.x for v in vertices) / len(vertices)
        center_y = sum(v.y for v in vertices) / len(vertices)

        # 在内部生成几个点
        intersections = []
        num_points = min(3, len(vertices) // 4)

        for i in range(num_points):
            angle = 2 * math.pi * i / num_points
            radius = random.uniform(5, 12)  # 内部半径

            x = center_x + radius * math.cos(angle)
            y = center_y + radius * math.sin(angle)
            intersections.append(Point(x, y))

        return intersections

    def _calculate_irregular_interior_points(self, vertices: List[Point]) -> List[Point]:
        """计算不规则多边形内部采样点"""
        if len(vertices) < 4:
            return []

        # 计算多边形的边界框
        min_x = min(v.x for v in vertices)
        max_x = max(v.x for v in vertices)
        min_y = min(v.y for v in vertices)
        max_y = max(v.y for v in vertices)

        # 在边界框内部随机生成点
        intersections = []
        attempts = 20
        max_points = 2

        for _ in range(attempts):
            if len(intersections) >= max_points:
                break

            x = random.uniform(min_x + 5, max_x - 5)
            y = random.uniform(min_y + 5, max_y - 5)
            point = Point(x, y)

            # 简单检查：确保点不在边界附近
            if self._is_point_interior(point, vertices):
                intersections.append(point)

        return intersections

    def _is_point_interior(self, point: Point, vertices: List[Point]) -> bool:
        """简单检查点是否在多边形内部（使用射线法的简化版本）"""
        # 这里使用一个简化的内部检查
        # 计算点到所有顶点的平均距离，如果距离适中则认为在内部
        center_x = sum(v.x for v in vertices) / len(vertices)
        center_y = sum(v.y for v in vertices) / len(vertices)

        dist_to_center = math.sqrt((point.x - center_x)**2 + (point.y - center_y)**2)
        avg_vertex_dist = sum(math.sqrt((v.x - center_x)**2 + (v.y - center_y)**2) for v in vertices) / len(vertices)

        return dist_to_center < avg_vertex_dist * 0.6


class DataGenerator:
    """数据生成器 - 为表格单元格生成有意义的内容"""

    def __init__(self):
        self.fake = Faker(['zh_CN', 'en_US'])
        Faker.seed(random.randint(0, 999999))

        # 预定义的数据类别和对应的生成方法
        self.data_categories = {
            'person': self._generate_person_data,
            'company': self._generate_company_data,
            'product': self._generate_product_data,
            'location': self._generate_location_data,
            'finance': self._generate_finance_data,
            'education': self._generate_education_data,
            'technology': self._generate_technology_data,
            'general': self._generate_general_data
        }

        # 预定义的中文数据池
        self.chinese_data_pools = {
            'names': ['张伟', '王芳', '李娜', '刘强', '陈静', '杨洋', '赵敏', '孙丽', '周杰', '吴磊'],
            'companies': ['华为技术', '腾讯科技', '阿里巴巴', '百度网络', '京东集团', '小米科技', '字节跳动', '美团网络'],
            'cities': ['北京', '上海', '广州', '深圳', '杭州', '南京', '成都', '武汉', '西安', '重庆'],
            'products': ['智能手机', '笔记本电脑', '平板电脑', '智能手表', '无线耳机', '智能音箱', '电动汽车', '智能家居'],
            'departments': ['研发部', '销售部', '市场部', '人事部', '财务部', '运营部', '客服部', '技术部'],
            'subjects': ['数学', '语文', '英语', '物理', '化学', '生物', '历史', '地理', '政治', '体育'],
            'status': ['进行中', '已完成', '待审核', '已取消', '暂停', '优秀', '良好', '一般', '待改进']
        }

    def generate_table_data(self, rows: int, cols: int) -> List[List[str]]:
        """
        生成表格数据

        Args:
            rows: 行数（最小2行：1行表头+1行数据）
            cols: 列数（最小2列）

        Returns:
            二维数据列表，第一行为表头，其余为数据行
        """
        # 确保最小规格要求
        rows = max(2, rows)  # 至少2行
        cols = max(2, cols)  # 至少2列

        # 随机选择数据类别
        category = random.choice(list(self.data_categories.keys()))

        # 生成表头
        headers = self._generate_headers(cols, category)

        # 生成数据行
        data_rows = []
        for i in range(rows - 1):  # 减1因为第一行是表头
            row_data = []
            for j, header in enumerate(headers):
                if j == 0:  # 第一列通常是序号
                    row_data.append(str(i + 1))
                else:
                    # 根据表头类型生成相应数据
                    data = self._generate_cell_data(header, category)
                    row_data.append(data)
            data_rows.append(row_data)

        return [headers] + data_rows

    def _generate_headers(self, cols: int, category: str) -> List[str]:
        """根据数据类别生成表头"""
        # 确保最小2列
        cols = max(2, cols)

        headers_map = {
            'person': ['序号', '姓名', '年龄', '职业', '联系方式'],
            'company': ['序号', '公司名称', '行业', '员工数', '成立年份'],
            'product': ['序号', '产品名称', '价格', '库存', '销量'],
            'location': ['序号', '城市', '人口', '面积', 'GDP'],
            'finance': ['序号', '项目', '收入', '支出', '利润'],
            'education': ['序号', '学科', '分数', '等级', '备注'],
            'technology': ['序号', '技术', '版本', '状态', '更新时间'],
            'general': ['序号', '项目', '数值', '状态', '备注']
        }

        available_headers = headers_map.get(category, headers_map['general'])

        # 确保第一列是序号
        selected_headers = ['序号']

        # 从剩余表头中随机选择，确保至少有1个数据列
        remaining_headers = available_headers[1:]
        needed_cols = max(1, min(cols - 1, len(remaining_headers)))  # 至少需要1个数据列

        if needed_cols > 0:
            selected_headers.extend(random.sample(remaining_headers, needed_cols))

        # 如果列数不够，添加通用列名
        while len(selected_headers) < cols:
            selected_headers.append(f'数据{len(selected_headers)}')

        return selected_headers[:cols]

    def _generate_cell_data(self, header: str, category: str) -> str:
        """根据表头和类别生成单元格数据"""
        header_lower = header.lower()

        # 根据表头关键词生成相应数据
        if '姓名' in header or 'name' in header_lower:
            return random.choice(self.chinese_data_pools['names'])
        elif '公司' in header or 'company' in header_lower:
            return random.choice(self.chinese_data_pools['companies'])
        elif '城市' in header or 'city' in header_lower:
            return random.choice(self.chinese_data_pools['cities'])
        elif '产品' in header or 'product' in header_lower:
            return random.choice(self.chinese_data_pools['products'])
        elif '部门' in header or 'department' in header_lower:
            return random.choice(self.chinese_data_pools['departments'])
        elif '学科' in header or 'subject' in header_lower:
            return random.choice(self.chinese_data_pools['subjects'])
        elif '状态' in header or 'status' in header_lower:
            return random.choice(self.chinese_data_pools['status'])
        elif '年龄' in header or 'age' in header_lower:
            return str(random.randint(18, 65))
        elif '价格' in header or 'price' in header_lower:
            return f'¥{random.randint(100, 9999)}'
        elif '分数' in header or 'score' in header_lower:
            return str(random.randint(60, 100))
        elif '数量' in header or '库存' in header or 'count' in header_lower:
            return str(random.randint(1, 999))
        elif '时间' in header or 'time' in header_lower or '日期' in header:
            return self.fake.date_this_year().strftime('%Y-%m-%d')
        elif '电话' in header or 'phone' in header_lower:
            return self.fake.phone_number()
        elif '邮箱' in header or 'email' in header_lower:
            return self.fake.email()
        else:
            # 使用类别生成器生成数据
            return self.data_categories.get(category, self._generate_general_data)()

    def _generate_person_data(self) -> str:
        """生成人员相关数据"""
        options = [
            self.fake.name(),
            f'{random.randint(20, 60)}岁',
            self.fake.job(),
            self.fake.phone_number()
        ]
        return random.choice(options)

    def _generate_company_data(self) -> str:
        """生成公司相关数据"""
        options = [
            self.fake.company(),
            f'{random.randint(10, 5000)}人',
            f'{random.randint(1990, 2023)}年',
            random.choice(['科技', '制造', '服务', '金融', '教育', '医疗'])
        ]
        return random.choice(options)

    def _generate_product_data(self) -> str:
        """生成产品相关数据"""
        options = [
            random.choice(self.chinese_data_pools['products']),
            f'¥{random.randint(99, 9999)}',
            f'{random.randint(0, 999)}件',
            f'{random.randint(1, 999)}台'
        ]
        return random.choice(options)

    def _generate_location_data(self) -> str:
        """生成地理位置相关数据"""
        options = [
            random.choice(self.chinese_data_pools['cities']),
            f'{random.randint(50, 3000)}万人',
            f'{random.randint(1000, 50000)}平方公里',
            self.fake.address()
        ]
        return random.choice(options)

    def _generate_finance_data(self) -> str:
        """生成财务相关数据"""
        options = [
            f'¥{random.randint(1000, 999999)}',
            f'{random.uniform(0.1, 99.9):.1f}%',
            f'{random.randint(-50, 200)}%',
            f'{random.randint(1, 12)}月'
        ]
        return random.choice(options)

    def _generate_education_data(self) -> str:
        """生成教育相关数据"""
        options = [
            random.choice(self.chinese_data_pools['subjects']),
            f'{random.randint(60, 100)}分',
            random.choice(['优秀', '良好', '中等', '及格']),
            f'第{random.randint(1, 10)}名'
        ]
        return random.choice(options)

    def _generate_technology_data(self) -> str:
        """生成技术相关数据"""
        options = [
            f'v{random.randint(1, 9)}.{random.randint(0, 9)}.{random.randint(0, 9)}',
            random.choice(['开发中', '测试中', '已发布', '维护中']),
            f'{random.randint(1, 30)}天前',
            random.choice(['Python', 'Java', 'JavaScript', 'C++', 'Go'])
        ]
        return random.choice(options)

    def _generate_general_data(self) -> str:
        """生成通用数据"""
        options = [
            f'数据{random.randint(1, 999)}',
            f'{random.randint(1, 100)}',
            random.choice(['是', '否']),
            random.choice(['正常', '异常', '待处理']),
            f'项目{random.randint(1, 99)}'
        ]
        return random.choice(options)


class LayoutManager:
    """布局管理器"""

    def __init__(self, canvas_width: int = 1600, canvas_height: int = 1000):
        self.canvas_width = canvas_width
        self.canvas_height = canvas_height
        self.min_table_width = 8.0   # 最小表格宽度 (百分比)
        self.min_table_height = 6.0  # 最小表格高度 (百分比)
        self.max_table_width = 18.0  # 最大表格宽度 (百分比)
        self.max_table_height = 15.0 # 最大表格高度 (百分比)
        self.margin = 2.0           # 表格间最小间距 (百分比)

    def calculate_table_positions(self, vertices: List[Point], intersection_points: List[Point] = None) -> List[TablePosition]:
        """
        根据顶点和交叉点计算表格位置

        Args:
            vertices: 图形顶点列表
            intersection_points: 图形内部交叉点列表

        Returns:
            表格位置列表（最多8个表格）
        """
        all_points = vertices.copy()

        # 添加交叉点
        if intersection_points:
            all_points.extend(intersection_points)

        # 限制表格数量不超过8个
        selected_points = self._select_optimal_vertices(all_points, max_count=8)

        positions = []

        # 为选定的点生成表格
        for i, point in enumerate(selected_points):
            # 随机生成表格规格 - 最小2行2列，确保有表头和数据
            rows = random.randint(2, 5)
            cols = random.randint(2, 4)

            # 根据表格大小计算尺寸
            base_width = self.min_table_width + (cols - 1) * 2.5
            base_height = self.min_table_height + (rows - 1) * 2.0

            width = min(base_width, self.max_table_width)
            height = min(base_height, self.max_table_height)

            # 调整位置避免超出画布边界
            adjusted_center = self._adjust_position_for_bounds(point, width, height)

            position = TablePosition(
                center=adjusted_center,
                width=width,
                height=height,
                rows=rows,
                cols=cols
            )
            positions.append(position)

        # 检测并解决重叠问题
        positions = self._resolve_overlaps(positions)

        return positions

    def _adjust_position_for_bounds(self, center: Point, width: float, height: float) -> Point:
        """调整位置确保表格不超出画布边界"""
        # 计算边界限制
        min_x = width / 2 + 1  # 左边界
        max_x = 100 - width / 2 - 1  # 右边界
        min_y = height / 2 + 1  # 上边界
        max_y = 100 - height / 2 - 1  # 下边界

        # 调整坐标
        adjusted_x = max(min_x, min(max_x, center.x))
        adjusted_y = max(min_y, min(max_y, center.y))

        return Point(adjusted_x, adjusted_y)

    def _resolve_overlaps(self, positions: List[TablePosition]) -> List[TablePosition]:
        """解决表格重叠问题"""
        if len(positions) <= 1:
            return positions

        resolved_positions = [positions[0]]  # 第一个位置保持不变

        for current_pos in positions[1:]:
            # 检查与已放置表格的重叠
            max_attempts = 10
            for attempt in range(max_attempts):
                overlaps = False

                for existing_pos in resolved_positions:
                    if self._check_overlap(current_pos, existing_pos):
                        overlaps = True
                        # 尝试调整位置
                        current_pos = self._adjust_position_to_avoid_overlap(current_pos, existing_pos)
                        break

                if not overlaps:
                    break

            resolved_positions.append(current_pos)

        return resolved_positions

    def _select_optimal_vertices(self, vertices: List[Point], max_count: int = 8) -> List[Point]:
        """
        从所有顶点中智能选择最优的位置放置表格

        Args:
            vertices: 所有顶点列表
            max_count: 最大选择数量

        Returns:
            选择的顶点列表
        """
        if len(vertices) <= max_count:
            return vertices

        # 策略1: 均匀分布选择
        if len(vertices) > max_count * 2:
            return self._select_evenly_distributed_vertices(vertices, max_count)

        # 策略2: 基于距离的选择（避免过度集中）
        return self._select_distance_based_vertices(vertices, max_count)

    def _select_evenly_distributed_vertices(self, vertices: List[Point], max_count: int) -> List[Point]:
        """均匀分布选择顶点"""
        if len(vertices) <= max_count:
            return vertices

        # 计算选择间隔
        step = len(vertices) / max_count
        selected_vertices = []

        for i in range(max_count):
            index = int(i * step)
            if index < len(vertices):
                selected_vertices.append(vertices[index])

        return selected_vertices

    def _select_distance_based_vertices(self, vertices: List[Point], max_count: int) -> List[Point]:
        """基于距离选择顶点，避免过度集中"""
        if len(vertices) <= max_count:
            return vertices

        selected_vertices = [vertices[0]]  # 从第一个顶点开始
        remaining_vertices = vertices[1:]

        while len(selected_vertices) < max_count and remaining_vertices:
            # 找到距离已选顶点最远的点
            best_vertex = None
            max_min_distance = 0

            for candidate in remaining_vertices:
                # 计算候选点到所有已选点的最小距离
                min_distance = min(
                    self._calculate_distance(candidate, selected)
                    for selected in selected_vertices
                )

                # 选择最小距离最大的点（最分散的点）
                if min_distance > max_min_distance:
                    max_min_distance = min_distance
                    best_vertex = candidate

            if best_vertex:
                selected_vertices.append(best_vertex)
                remaining_vertices.remove(best_vertex)
            else:
                break

        return selected_vertices

    def _calculate_distance(self, point1: Point, point2: Point) -> float:
        """计算两点之间的欧几里得距离"""
        return math.sqrt((point1.x - point2.x)**2 + (point1.y - point2.y)**2)

    def _check_overlap(self, pos1: TablePosition, pos2: TablePosition) -> bool:
        """检查两个表格是否重叠"""
        # 添加边距检查
        margin = self.margin

        return not (pos1.right + margin < pos2.left or
                   pos2.right + margin < pos1.left or
                   pos1.bottom + margin < pos2.top or
                   pos2.bottom + margin < pos1.top)

    def _adjust_position_to_avoid_overlap(self, current: TablePosition, existing: TablePosition) -> TablePosition:
        """调整位置以避免重叠"""
        # 计算移动方向
        dx = current.center.x - existing.center.x
        dy = current.center.y - existing.center.y

        # 如果位置太接近，随机选择一个方向
        if abs(dx) < 1 and abs(dy) < 1:
            angle = random.uniform(0, 2 * math.pi)
            dx = math.cos(angle)
            dy = math.sin(angle)

        # 标准化方向向量
        length = math.sqrt(dx * dx + dy * dy)
        if length > 0:
            dx /= length
            dy /= length

        # 计算需要移动的距离
        min_distance = (current.width + existing.width) / 2 + self.margin
        move_distance = min_distance + 2  # 额外间距

        # 计算新位置
        new_x = current.center.x + dx * move_distance
        new_y = current.center.y + dy * move_distance

        # 确保新位置在画布范围内
        new_center = self._adjust_position_for_bounds(
            Point(new_x, new_y),
            current.width,
            current.height
        )

        return TablePosition(
            center=new_center,
            width=current.width,
            height=current.height,
            rows=current.rows,
            cols=current.cols
        )


class StyleManager:
    """样式管理器"""

    def __init__(self):
        self.color_schemes = self._init_color_schemes()
        self.contrast_colors = self._init_contrast_colors()

    def _init_color_schemes(self) -> List[ColorScheme]:
        """初始化配色方案"""
        return [
            ColorScheme(
                name="清新蓝绿",
                primary="#E3F2FD",
                secondary="#B3E5FC",
                accent="#4FC3F7",
                background="#F8F9FA",
                text="#212529"
            ),
            ColorScheme(
                name="温暖橙黄",
                primary="#FFF3E0",
                secondary="#FFE0B2",
                accent="#FFB74D",
                background="#FAFAFA",
                text="#424242"
            ),
            ColorScheme(
                name="优雅紫色",
                primary="#F3E5F5",
                secondary="#E1BEE7",
                accent="#BA68C8",
                background="#F9F9F9",
                text="#37474F"
            ),
            ColorScheme(
                name="自然绿色",
                primary="#E8F5E8",
                secondary="#C8E6C9",
                accent="#81C784",
                background="#F1F8E9",
                text="#2E7D32"
            ),
            ColorScheme(
                name="经典灰色",
                primary="#F5F5F5",
                secondary="#EEEEEE",
                accent="#9E9E9E",
                background="#FAFAFA",
                text="#424242"
            )
        ]

    def _init_contrast_colors(self) -> Dict[str, List[str]]:
        """初始化每个配色方案对应的高对比度颜色"""
        return {
            "清新蓝绿": ["#FFF9C4", "#F3E5F5", "#E8F5E8"],  # 淡黄色、淡紫色、淡绿色
            "温暖橙黄": ["#E8F5E8", "#E3F2FD", "#F3E5F5"],  # 淡绿色、淡蓝色、淡紫色
            "优雅紫色": ["#FFF9C4", "#E8F5E8", "#E3F2FD"],  # 淡黄色、淡绿色、淡蓝色
            "自然绿色": ["#FFF9C4", "#F3E5F5", "#E3F2FD"],  # 淡黄色、淡紫色、淡蓝色
            "经典灰色": ["#E8F5E8", "#E3F2FD", "#FFF9C4"]   # 淡绿色、淡蓝色、淡黄色
        }

    def get_random_color_scheme(self) -> ColorScheme:
        """获取随机配色方案"""
        return random.choice(self.color_schemes)

    def generate_cell_colors(self, rows: int, cols: int, scheme: ColorScheme) -> List[List[str]]:
        """生成单元格颜色矩阵，包含高对比度颜色"""
        cell_colors = []
        total_cells = rows * cols

        # 获取对应配色方案的高对比度颜色
        contrast_colors = self.contrast_colors.get(scheme.name, [])

        # 计算需要添加的高对比度单元格数量（2-3个）
        contrast_count = min(random.randint(2, 3), total_cells // 2)
        contrast_positions = set()

        # 随机选择高对比度单元格位置
        while len(contrast_positions) < contrast_count:
            row = random.randint(0, rows - 1)
            col = random.randint(0, cols - 1)
            contrast_positions.add((row, col))

        for i in range(rows):
            row_colors = []
            for j in range(cols):
                if (i, j) in contrast_positions and contrast_colors:
                    # 使用高对比度颜色
                    color = random.choice(contrast_colors)
                else:
                    # 使用原有的颜色选择逻辑
                    if random.random() < 0.3:  # 30%概率使用强调色
                        color = scheme.accent
                    elif random.random() < 0.5:  # 50%概率使用次要色
                        color = scheme.secondary
                    else:  # 其余使用主色
                        color = scheme.primary
                row_colors.append(color)
            cell_colors.append(row_colors)

        return cell_colors

    def generate_cell_merges(self, rows: int, cols: int) -> List[Dict[str, Any]]:
        """生成单元格合并规则"""
        merges = []

        # 随机决定是否进行合并
        if random.random() < 0.4:  # 40%概率进行合并
            merge_type = random.choice(['horizontal', 'vertical', 'l_shape'])

            if merge_type == 'horizontal' and cols > 1:
                # 水平合并
                row = random.randint(0, rows - 1)
                start_col = random.randint(0, cols - 2)
                span = random.randint(2, min(cols - start_col, 3))
                merges.append({
                    'type': 'horizontal',
                    'row': row,
                    'col': start_col,
                    'colspan': span,
                    'rowspan': 1
                })

            elif merge_type == 'vertical' and rows > 1:
                # 垂直合并
                col = random.randint(0, cols - 1)
                start_row = random.randint(0, rows - 2)
                span = random.randint(2, min(rows - start_row, 3))
                merges.append({
                    'type': 'vertical',
                    'row': start_row,
                    'col': col,
                    'colspan': 1,
                    'rowspan': span
                })

            elif merge_type == 'l_shape' and rows > 1 and cols > 1:
                # L型合并（简化版）
                start_row = random.randint(0, rows - 2)
                start_col = random.randint(0, cols - 2)
                merges.append({
                    'type': 'l_shape',
                    'row': start_row,
                    'col': start_col,
                    'colspan': 2,
                    'rowspan': 2
                })

        return merges


class TableGenerator:
    """表格生成器"""

    def __init__(self):
        self.geometry_generator = GeometryGenerator()
        self.layout_manager = LayoutManager()
        self.style_manager = StyleManager()
        self.data_generator = DataGenerator()

    def generate_table_html(self, position: TablePosition, scheme: ColorScheme,
                          table_id: str = "") -> str:
        """生成单个表格的HTML"""

        # 生成表格数据
        table_data = self.data_generator.generate_table_data(position.rows, position.cols)

        # 生成单元格颜色和合并规则
        cell_colors = self.style_manager.generate_cell_colors(
            position.rows, position.cols, scheme
        )
        merges = self.style_manager.generate_cell_merges(
            position.rows, position.cols
        )

        # 创建合并映射
        merge_map = {}
        skip_cells = set()

        for merge in merges:
            row, col = merge['row'], merge['col']
            rowspan, colspan = merge['rowspan'], merge['colspan']
            merge_map[(row, col)] = (rowspan, colspan)

            # 标记需要跳过的单元格
            for r in range(row, row + rowspan):
                for c in range(col, col + colspan):
                    if r != row or c != col:  # 不跳过起始单元格
                        skip_cells.add((r, c))

        # 生成表格HTML
        table_html = f"""
        <div class="table-container" style="
            position: absolute;
            left: {position.left}%;
            top: {position.top}%;
            width: {position.width}%;
            height: {position.height}%;
        ">
            <table class="data-table" id="{table_id}">
        """

        for row in range(position.rows):
            table_html += "<tr>"
            for col in range(position.cols):
                if (row, col) in skip_cells:
                    continue

                # 检查是否有合并
                rowspan, colspan = merge_map.get((row, col), (1, 1))
                color = cell_colors[row][col]

                # 获取单元格数据
                cell_data = "&nbsp;"
                if row < len(table_data) and col < len(table_data[row]):
                    cell_data = table_data[row][col]

                # 为表头行添加特殊样式
                header_style = ""
                if row == 0:
                    header_style = "font-weight: bold;"

                table_html += f"""
                <td style="background-color: {color}; color: {scheme.text}; {header_style}"
                    rowspan="{rowspan}" colspan="{colspan}">
                    {cell_data}
                </td>
                """
            table_html += "</tr>"

        table_html += """
            </table>
        </div>
        """

        return table_html

    def generate_complete_layout(self, shape_type: ShapeType,
                               canvas_width: int = 1600, canvas_height: int = 1000) -> Dict[str, Any]:
        """
        生成完整的布局数据

        Args:
            shape_type: 几何图形类型
            canvas_width: 画布宽度
            canvas_height: 画布高度

        Returns:
            包含所有布局信息的字典
        """
        # 更新生成器尺寸
        self.geometry_generator.canvas_width = canvas_width
        self.geometry_generator.canvas_height = canvas_height
        self.layout_manager.canvas_width = canvas_width
        self.layout_manager.canvas_height = canvas_height

        # 生成几何图形顶点
        vertices = self.geometry_generator.generate_vertices(shape_type)

        # 计算图形内部交叉点
        intersection_points = self.geometry_generator.calculate_intersection_points(shape_type, vertices)

        # 计算表格位置（包含顶点和交叉点）
        table_positions = self.layout_manager.calculate_table_positions(vertices, intersection_points)

        # 选择配色方案
        color_scheme = self.style_manager.get_random_color_scheme()

        # 生成SVG路径
        svg_path = self.geometry_generator.get_shape_svg_path(shape_type, vertices)

        return {
            'shape_type': shape_type,
            'vertices': vertices,
            'intersection_points': intersection_points,
            'table_positions': table_positions,
            'color_scheme': color_scheme,
            'svg_path': svg_path,
            'canvas_width': canvas_width,
            'canvas_height': canvas_height
        }

    def generate_html_document(self, layout_data: Dict[str, Any]) -> str:
        """生成完整的HTML文档"""

        # 提取布局数据
        shape_type = layout_data['shape_type']
        vertices = layout_data['vertices']
        intersection_points = layout_data['intersection_points']
        table_positions = layout_data['table_positions']
        color_scheme = layout_data['color_scheme']
        svg_path = layout_data['svg_path']
        canvas_width = layout_data['canvas_width']
        canvas_height = layout_data['canvas_height']

        # 生成所有表格HTML
        tables_html = ""
        for i, position in enumerate(table_positions):
            table_html = self.generate_table_html(position, color_scheme, f"table_{i}")
            tables_html += table_html



        # 构建完整HTML文档
        html_document = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多样化卡片线表格 - {shape_type.value}</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: {color_scheme.background};
            color: {color_scheme.text};
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }}

        .canvas-container {{
            position: relative;
            width: {canvas_width}px;
            height: {canvas_height}px;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }}

        .shape-outline {{
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }}

        .table-container {{
            z-index: 10;
            border: 1px solid #ccc;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}

        .data-table {{
            width: 100%;
            height: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }}

        .data-table td {{
            border: 1px solid #ddd;
            text-align: center;
            vertical-align: middle;
            padding: 4px;
            font-size: 12px;
            min-height: 20px;
        }}


    </style>
</head>
<body>
    <div class="canvas-container" id="capture">
        <!-- 图形轮廓 -->
        <svg class="shape-outline" viewBox="0 0 100 100" preserveAspectRatio="none">
            <path d="{svg_path}"
                  fill="none"
                  stroke="{color_scheme.accent}"
                  stroke-width="0.2"
                  stroke-dasharray="1,1"/>
        </svg>

        <!-- 表格 -->
        {tables_html}


    </div>
</body>
</html>"""

        return html_document


def generate_multi_shape_table(shape_type: ShapeType = None, output_filename: str = None,
                              canvas_width: int = 1600, canvas_height: int = 1000) -> bool:
    """
    生成多样化卡片线表格图片

    Args:
        shape_type: 几何图形类型（如果为None则随机选择）
        output_filename: 输出文件名（如果为None则自动生成）
        canvas_width: 画布宽度
        canvas_height: 画布高度

    Returns:
        是否生成成功
    """
    try:
        # 确保输出目录存在
        if not os.path.exists(OUTPUT_FOLDER):
            os.makedirs(OUTPUT_FOLDER)

        # 如果没有指定图形类型，随机选择
        if shape_type is None:
            shape_type = random.choice(list(ShapeType))

        # 如果没有指定文件名，自动生成
        if output_filename is None:
            output_filename = get_next_filename()

        # 创建表格生成器
        generator = TableGenerator()

        # 生成布局数据
        layout_data = generator.generate_complete_layout(shape_type, canvas_width, canvas_height)

        # 生成HTML文档
        html_content = generator.generate_html_document(layout_data)

        # 生成图片
        output_path = os.path.join(OUTPUT_FOLDER, output_filename)
        options = {
            'width': canvas_width + 40,
            'height': canvas_height + 40,
            'encoding': "UTF-8",
            'quiet': ''
        }

        if config:
            imgkit.from_string(html_content, output_path, options=options, config=config)
        else:
            imgkit.from_string(html_content, output_path, options=options)

        print(f"多样化表格已保存到: {output_path}")
        print(f"图形类型: {shape_type.value}")
        print(f"顶点数量: {len(layout_data['vertices'])}")
        print(f"表格数量: {len(layout_data['table_positions'])}")
        print(f"配色方案: {layout_data['color_scheme'].name}")

        return True

    except Exception as e:
        print(f"生成多样化表格时出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def generate_single_table_worker(args):
    """单个表格生成的工作函数（用于多进程）"""
    canvas_width, canvas_height = args
    return generate_multi_shape_table(canvas_width=canvas_width, canvas_height=canvas_height)


def batch_generate_tables(count: int, canvas_width: int = 1600, canvas_height: int = 1000,
                         max_workers: int = None) -> int:
    """
    批量生成表格

    Args:
        count: 生成数量
        canvas_width: 画布宽度
        canvas_height: 画布高度
        max_workers: 最大并发数（默认为CPU核心数）

    Returns:
        成功生成的数量
    """
    if max_workers is None:
        max_workers = multiprocessing.cpu_count()

    print(f"开始批量生成 {count} 张表格...")
    print(f"使用 {max_workers} 个并发进程")

    # 重置文件计数器
    reset_file_counter()

    success_count = 0

    # 准备参数
    args_list = [(canvas_width, canvas_height) for _ in range(count)]

    # 使用线程池执行（imgkit在多进程下可能有问题，使用线程池更安全）
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        results = list(executor.map(generate_single_table_worker, args_list))
        success_count = sum(1 for result in results if result)

    print(f"\n批量生成完成: {success_count}/{count} 成功")
    return success_count











if __name__ == "__main__":
    print("=" * 60)
    print("多样化卡片线表格生成器")
    print("=" * 60)

    try:
        # 获取用户输入的生成数量
        count_input = input("\n请输入要生成的表格数量 (默认10): ").strip()
        if count_input:
            count = int(count_input)
        else:
            count = 10

        # 获取并发数
        workers_input = input(f"请输入并发数量 (默认{multiprocessing.cpu_count()}): ").strip()
        if workers_input:
            max_workers = int(workers_input)
        else:
            max_workers = multiprocessing.cpu_count()

        # 开始批量生成
        success_count = batch_generate_tables(count, max_workers=max_workers)

        print(f"\n生成完成！成功生成 {success_count} 张表格")

    except KeyboardInterrupt:
        print("\n\n用户中断，程序退出")
    except ValueError:
        print("\n输入无效，使用默认设置生成10张表格")
        batch_generate_tables(10)
    except Exception as e:
        print(f"\n发生错误: {e}")
        print("使用默认设置生成5张表格")
        batch_generate_tables(5)

