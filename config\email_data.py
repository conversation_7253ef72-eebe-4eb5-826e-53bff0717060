email_data = {
    "display_name": "电子邮件",
    "table_title_template": "电子邮件数据表",
    "columns": {
        "邮件ID": {
            "generator_type": "alphanumeric_pattern",
            "params": {
                "patterns": [
                    "MAIL-####-####",
                    "MSG######",
                    "EM########",
                    "EMAIL######"
                ]
            },
            "data_category": "text"
        },
        "发件人": {
            "generator_type": "faker_email",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "发件人姓名": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "收件人": {
            "generator_type": "faker_email",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "收件人姓名": {
            "generator_type": "faker_name",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "抄送人": {
            "generator_type": "faker_email",
            "params": {
                "locale": "zh_CN"
            },
            "data_category": "text"
        },
        "主题": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "会议通知：周例会安排",
                    "项目进度更新",
                    "关于预算调整的讨论",
                    "新产品发布计划",
                    "请假申请",
                    "客户反馈汇总",
                    "系统维护通知",
                    "年度报告提交",
                    "培训课程安排",
                    "节日祝福",
                    "合同审批",
                    "账单通知",
                    "密码重置",
                    "账户登录验证",
                    "服务器异常警报",
                    "招聘面试安排",
                    "市场调研报告",
                    "销售数据分析",
                    "产品更新说明",
                    "技术支持请求"
                ]
            },
            "data_category": "text"
        },
        "发送时间": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2023,
                "end_year": 2023,
                "format": "%Y-%m-%d %H:%M:%S"
            },
            "data_category": "date"
        },
        "状态": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "已发送",
                    "已读",
                    "未读",
                    "草稿",
                    "已回复",
                    "已转发",
                    "已删除",
                    "垃圾邮件",
                    "收件箱",
                    "已存档",
                    "已标记",
                    "待处理",
                    "重要",
                    "发送失败"
                ]
            },
            "data_category": "text"
        },
        "优先级": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "高",
                    "中",
                    "低",
                    "普通",
                    "紧急"
                ]
            },
            "data_category": "text"
        },
        "邮件大小": {
            "generator_type": "numerical_range_formatted",
            "params": {
                "min_value": 1,
                "max_value": 25000,
                "format_string": "{:,}KB"
            },
            "data_category": "numeric"
        },
        "是否有附件": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "是",
                    "否"
                ]
            },
            "data_category": "text"
        },
        "附件数量": {
            "generator_type": "integer_range",
            "params": {
                "min": 0,
                "max": 10
            },
            "data_category": "numeric"
        },
        "邮件标签": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "工作",
                    "个人",
                    "重要",
                    "待办",
                    "已完成",
                    "跟进",
                    "项目",
                    "会议",
                    "客户",
                    "财务",
                    "合同",
                    "报告",
                    "产品",
                    "技术",
                    "管理",
                    "未分类"
                ]
            },
            "data_category": "text"
        },
        "是否已回复": {
            "generator_type": "categorical",
            "params": {
                "values": [
                    "是",
                    "否"
                ]
            },
            "data_category": "text"
        },
        "回复时间": {
            "generator_type": "date_range",
            "params": {
                "start_year": 2023,
                "end_year": 2023,
                "format": "%Y-%m-%d %H:%M:%S"
            },
            "data_category": "date"
        }
    },
    "text_columns_count": 7,
    "text_columns_names": [
        "发件人姓名",
        "收件人姓名",
        "状态",
        "优先级",
        "是否有附件",
        "邮件标签",
        "是否已回复"
    ],
    "numeric_columns_count": 3,
    "numeric_columns_names": [
        "邮件ID",
        "邮件大小",
        "附件数量"
    ],
    "date_columns_count": 2,
    "date_columns_names": [
        "发送时间",
        "回复时间"
    ],
    "other_columns_count": 1,
    "other_columns_names": [
        "主题"
    ],
    "all_columns": [
        "发件人",
        "发件人姓名",
        "收件人",
        "收件人姓名",
        "抄送人",
        "状态",
        "优先级",
        "是否有附件",
        "邮件标签",
        "是否已回复",
        "邮件ID",
        "邮件大小",
        "附件数量",
        "发送时间",
        "回复时间",
        "主题"
    ]
}

